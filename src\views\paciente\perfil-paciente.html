<!DOCTYPE html>
<html lang="pt-BR">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" href="../../../public/css/global.css" />
    <title><PERSON>u <PERSON>l</title>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&display=swap"
      rel="stylesheet"
    />
    <!-- Adicionar o script de verificação de autenticação -->
    <script src="../../../public/js/auth-check.js"></script>
    <style>
      .profile-header {
        background: white;
        border-radius: 12px;
        padding: 30px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
        text-align: center;
      }
      .profile-avatar {
        width: 120px;
        height: 120px;
        border-radius: 50%;
        background-color: #013c6d;
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 48px;
        margin: 0 auto 16px;
      }
      .profile-name {
        font-size: 24px;
        font-weight: 600;
        color: #2d3748;
        margin-bottom: 8px;
      }
      .profile-info {
        color: #718096;
        margin-bottom: 16px;
      }
      .profile-status {
        display: inline-block;
        padding: 6px 16px;
        background-color: #c6f6d5;
        color: #38a169;
        border-radius: 20px;
        font-size: 14px;
        font-weight: 500;
      }
      .info-section {
        background: white;
        border-radius: 12px;
        padding: 20px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
      }
      .section-title {
        font-size: 18px;
        font-weight: 600;
        color: #2d3748;
        margin-bottom: 16px;
        display: flex;
        align-items: center;
        gap: 10px;
      }
      .info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 16px;
      }
      .info-item {
        display: flex;
        flex-direction: column;
      }
      .info-label {
        font-size: 12px;
        color: #718096;
        margin-bottom: 4px;
        text-transform: uppercase;
        font-weight: 500;
      }
      .info-value {
        font-size: 16px;
        color: #2d3748;
        font-weight: 500;
      }
      .edit-button {
        background-color: #f8fafc;
        border: 1px solid #e2e8f0;
        color: #4a5568;
        padding: 8px 16px;
        border-radius: 6px;
        font-size: 14px;
        cursor: pointer;
        transition: all 0.2s;
      }
      .edit-button:hover {
        background-color: #e2e8f0;
      }
      .medical-info {
        background: #f8fafc;
        border-radius: 8px;
        padding: 16px;
        margin-top: 16px;
      }
      .medical-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 0;
        border-bottom: 1px solid #e2e8f0;
      }
      .medical-item:last-child {
        border-bottom: none;
      }
      .medical-label {
        font-weight: 500;
        color: #2d3748;
      }
      .medical-value {
        color: #4a5568;
      }
      .preferences-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 16px;
        margin-top: 16px;
      }
      .preference-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 12px;
        background-color: #f8fafc;
        border-radius: 8px;
      }
      .toggle-switch {
        position: relative;
        width: 44px;
        height: 24px;
        background-color: #e2e8f0;
        border-radius: 12px;
        cursor: pointer;
        transition: background-color 0.2s;
      }
      .toggle-switch.active {
        background-color: #013c6d;
      }
      .toggle-switch::after {
        content: "";
        position: absolute;
        width: 20px;
        height: 20px;
        background-color: white;
        border-radius: 50%;
        top: 2px;
        left: 2px;
        transition: transform 0.2s;
      }
      .toggle-switch.active::after {
        transform: translateX(20px);
      }

      /* Estilos para o grid de feridas */
      .wounds-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 16px;
        margin-top: 16px;
      }

      .wound-card {
        background: white;
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        transition: transform 0.2s;
        border: 1px solid #edf2f7;
        height: 100%;
        display: flex;
        flex-direction: column;
      }

      .wound-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }

      .wound-img-container {
        height: 160px;
        background-color: #f7fafc;
        overflow: hidden;
        position: relative;
      }

      .wound-img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .wound-placeholder {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
        color: #a0aec0;
        font-size: 36px;
      }

      .wound-content {
        padding: 16px;
        flex: 1;
        display: flex;
        flex-direction: column;
      }

      .wound-title {
        font-weight: 600;
        margin-bottom: 8px;
        color: #2d3748;
      }

      .wound-location {
        color: #718096;
        font-size: 14px;
        margin-bottom: 12px;
      }

      .wound-status-badge {
        display: inline-block;
        padding: 4px 10px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 500;
        margin-bottom: 8px;
      }

      .status-active {
        background-color: #fed7d7;
        color: #e53e3e;
      }

      .status-healing {
        background-color: #fefcbf;
        color: #d69e2e;
      }

      .status-healed {
        background-color: #c6f6d5;
        color: #38a169;
      }

      .wound-footer {
        margin-top: auto;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-top: 12px;
      }

      .wound-date {
        font-size: 12px;
        color: #718096;
      }

      .wound-action {
        font-size: 13px;
        color: #4299e1;
        cursor: pointer;
        background: none;
        border: none;
        padding: 6px 12px;
        border-radius: 4px;
        transition: background-color 0.2s;
      }

      .wound-action:hover {
        background-color: #ebf8ff;
        text-decoration: underline;
      }

      /* Estilos para a visualização em carrossel em dispositivos móveis */
      @media (max-width: 768px) {
        .wounds-carousel {
          display: flex;
          overflow-x: auto;
          scroll-snap-type: x mandatory;
          padding: 8px 0;
          margin: 0 -8px;
        }

        .wounds-carousel .wound-card {
          flex: 0 0 auto;
          width: 250px;
          margin: 0 8px;
          scroll-snap-align: start;
        }
      }

      /* Estilos para o modal de detalhes */
      .wound-detail-modal {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 1000;
        overflow-y: auto;
      }

      .modal-content {
        background: white;
        width: 90%;
        max-width: 800px;
        margin: 40px auto;
        border-radius: 12px;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        position: relative;
        animation: modalFadeIn 0.3s;
      }

      @keyframes modalFadeIn {
        from {
          opacity: 0;
          transform: translateY(-20px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      .modal-header {
        padding: 20px 24px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 1px solid #edf2f7;
      }

      .modal-title {
        font-size: 18px;
        font-weight: 600;
        color: #2d3748;
      }

      .modal-close {
        background: none;
        border: none;
        font-size: 24px;
        color: #a0aec0;
        cursor: pointer;
      }

      .modal-body {
        padding: 24px;
      }

      .detail-columns {
        display: flex;
        gap: 24px;
      }

      @media (max-width: 768px) {
        .detail-columns {
          flex-direction: column;
        }
      }

      .detail-column {
        flex: 1;
      }

      .detail-photo {
        width: 100%;
        border-radius: 8px;
        margin-bottom: 16px;
      }

      .detail-section {
        margin-bottom: 20px;
      }

      .detail-heading {
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 12px;
        color: #4a5568;
      }

      .info-row {
        display: flex;
        padding: 8px 0;
        border-bottom: 1px solid #edf2f7;
      }

      .info-label {
        width: 140px;
        color: #718096;
        font-size: 14px;
      }

      .info-value {
        flex: 1;
        color: #2d3748;
        font-weight: 500;
        font-size: 14px;
      }

      .symptoms-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 10px;
        margin-top: 10px;
      }

      .symptom-item {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 8px;
        background-color: #f7fafc;
        border-radius: 6px;
      }

      .symptom-icon {
        width: 18px;
        height: 18px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 10px;
        font-weight: bold;
        color: white;
      }

      .icon-yes {
        background-color: #e53e3e;
      }

      .icon-no {
        background-color: #38a169;
      }

      .symptom-text {
        font-size: 14px;
      }

      .observations-box {
        background-color: #f7fafc;
        border-radius: 8px;
        padding: 12px;
        margin-top: 10px;
        font-size: 14px;
        color: #4a5568;
      }
    </style>
  </head>
  <body>
    <div class="desktop-frame">
      <div class="sidebar">
        <div class="sidebar-logo">
          <img
            src="../../../public/images/logo/cicatriza_semnada.png"
            alt="Logo"
            class="logo-small"
          />
          <h3 class="sidebar-title">Cicatriza+</h3>
        </div>
        <ul class="sidebar-menu">
          <a href="menu-principal.html">
            <li class="sidebar-item">
              <span class="sidebar-icon">🏠</span>Início
            </li>
          </a>
          <a href="tutorial-paciente.html">
            <li class="sidebar-item">
              <span class="sidebar-icon">📝</span>Tutorial
            </li>
          </a>
          <a href="enviar-foto-paciente.html">
            <li class="sidebar-item">
              <span class="sidebar-icon">📷</span>Enviar foto
            </li>
          </a>
          <a href="videos-paciente.html">
            <li class="sidebar-item">
              <span class="sidebar-icon">🎥</span>Vídeos
            </li>
          </a>
          <a href="perfil-paciente.html">
            <li class="sidebar-item">
              <span class="sidebar-icon">👤</span>Perfil
            </li>
          </a>
          <a href="notificacoes-paciente.html">
            <li class="sidebar-item">
              <span class="sidebar-icon">🔔</span>Notificações
            </li>
          </a>
          <a href="emergencia.html">
            <li class="sidebar-item">
              <span class="sidebar-icon">⚠️</span>Emergência
            </li>
          </a>
        </ul>
      </div>
      <div class="desktop-content">
        <div class="desktop-header">
          <div class="header-title">Meu Perfil</div>
        </div>
        <div class="main-area">
          <div class="profile-header">
            <div class="profile-avatar">👤</div>
            <div class="profile-name" id="profile-name"></div>
            <div class="profile-info" id="profile-registration-date"></div>
            <div class="profile-status" id="profile-status"></div>
          </div>

          <div class="info-section">
            <div class="section-title">
              📋 Informações Pessoais
              <button class="edit-button" style="margin-left: auto">
                Editar
              </button>
            </div>
            <div class="info-grid">
              <div class="info-item">
                <div class="info-label">Nome Completo</div>
                <div class="info-value" id="profile-fullname"></div>
              </div>
              <div class="info-item">
                <div class="info-label">CPF</div>
                <div class="info-value" id="profile-cpf"></div>
              </div>
              <div class="info-item">
                <div class="info-label">Data de Nascimento</div>
                <div class="info-value" id="profile-dob"></div>
              </div>
              <div class="info-item">
                <div class="info-label">Idade</div>
                <div class="info-value" id="profile-age"></div>
              </div>
              <div class="info-item">
                <div class="info-label">Telefone</div>
                <div class="info-value" id="profile-phone"></div>
              </div>
              <div class="info-item">
                <div class="info-label">E-mail</div>
                <div class="info-value" id="profile-email"></div>
              </div>
            </div>
          </div>

          <div class="info-section">
            <div class="section-title">
              🏠 Endereço
              <button class="edit-button" style="margin-left: auto">
                Editar
              </button>
            </div>
            <div class="info-grid">
              <div class="info-item">
                <div class="info-label">CEP</div>
                <div class="info-value" id="profile-cep"></div>
              </div>
              <div class="info-item">
                <div class="info-label">Endereço</div>
                <div class="info-value" id="profile-address"></div>
              </div>
              <div class="info-item">
                <div class="info-label">Bairro</div>
                <div class="info-value" id="profile-neighborhood"></div>
              </div>
              <div class="info-item">
                <div class="info-label">Cidade</div>
                <div class="info-value" id="profile-city"></div>
              </div>
            </div>
          </div>

          <div class="info-section">
            <div class="section-title">
              🏥 Informações Médicas
              <button
                class="edit-button"
                style="margin-left: auto"
                onclick="location.href='enviar-foto-paciente.html'"
              >
                Registrar Nova Ferida
              </button>
            </div>
            <div id="wounds-container">
              <p>Carregando informações médicas...</p>
            </div>
          </div>

          <div class="info-section">
            <div class="section-title">⚕️ Condições de Saúde</div>
            <div class="medical-info" id="comorbidities-container">
              <p>Carregando condições de saúde...</p>
            </div>
          </div>

          <div class="info-section">
            <div class="section-title">🔔 Preferências de Notificação</div>
            <div class="preferences-grid">
              <div class="preference-item">
                <span>Lembretes de medicação</span>
                <div
                  class="toggle-switch active"
                  onclick="toggleSwitch(this)"
                ></div>
              </div>
              <div class="preference-item">
                <span>Lembretes de fotos</span>
                <div
                  class="toggle-switch active"
                  onclick="toggleSwitch(this)"
                ></div>
              </div>
              <div class="preference-item">
                <span>Confirmação de consultas</span>
                <div
                  class="toggle-switch active"
                  onclick="toggleSwitch(this)"
                ></div>
              </div>
              <div class="preference-item">
                <span>Mensagens do profissional</span>
                <div
                  class="toggle-switch active"
                  onclick="toggleSwitch(this)"
                ></div>
              </div>
              <div class="preference-item">
                <span>Relatórios semanais</span>
                <div class="toggle-switch" onclick="toggleSwitch(this)"></div>
              </div>
              <div class="preference-item">
                <span>Notificações por e-mail</span>
                <div
                  class="toggle-switch active"
                  onclick="toggleSwitch(this)"
                ></div>
              </div>
            </div>
          </div>

          <div class="info-section">
            <div class="section-title">🔐 Segurança</div>
            <div style="display: flex; gap: 12px; flex-wrap: wrap">
              <button class="button outline">Alterar Senha</button>
              <button class="button outline">
                Configurar Autenticação em Duas Etapas
              </button>
              <button class="button outline">Baixar Meus Dados</button>
              <button
                class="button outline logout-button"
                style="color: #e53e3e; border-color: #e53e3e"
              >
                Sair da Conta
              </button>
            </div>
          </div>
        </div>

        <div class="footer-desktop">
          <div class="footer-left">
            Menu - início - Perfil - Login - Contate - nos
          </div>
          <div class="footer-right">Cicatriza+</div>
        </div>
      </div>
    </div>

    <div class="mobile-navbar">
      <div class="mobile-nav-container">
        <a href="menu-principal.html" class="mobile-nav-item active">
          <div class="mobile-nav-icon">🏠</div>
          <div class="mobile-nav-text">Início</div>
        </a>
        <a href="consultas-paciente.html" class="mobile-nav-item">
          <div class="mobile-nav-icon">📋</div>
          <div class="mobile-nav-text">Consultas</div>
        </a>
        <a href="saude-paciente.html" class="mobile-nav-item">
          <div class="mobile-nav-icon">📊</div>
          <div class="mobile-nav-text">Saúde</div>
        </a>
        <a href="perfil-paciente.html" class="mobile-nav-item">
          <div class="mobile-nav-icon">👤</div>
          <div class="mobile-nav-text">Perfil</div>
        </a>
      </div>
    </div>

    <!-- Modal para detalhes da ferida -->
    <div id="wound-detail-modal" class="wound-detail-modal">
      <div class="modal-content">
        <div class="modal-header">
          <div class="modal-title">Detalhes da Ferida</div>
          <button class="modal-close" onclick="closeWoundModal()">
            &times;
          </button>
        </div>
        <div class="modal-body" id="wound-detail-content">
          <!-- Conteúdo será preenchido via JavaScript -->
        </div>
      </div>
    </div>

    <script>
      
      function getUserFromCookie() {
        console.log("👤 Buscando cookie do usuário...");
        const cookies = document.cookie.split("; ");
        console.log("Cookies disponíveis:", cookies);

        const match = cookies.find((row) => row.startsWith("user="));
        console.log("Match encontrado:", match);

        if (!match) {
          console.error("❌ Cookie 'user' não encontrado!");
          return null;
        }

        try {
          const userData = JSON.parse(decodeURIComponent(match.split("=")[1]));
          console.log("✅ Dados do usuário recuperados:", userData);
          return userData;
        } catch (e) {
          console.error("❌ Erro ao processar cookie:", e);
          return null;
        }
      }

      function calculateAge(dob) {
        const diff = Date.now() - new Date(dob);
        return Math.floor(new Date(diff).getUTCFullYear() - 1970);
      }

      
      function formatDate(dateString) {
        if (!dateString) return "N/A";
        return new Date(dateString).toLocaleDateString("pt-BR");
      }

      
      function getStatusBadge(status) {
        const statusColors = {
          ativa: "color: #e53e3e;",
          "em cicatrização": "color: #d69e2e;",
          cicatrizada: "color: #38a169;",
        };

        return `<span style="${
          statusColors[status] || ""
        }; font-weight: 500;">${status || "Não informado"}</span>`;
      }

      
      function getSeverityText(level) {
        const severity = {
          1: "Baixa",
          2: "Leve",
          3: "Moderada",
          4: "Alta",
          5: "Grave",
        };

        return severity[level] || "Não informado";
      }

      
      function toggleSwitch(element) {
        element.classList.toggle("active");
      }

      
      function openWoundModal(wound) {
        const modal = document.getElementById("wound-detail-modal");
        const content = document.getElementById("wound-detail-content");

        
        content.innerHTML = `
          <div class="detail-columns">
            <div class="detail-column">
              <div class="detail-section">
                <div class="detail-heading">Tipo de Ferida</div>
                <div class="info-value">${wound.wound_type}</div>
              </div>
              
              <div class="detail-section">
                <div class="detail-heading">Localização</div>
                <div class="info-value">${wound.anatomical_location}</div>
              </div>
              
              <div class="detail-section">
                <div class="detail-heading">Data de Início</div>
                <div class="info-value">${formatDate(
                  wound.first_occurrence_date
                )}</div>
              </div>
              
              <div class="detail-section">
                <div class="detail-heading">Gravidade</div>
                <div class="info-value">${getSeverityText(
                  wound.severity_level
                )}</div>
              </div>
              
              <div class="detail-section">
                <div class="detail-heading">Status</div>
                <div class="info-value">${getStatusBadge(wound.status)}</div>
              </div>
            </div>
          </div>
        `;

        
        modal.style.display = "block";
      }

      
      function closeWoundModal() {
        const modal = document.getElementById("wound-detail-modal");
        modal.style.display = "none";
      }

      
      window.onclick = function (event) {
        const modal = document.getElementById("wound-detail-modal");
        if (event.target == modal) {
          closeWoundModal();
        }
      };

      async function loadWounds(patientId) {
        try {
          console.log("🔍 Carregando feridas para o paciente ID:", patientId);

          
          if (!patientId) {
            console.error("ID do paciente não especificado");
            throw new Error("ID do paciente não especificado");
          }

          
          

          const response = await fetch(`/api/wounds?patient_id=${patientId}`, {
            credentials: "include", 
            headers: {
              "Cache-Control": "no-cache",
              Pragma: "no-cache",
            },
          });

          if (!response.ok) {
            console.error(
              "❌ Erro na resposta da API:",
              response.status,
              response.statusText
            );

            
            let errorDetail = "Falha ao carregar dados de feridas";
            try {
              const errorData = await response.json();
              errorDetail = errorData.error || errorDetail;
              console.error("Detalhe do erro:", errorData);
            } catch (e) {}

            
            if (response.status === 401) {
              console.log(
                "⚠️ Erro de autenticação ao carregar feridas - ignorando temporariamente para desenvolvimento"
              );
              
              
              

              
              return [];
            }

            throw new Error(errorDetail);
          }

          const wounds = await response.json();
          console.log("✅ Feridas carregadas:", wounds);
          return wounds;
        } catch (error) {
          console.error("❌ Erro ao carregar feridas:", error);
          document.getElementById(
            "wounds-container"
          ).innerHTML = `<p style="color: #e53e3e;">
              Erro ao carregar informações médicas: ${
                error.message || "Falha na requisição"
              }. 
              <button onclick="window.location.reload()" 
                      style="background: #e53e3e; color: white; border: none; padding: 5px 10px; border-radius: 4px; cursor: pointer;">
                Tentar novamente
              </button>
            </p>`;
          
          return [];
        }
      }

      async function loadComorbidities(patientId) {
        try {
          console.log(
            "🔍 Carregando comorbidades para o paciente ID:",
            patientId
          );

          
          if (!patientId) {
            console.error("ID do paciente não especificado");
            throw new Error("ID do paciente não especificado");
          }

          

          const response = await fetch(
            `/api/comorbidity?patient_id=${patientId}`,
            {
              credentials: "include",
              headers: {
                "Cache-Control": "no-cache",
                Pragma: "no-cache",
              },
            }
          );

          if (!response.ok) {
            console.error(
              "Erro na resposta da API de comorbidades:",
              response.status
            );

            
            if (response.status === 401) {
              console.log(
                "⚠️ Erro de autenticação ao carregar comorbidades - ignorando temporariamente para desenvolvimento"
              );
              
              return [];
            }

            throw new Error("Falha ao carregar dados de comorbidades");
          }

          const comorbidities = await response.json();
          console.log("Comorbidades carregadas:", comorbidities);
          return comorbidities;
        } catch (error) {
          console.error("Erro ao carregar comorbidades:", error);
          document.getElementById("comorbidities-container").innerHTML =
            '<p style="color: #e53e3e;">Erro ao carregar condições de saúde. Tente novamente mais tarde.</p>';
          
          return [];
        }
      }

      
      async function loadPatientMonitoring(patientId) {
        try {
          
          
          const response = await fetch(
            `/api/patient-monitoring?patient_id=${patientId}`
          );
          if (response.ok) {
            const monitoring = await response.json();
            
          }
        } catch (error) {
          console.error("Erro ao carregar monitoramento:", error);
        }
      }

      (async function loadProfile() {
        console.log("🚀 Iniciando carregamento do perfil...");
        const usr = getUserFromCookie();
        if (!usr) {
          console.error("❌ Usuário não encontrado no cookie!");
          document.getElementById("wounds-container").innerHTML = `
            <div style="padding: 20px; background: #fed7d7; color: #e53e3e; border-radius: 8px;">
              <p>Não foi possível identificar sua sessão. Por favor, faça login novamente.</p>
              <p><strong>Ambiente de Desenvolvimento:</strong> Para fins de teste, continuaremos o carregamento da página.</p>
            </div>
          `;
          
          
        }

        console.log("📋 Buscando dados do paciente na API...");

        try {
          
          const patientId = usr ? usr.id : 1; 

          const res = await fetch(`/api/patients/${patientId}`, {
            credentials: "include",
            headers: {
              "Cache-Control": "no-cache",
              Pragma: "no-cache",
            },
          });

          if (!res.ok) {
            console.error("❌ Erro ao buscar dados do paciente:", res.status);

            
            if (res.status === 401) {
              console.log(
                "⚠️ Erro de autenticação - ignorando temporariamente para desenvolvimento"
              );
              document.querySelector(".main-area").innerHTML = `
                <div style="padding: 20px; background: #fed7d7; color: #e53e3e; border-radius: 8px;">
                  <h3>Erro de Autenticação</h3>
                  <p>Para fins de desenvolvimento, você pode:</p>
                  <ol>
                    <li>Fazer login <a href="../paciente/login-paciente.html">aqui</a></li>
                    <li>Remover temporariamente o middleware de autenticação nas rotas</li>
                  </ol>
                </div>
              `;
              return;
            }

            throw new Error("Falha ao carregar dados do perfil");
          }

          const p = await res.json();
          console.log("✅ Dados do paciente recebidos:", p);

          
          document.getElementById("profile-name").textContent = p.full_name;
          document.getElementById("profile-fullname").textContent = p.full_name;

          document.getElementById("profile-registration-date").textContent =
            "Paciente desde " +
            new Date(p.registration_date).toLocaleDateString();
          document.getElementById("profile-status").textContent =
            "Tratamento Ativo";

          document.getElementById("profile-cpf").textContent = p.cpf;
          document.getElementById("profile-dob").textContent = new Date(
            p.birth_date
          ).toLocaleDateString();
          document.getElementById("profile-age").textContent =
            calculateAge(p.birth_date) + " anos";
          document.getElementById("profile-phone").textContent = p.phone;
          document.getElementById("profile-email").textContent = p.email;

          
          const [addrPart, cepPart] = (p.address || "").split(" - CEP: ");
          document.getElementById("profile-cep").textContent = cepPart || "";
          document.getElementById("profile-address").textContent =
            addrPart || "";

          
          console.log("🔄 Carregando dados complementares...");

          
          const wounds = await loadWounds(patientId);
          if (wounds && wounds.length > 0) {
            renderWounds(wounds);
          } else {
            document.getElementById("wounds-container").innerHTML =
              "<p>Nenhuma ferida registrada ou erro ao carregar os dados.</p>";
          }

          const comorbidities = await loadComorbidities(patientId);
          if (comorbidities && comorbidities.length > 0) {
            renderComorbidities(comorbidities);
          } else {
            document.getElementById("comorbidities-container").innerHTML =
              "<p>Nenhuma condição de saúde registrada ou erro ao carregar os dados.</p>";
          }

          console.log("✅ Carregamento do perfil concluído!");
        } catch (error) {
          console.error("❌ Erro ao carregar perfil:", error);
          document.querySelector(".main-area").innerHTML = `
            <div style="padding: 20px; background: #fed7d7; color: #e53e3e; border-radius: 8px; text-align: center;">
              <h2>Erro ao carregar perfil</h2>
              <p>${
                error.message ||
                "Ocorreu um erro ao tentar carregar seus dados."
              }</p>
              <button onclick="window.location.reload()" class="button" 
                      style="background: #e53e3e; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; margin-top: 10px;">
                Tentar novamente
              </button>
            </div>
          `;
        }
      })();

      
      function renderWounds(wounds) {
        const container = document.getElementById("wounds-container");

        if (!wounds || wounds.length === 0) {
          container.innerHTML = "<p>Nenhuma ferida registrada.</p>";
          return;
        }

        
        const isMobile = window.innerWidth <= 768;
        const containerClass = isMobile ? "wounds-carousel" : "wounds-grid";

        let html = `<div class="${containerClass}">`;

        
        wounds.forEach((wound) => {
          
          let statusClass = "status-active";
          if (wound.status === "em cicatrização")
            statusClass = "status-healing";
          if (wound.status === "cicatrizada") statusClass = "status-healed";

          html += `
            <div class="wound-card" 
              data-wound-type="${wound.wound_type}"
              data-anatomical-location="${wound.anatomical_location}"
              data-first-occurrence-date="${wound.first_occurrence_date}"
              data-severity-level="${wound.severity_level}"
              data-status="${wound.status}">
              <div class="wound-img-container">
                <div class="wound-placeholder">🩹</div>
              </div>
              <div class="wound-content">
                <div class="wound-title">${wound.wound_type}</div>
                <div class="wound-location">${wound.anatomical_location}</div>
                <span class="wound-status-badge ${statusClass}">${
            wound.status
          }</span>
                <div class="wound-footer">
                  <div class="wound-date">${formatDate(
                    wound.first_occurrence_date
                  )}</div>
                  <button class="wound-action">Ver detalhes</button>
                </div>
              </div>
            </div>
          `;
        });

        html += "</div>";
        container.innerHTML = html;

        
        document
          .getElementById("wounds-container")
          .addEventListener("click", (e) => {
            const card = e.target.closest(".wound-card");
            if (!card) return;

            
            const wound = {
              wound_type: card.dataset.woundType,
              anatomical_location: card.dataset.anatomicalLocation,
              first_occurrence_date: card.dataset.firstOccurrenceDate,
              severity_level: card.dataset.severityLevel,
              status: card.dataset.status,
            };

            
            openWoundModal(wound);
          });
      }

      
      function renderComorbidities(comorbidities) {
        const container = document.getElementById("comorbidities-container");

        if (!comorbidities || comorbidities.length === 0) {
          container.innerHTML = "<p>Nenhuma condição de saúde registrada.</p>";
          return;
        }

        
        const comorbidityColors = {
          diabetes: "#feb2b2",
          hipertensão: "#bfdbfe",
          obesidade: "#fde68a",
          asma: "#a7f3d0",
          "artrite reumatoide": "#ddd6fe",
          "doenca renal": "#c4b5fd",
        };

        let html = "";
        comorbidities.forEach((comorbidity) => {
          
          const lowerType = comorbidity.comorbidity_type.toLowerCase();
          const bgColor = comorbidityColors[lowerType] || "#f3f4f6"; 

          html += `
            <div class="medical-item" style="background-color: ${bgColor}; border-radius: 8px; padding: 10px; margin-bottom: 10px;">
              <div style="display: flex; justify-content: space-between; align-items: center;">
                <div class="medical-label" style="font-weight: 600;">${
                  comorbidity.comorbidity_type
                }</div>
                <div class="medical-value">
                  <span style="display: inline-block; padding: 3px 10px; background: ${
                    comorbidity.under_treatment ? "#dcfce7" : "#fef9c3"
                  }; 
                               border-radius: 12px; font-size: 12px; font-weight: 500;">
                    ${
                      comorbidity.under_treatment
                        ? "✓ Em tratamento"
                        : "⚠️ Sem tratamento ativo"
                    }
                  </span>
                </div>
              </div>
              <div style="font-size: 12px; color: #6b7280; margin-top: 6px;">
                Registrado em ${formatDate(comorbidity.diagnosis_date)}
              </div>
            </div>
          `;
        });

        container.innerHTML = html;
      }
    </script>

    <div vw class="enabled">
      <div vw-access-button class="active"></div>
      <div vw-plugin-wrapper>
        <div class="vw-plugin-top-wrapper"></div>
      </div>
    </div>
    <script src="https://vlibras.gov.br/app/vlibras-plugin.js"></script>
    <script>
      new window.VLibras.Widget("https://vlibras.gov.br/app");
    </script>
    <script src="../../../public/js/logout.js"></script>
  </body>
</html>
