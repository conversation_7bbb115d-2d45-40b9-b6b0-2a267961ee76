const Joi = require("joi");

class UBSModel {
  static get schema() {
    return Joi.object({
      unit_name: Joi.string().max(255).required(),
      address: Joi.string().allow('').required(),
      phone: Joi.string().pattern(/^\d{1,13}$/).required(),
      coverage_area: Joi.string().allow('').required(),
      opening_hours: Joi.string().max(100).required()
    });
  }
}

module.exports = UBSModel;