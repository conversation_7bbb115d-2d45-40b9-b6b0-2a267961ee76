class WoundsController {
  constructor(service) {
        this.service = service;
  }

  async index(req, res) {
    try {
        const data = await this.service.findAll();
        res.json(data); 
    } catch (err) {
        console.error('[WoundsController] - erro ao buscar ferida:', err.message);
        res.status(400).json({ error: err.message });
    }
  }

  async show(req, res) {
    try {
      const data = await this.service.findById(req.params.id);
      res.json(data);
    } catch (err) {
      console.error('[WoundsController] - erro ao buscar ferida:', err.message);
      res.status(400).json({ error: err.message });
    }
  }

  async create(req, res) {
    try {
      const data = await this.service.create(req.body);
      res.json(data);
    } catch (err) {
      console.error('[WoundsController] - erro ao criar ferida:', err.message);
      res.status(400).json({ error: err.message });
    }
  }

  async update(req, res) {
    try {
      const data = await this.service.update(req.params.id, req.body);
      res.json(data);
    } catch (err) {
      console.error('[WoundsController] - erro ao atualizar ferida:', err.message);
      res.status(400).json({ error: err.message });
    }
  }   

  async delete(req, res) {
    try {
      const data = await this.service.delete(req.params.id);
      res.json(data);
    } catch (err) {
      console.error('[WoundsController] - erro ao deletar ferida:', err.message);
      res.status(400).json({ error: err.message });
    }
  }
}

module.exports = WoundsController;
