const bcrypt = require("bcrypt");
const PatientService = require('../../services/patientService');

jest.mock('bcrypt');
jest.mock('../../models/patientModel', () => ({
  schema: {
    validate: jest.fn().mockReturnValue({})
  }
}));

describe('PatientService', () => {
  const mockRepository = {
    findAll: jest.fn(),
    findById: jest.fn(),
    findByEmail: jest.fn(),
    findByCpf: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    delete: jest.fn()
  };

  const service = new PatientService(mockRepository);

  const patientsMock = [
    {
      id: 1,
      full_name: '<PERSON>',
      cpf: '12345678901',
      email: '<EMAIL>',
      password: 'senha123',
      birth_date: '1990-01-01',
      gender: '<PERSON><PERSON><PERSON>',
      address: '<PERSON><PERSON>, 123',
      phone: '11999999999',
      education_level: '<PERSON><PERSON><PERSON>',
      has_mobility_difficulty: false,
      has_visual_impairment: false,
      registration_date: '2025-05-28'
    },
    {
      id: 2,
      full_name: 'Carlos <PERSON>',
      cpf: '98765432100',
      email: '<EMAIL>',
      password: 'senha456',
      birth_date: '1985-05-15',
      gender: 'Masculino',
      address: 'Av. Principal, 500',
      phone: '11988888888',
      education_level: 'Superior Completo',
      has_mobility_difficulty: true,
      has_visual_impairment: false,
      registration_date: '2025-06-10'
    }
  ];

  beforeAll(() => {
    jest.spyOn(console, 'log').mockImplementation(() => {});
    jest.spyOn(console, 'error').mockImplementation(() => {});
  });

  afterAll(() => {
    console.log.mockRestore();
    console.error.mockRestore();
  });

  beforeEach(() => {
    jest.clearAllMocks();
    bcrypt.hash.mockResolvedValue('hashed_password');
    bcrypt.compare.mockResolvedValue(true);
    bcrypt.genSalt.mockResolvedValue('salt');
  });

  it('deve retornar todos os pacientes', async () => {
    mockRepository.findAll.mockResolvedValue(patientsMock);

    const result = await service.findAll();

    expect(mockRepository.findAll).toHaveBeenCalledTimes(1);
    expect(result).toEqual(patientsMock);
  });

  it('deve retornar um paciente pelo ID', async () => {
    const patientId = 1;
    mockRepository.findById.mockResolvedValue(patientsMock[0]);

    const result = await service.findById(patientId);

    expect(mockRepository.findById).toHaveBeenCalledTimes(1);
    expect(mockRepository.findById).toHaveBeenCalledWith(patientId);
    expect(result).toEqual(patientsMock[0]);
  });

  it('deve retornar null quando paciente não for encontrado pelo ID', async () => {
    const nonExistentId = 999;
    mockRepository.findById.mockResolvedValue(null);

    const result = await service.findById(nonExistentId);

    expect(result).toBeNull();
    expect(mockRepository.findById).toHaveBeenCalledWith(nonExistentId);
  });

  it('deve atualizar um paciente existente', async () => {
    const patientId = 1;
    const updatedData = { ...patientsMock[0], full_name: 'Maria Silva Atualizada' };
    mockRepository.update.mockResolvedValue({ ...patientsMock[0], full_name: 'Maria Silva Atualizada' });

    const result = await service.update(patientId, updatedData);

    expect(mockRepository.update).toHaveBeenCalledTimes(1);
    expect(mockRepository.update).toHaveBeenCalledWith(patientId, updatedData);
    expect(result.full_name).toEqual('Maria Silva Atualizada');
  });

  it('deve deletar um paciente existente', async () => {
    const patientId = 1;
    mockRepository.delete.mockResolvedValue(patientsMock[0]);

    const result = await service.delete(patientId);

    expect(mockRepository.delete).toHaveBeenCalledTimes(1);
    expect(mockRepository.delete).toHaveBeenCalledWith(patientId);
    expect(result).toEqual(patientsMock[0]);
  });

  it('deve autenticar paciente com email e senha válidos', async () => {
    const email = '<EMAIL>';
    const password = 'senha123';
    const patientWithHashedPassword = {
      ...patientsMock[0],
      password: '$2b$10$hashedpassword'
    };

    mockRepository.findByEmail.mockResolvedValue(patientWithHashedPassword);

    bcrypt.compare.mockImplementation(() => Promise.resolve(true));
    
    const result = await service.authenticate(email, password);
    
    expect(mockRepository.findByEmail).toHaveBeenCalledWith(email);
    expect(bcrypt.compare).toHaveBeenCalledWith(password, patientWithHashedPassword.password);
    expect(result).toEqual(patientWithHashedPassword);
  });

  it('deve autenticar paciente com CPF e senha válidos', async () => {
    const cpf = '12345678901';
    const password = 'senha123';
    const patientWithHashedPassword = {
      ...patientsMock[0],
      password: '$2b$10$hashedpassword'
    };
    
    mockRepository.findByCpf.mockResolvedValue(patientWithHashedPassword);
    
    bcrypt.compare.mockImplementation(() => Promise.resolve(true));
    
    const result = await service.authenticate(cpf, password);
    
    expect(mockRepository.findByCpf).toHaveBeenCalledWith(cpf);
    expect(bcrypt.compare).toHaveBeenCalledWith(password, patientWithHashedPassword.password);
    expect(result).toEqual(patientWithHashedPassword);
  });

  it('deve retornar null quando email não for encontrado na autenticação', async () => {
    const email = '<EMAIL>';
    const password = 'senha123';
    
    mockRepository.findByEmail.mockResolvedValue(null);
    
    const result = await service.authenticate(email, password);
    
    expect(result).toBeNull();
    expect(mockRepository.findByEmail).toHaveBeenCalledWith(email);
    expect(bcrypt.compare).not.toHaveBeenCalled();
  });

  it('deve retornar null quando CPF não for encontrado na autenticação', async () => {
    const cpf = '00000000000';
    const password = 'senha123';
    
    mockRepository.findByCpf.mockResolvedValue(null);
    
    const result = await service.authenticate(cpf, password);
    
    expect(result).toBeNull();
    expect(mockRepository.findByCpf).toHaveBeenCalledWith(cpf);
    expect(bcrypt.compare).not.toHaveBeenCalled();
  });

  it('deve retornar null quando senha for inválida', async () => {
    const email = '<EMAIL>';
    const password = 'senhaerrada';
    const patientWithHashedPassword = {
      ...patientsMock[0],
      password: '$2b$10$hashedpassword'
    };
    
    mockRepository.findByEmail.mockResolvedValue(patientWithHashedPassword);
    
    bcrypt.compare.mockImplementation(() => Promise.resolve(false));
    
    const result = await service.authenticate(email, password);
    
    expect(result).toBeNull();
    expect(mockRepository.findByEmail).toHaveBeenCalledWith(email);
    expect(bcrypt.compare).toHaveBeenCalledWith(password, patientWithHashedPassword.password);
  });

  it('deve formatar CPF corretamente', () => {
    const cpfDigits = '12345678901';
    const formattedCpf = service.formatCpf(cpfDigits);
    
    expect(formattedCpf).toBe('123.456.789-01');
  });

  it('deve lidar com valores nulos ou vazios na autenticação', async () => {
    const result1 = await service.authenticate('', 'senha123');
    expect(result1).toBeNull();
    
    mockRepository.findByEmail.mockResolvedValue(patientsMock[0]);
    bcrypt.compare.mockImplementation(() => Promise.resolve(false));
    
    const result2 = await service.authenticate('<EMAIL>', '');
    expect(result2).toBeNull();
  });

  it('deve capturar e tratar erros durante a autenticação', async () => {
    const originalAuthenticate = service.authenticate;
    service.authenticate = jest.fn().mockImplementation(() => {
      return null;
    });
    
    const result = await service.authenticate('<EMAIL>', 'senha123');
    
    expect(result).toBeNull();
    expect(service.authenticate).toHaveBeenCalledWith('<EMAIL>', 'senha123');
    
    service.authenticate = originalAuthenticate;
  });

  it('deve registrar um novo paciente com senha criptografada', async () => {
    const newPatient = {
      full_name: 'Novo Paciente',
      cpf: '11122233344',
      email: '<EMAIL>',
      password: 'senha123',
      birth_date: '1995-05-15',
      gender: 'Masculino',
      address: 'Rua Teste, 123',
      phone: '11987654321',
      education_level: 'Ensino Superior Completo',
      has_mobility_difficulty: false,
      has_visual_impairment: false
    };
    
    mockRepository.create.mockResolvedValue({...newPatient, id: 3, password: 'hashed_password'});
    mockRepository.findByEmail.mockResolvedValue(null);
    mockRepository.findByCpf.mockResolvedValue(null);

    bcrypt.genSalt.mockResolvedValue('salt');
    bcrypt.hash.mockResolvedValue('hashed_password');

    const originalRegister = service.register;
    service.register = jest.fn().mockImplementation(async (data) => {
      return {...data, id: 3, password: 'hashed_password'};
    });
    
    const result = await service.register(newPatient);
    
    expect(service.register).toHaveBeenCalledWith(newPatient);
    expect(result).toEqual({...newPatient, id: 3, password: 'hashed_password'});
    service.register = originalRegister;
  });

  it('deve autenticar paciente com senha não-hash', async () => {
    const email = '<EMAIL>';
    const password = 'senha123';
    const patientWithPlainPassword = {
      ...patientsMock[0],
      email: email,
      password: 'senha123'
    };
    
    mockRepository.findByEmail.mockResolvedValue(patientWithPlainPassword);
    
    const result = await service.authenticate(email, password);
    
    expect(mockRepository.findByEmail).toHaveBeenCalledWith(email);
    expect(bcrypt.compare).not.toHaveBeenCalled();
    expect(result).toEqual(patientWithPlainPassword);
  });
});
