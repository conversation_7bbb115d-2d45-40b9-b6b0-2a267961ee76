const { Pool } = require("pg");
const path = require("path");

require("dotenv").config({ path: path.resolve(__dirname, "../../.env") });

const isSSL = process.env.DB_SSL === "true";

const sslConfig = isSSL
  ? {
      rejectUnauthorized: false,
    }
  : false;


const pool = new Pool({
  host: process.env.DB_HOST || "localhost",
  port: parseInt(process.env.DB_PORT, 10) || 5432,
  database: process.env.DB_DATABASE || "cicatriza_db",
  user: process.env.DB_USER || "postgres",
  password: process.env.DB_PASSWORD,
  ssl: sslConfig,
  max: 20,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 10000,
  maxUses: 7500,
});

pool.on("connect", () => {
  console.log("✅ Conexão estabelecida com o banco de dados");
});

pool.on("error", (err) => {
  console.error("❌ Erro no pool de conexões:", err);
});

async function queryWithRetry(text, params, maxRetries = 3) {
  let retries = 0;
  while (true) {
    try {
      const client = await pool.connect();
      try {
        const result = await client.query(text, params);
        return result;
      } finally {
        client.release();
      }
    } catch (err) {
      retries += 1;
      console.error(`Tentativa ${retries}/${maxRetries} falhou:`, err.message);

      if (retries >= maxRetries) {
        console.error("Todas as tentativas falharam");
        throw err;
      }
      await new Promise((resolve) => setTimeout(resolve, 1000 * retries));
    }
  }
}

module.exports = {
  query: queryWithRetry,

  connect: async () => {
    try {
      const client = await pool.connect();
      console.log("✅ Cliente conectado ao banco com sucesso");
      const result = await client.query("SELECT NOW()");
      console.log("✅ Teste de conexão bem-sucedido:", result.rows[0]);

      client.release();
      return client;
    } catch (error) {
      console.error("❌ Detalhes do erro de conexão:", {
        message: error.message,
        code: error.code,
        detail: error.detail,
        host: process.env.DB_HOST || "localhost",
        port: process.env.DB_PORT || 5432,
        database: process.env.DB_DATABASE || "cicatriza_db",
        user: process.env.DB_USER || "postgres",
        password_provided: !!process.env.DB_PASSWORD,
      });

      throw new Error(`Erro de conexão com o banco de dados: ${error.message}`);
    }
  },
  
  end: async () => {
    await pool.end();
    console.log("🔌 Pool de conexões encerrado");
  },
};
