import { readdirSync } from "fs";
import path from "path";
import { fileURLToPath } from "url";
import { Client } from "pg";
import dotenv from "dotenv";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

dotenv.config({ path: path.resolve(__dirname, "../../.env") });

async function runMigrations(folder) {
  console.log("URL de conexão:", process.env.DATABASE_URL);
  const client = new Client({
    host: process.env.DB_HOST,
    port: parseInt(process.env.DB_PORT, 10),
    database: process.env.DB_DATABASE,
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    ssl: process.env.DB_SSL === "true" ? { rejectUnauthorized: false } : false,
  });

  await client.connect();

  
  console.log("\nExecutando migrações de produção...");
  const prodPath = path.join(__dirname, "production");
  const prodFiles = readdirSync(prodPath)
    .filter((file) => file.endsWith(".js"))
    .sort();

  for (const file of prodFiles) {
    console.log(`Executando: ${file}`);
    const filePath = path.join(prodPath, file);
    const module = await import(`file://${filePath}`);
    await module.default(client);
  }

  
  console.log("\nExecutando seeds de desenvolvimento...");
  const devPath = path.join(__dirname, "development");
  const devFiles = readdirSync(devPath)
    .filter((file) => file.endsWith(".js"))
    .sort();

  for (const file of devFiles) {
    console.log(`Executando: ${file}`);
    const filePath = path.join(devPath, file);
    const module = await import(`file://${filePath}`);
    await module.default(client);
  }

  await client.end();
}

const env = process.argv[2] || "production";
runMigrations(env)
  .then(() => {
    console.log("\nMigrações e seeds concluídos.");
  })
  .catch((err) => {
    console.error("Erro ao executar migrações:", err);
  });
