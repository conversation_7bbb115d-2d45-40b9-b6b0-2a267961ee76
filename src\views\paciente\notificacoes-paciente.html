<!DOCTYPE html>
<html lang="pt-BR">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" href="../../../public/css/global.css" />
    <title>Notificações</title>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&display=swap"
      rel="stylesheet"
    />
    <script src="../../../public/js/auth-check.js"></script>
    <style>
      .notification-section {
        margin-bottom: 30px;
      }
      .section-title {
        font-size: 18px;
        font-weight: 600;
        color: #2d3748;
        margin-bottom: 16px;
      }
      .notification-card {
        background: white;
        border-radius: 12px;
        padding: 16px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        margin-bottom: 15px;
      }
      .notification-content {
        display: flex;
      }
      .notification-icon {
        font-size: 24px;
        margin-right: 16px;
        min-width: 40px;
        display: flex;
        align-items: flex-start;
        justify-content: center;
      }
      .notification-details {
        flex-grow: 1;
      }
      .notification-title {
        font-size: 16px;
        font-weight: 600;
        color: #2d3748;
        margin-bottom: 5px;
      }
      .notification-message {
        font-size: 14px;
        color: #4a5568;
        margin-bottom: 8px;
      }
      .notification-time {
        font-size: 12px;
        color: #718096;
      }
      .notification-alert {
        border-left: 4px solid #f56565;
      }
      .notification-reminder {
        border-left: 4px solid #4299e1;
      }
      .notification-notice {
        border-left: 4px solid #68d391;
      }
      .notification-unread {
        background-color: #f7fafc;
      }
      .notification-actions {
        display: flex;
        margin-top: 10px;
        gap: 10px;
      }
      .notification-button {
        background: transparent;
        border: 1px solid #e2e8f0;
        border-radius: 4px;
        padding: 5px 10px;
        font-size: 12px;
        cursor: pointer;
        color: #4a5568;
      }
      .notification-button:hover {
        background-color: #f7fafc;
      }
      .empty-state {
        text-align: center;
        padding: 20px;
        color: #718096;
        font-style: italic;
      }
      .loading-indicator {
        text-align: center;
        padding: 20px;
        color: #718096;
        font-style: italic;
      }

      /* Estilo para botão de excluir nas notificações */
      .notification-action-buttons {
        display: flex;
        gap: 8px;
        margin-top: 10px;
      }

      .notification-action-btn {
        background: transparent;
        border: 1px solid #e2e8f0;
        border-radius: 4px;
        padding: 4px 8px;
        font-size: 12px;
        cursor: pointer;
        display: flex;
        align-items: center;
        gap: 4px;
      }

      .delete-btn {
        color: #e53e3e;
      }

      .delete-btn:hover {
        background-color: #fff5f5;
      }
    </style>
  </head>
  <body>
    <div class="desktop-frame">
      <div class="sidebar">
        <div class="sidebar-logo">
          <img
            src="../../../public/images/logo/cicatriza_semnada.png"
            alt="Logo"
            class="logo-small"
          />
          <h3 class="sidebar-title">Cicatriza+</h3>
        </div>
        <ul class="sidebar-menu">
          <a href="menu-principal.html">
            <li class="sidebar-item">
              <span class="sidebar-icon">🏠</span>Início
            </li>
          </a>
          <a href="tutorial-paciente.html">
            <li class="sidebar-item">
              <span class="sidebar-icon">📝</span>Tutorial
            </li>
          </a>
          <a href="enviar-foto-paciente.html">
            <li class="sidebar-item">
              <span class="sidebar-icon">📷</span>Enviar foto
            </li>
          </a>
          <a href="videos-paciente.html">
            <li class="sidebar-item">
              <span class="sidebar-icon">🎥</span>Vídeos
            </li>
          </a>
          <a href="perfil-paciente.html">
            <li class="sidebar-item">
              <span class="sidebar-icon">👤</span>Perfil
            </li>
          </a>
          <a href="notificacoes-paciente.html">
            <li class="sidebar-item active">
              <span class="sidebar-icon">🔔</span>Notificações
            </li>
          </a>
          <a href="emergencia.html">
            <li class="sidebar-item">
              <span class="sidebar-icon">⚠️</span>Emergência
            </li>
          </a>
        </ul>
      </div>
      <div class="desktop-content">
        <div class="desktop-header">
          <div class="header-title">Suas Notificações</div>
        </div>
        <div class="main-area">
          <div class="notification-section">
            <h3 class="section-title">Notificações do Sistema</h3>
            <div id="notifications-container">
              <div class="loading-indicator">Carregando notificações...</div>
            </div>
          </div>
        </div>

        <div class="footer-desktop">
          <div class="footer-left">
            Menu - início - Perfil - Login - Contate - nos
          </div>
          <div class="footer-right">Cicatriza+</div>
        </div>
      </div>
    </div>

    <div class="mobile-navbar">
      <div class="mobile-nav-container">
        <a href="menu-principal.html" class="mobile-nav-item">
          <div class="mobile-nav-icon">🏠</div>
          <div class="mobile-nav-text">Início</div>
        </a>
        <a href="consultas-paciente.html" class="mobile-nav-item">
          <div class="mobile-nav-icon">📋</div>
          <div class="mobile-nav-text">Consultas</div>
        </a>
        <a href="saude-paciente.html" class="mobile-nav-item">
          <div class="mobile-nav-icon">📊</div>
          <div class="mobile-nav-text">Saúde</div>
        </a>
        <a href="perfil-paciente.html" class="mobile-nav-item">
          <div class="mobile-nav-icon">👤</div>
          <div class="mobile-nav-text">Perfil</div>
        </a>
      </div>
    </div>

    <div vw class="enabled">
      <div vw-access-button class="active"></div>
      <div vw-plugin-wrapper>
        <div class="vw-plugin-top-wrapper"></div>
      </div>
    </div>
    <script src="https://vlibras.gov.br/app/vlibras-plugin.js"></script>
    <script>
      new window.VLibras.Widget("https://vlibras.gov.br/app");

      
      function getUserFromCookie() {
        const match = document.cookie.match("(^|;)\\s*user=([^;]+)");
        return match ? JSON.parse(decodeURIComponent(match[2])) : null;
      }

      
      async function deleteNotification(notificationId) {
        if (!confirm("Tem certeza que deseja excluir esta notificação?")) {
          return;
        }

        try {
          const response = await fetch(
            `/api/system-notifications/${notificationId}`,
            {
              method: "DELETE",
            }
          );

          if (!response.ok) {
            const errorText = await response.text();
            console.error("Resposta do servidor:", errorText);
            throw new Error("Erro ao excluir notificação: " + errorText);
          }

          
          const notificationElement = document.getElementById(
            `notification-${notificationId}`
          );
          if (notificationElement) {
            notificationElement.remove();
          }

          
          const container = document.getElementById("notifications-container");
          if (container.children.length === 0) {
            container.innerHTML =
              '<div class="empty-state">Nenhuma notificação encontrada</div>';
          }

          alert("Notificação excluída com sucesso!");
        } catch (error) {
          console.error("Erro ao excluir notificação:", error);
          alert(
            "Erro ao excluir notificação. Por favor, verifique o console para detalhes."
          );
        }
      }

      
      async function loadNotifications() {
        const user = getUserFromCookie();
        if (!user || !user.id) {
          console.error("Usuário não encontrado no cookie");
          document.getElementById("notifications-container").innerHTML =
            '<div class="empty-state">Faça login para ver suas notificações</div>';
          return;
        }

        try {
          console.log("Buscando notificações para o paciente:", user.id);

          
          const response = await fetch(
            `/api/system-notifications?patient_id=${user.id}`
          );

          console.log("Status da resposta:", response.status);

          if (!response.ok) {
            const errorText = await response.text();
            console.error("Detalhes do erro:", errorText);
            throw new Error("Erro ao carregar notificações: " + errorText);
          }

          const notifications = await response.json();
          console.log("Notificações recebidas:", notifications);

          const container = document.getElementById("notifications-container");

          
          container.innerHTML = "";

          if (notifications.length === 0) {
            container.innerHTML =
              '<div class="empty-state">Nenhuma notificação encontrada</div>';
            return;
          }

          
          notifications.sort(
            (a, b) => new Date(b.sent_date) - new Date(a.sent_date)
          );

          
          notifications.forEach((notification) => {
            const isUnread = !notification.viewed; 
            let cardClass = "card notification-card";

            
            if (notification.type === "Alerta") {
              cardClass += " notification-alert";
            } else if (notification.type === "Lembrete") {
              cardClass += " notification-reminder";
            } else if (notification.type === "Aviso") {
              cardClass += " notification-notice";
            }

            
            if (isUnread) {
              cardClass += " notification-unread";
            }

            
            let icon = "🔔";
            if (notification.type === "Alerta") icon = "⚠️";
            if (notification.type === "Lembrete") icon = "📅";
            if (notification.type === "Aviso") icon = "📢";

            
            const notificationElement = document.createElement("div");
            notificationElement.className = cardClass;
            notificationElement.id = `notification-${notification.id}`;
            notificationElement.innerHTML = `
              <div class="notification-content">
                <div class="notification-icon">${icon}</div>
                <div class="notification-details">
                  <div class="notification-title">
                    ${notification.type}
                    ${
                      isUnread
                        ? '<span style="color:#3182ce;font-size:12px;margin-left:8px;">• Novo</span>'
                        : ""
                    }
                  </div>
                  <div class="notification-message">${
                    notification.content
                  }</div>
                  <div class="notification-time">
                    ${formatDate(notification.sent_date)}
                  </div>
                  <div class="notification-action-buttons">
                    ${
                      isUnread
                        ? `<button class="notification-button" onclick="markAsRead(${notification.id})">Marcar como lida</button>`
                        : ""
                    }
                    <button class="notification-action-btn delete-btn" onclick="deleteNotification(${
                      notification.id
                    })">
                      🗑️ Excluir
                    </button>
                  </div>
                </div>
              </div>
            `;

            container.appendChild(notificationElement);
          });
        } catch (error) {
          console.error("Erro detalhado ao carregar notificações:", error);
          document.getElementById("notifications-container").innerHTML =
            '<div class="empty-state">Erro ao carregar notificações. Verifique o console para detalhes.</div>';
        }
      }

      
      function formatDate(dateString) {
        const date = new Date(dateString);
        const now = new Date();
        const diff = now - date;

        
        if (diff < 24 * 60 * 60 * 1000) {
          const hours = date.getHours().toString().padStart(2, "0");
          const minutes = date.getMinutes().toString().padStart(2, "0");
          return `Hoje, ${hours}:${minutes}`;
        }

        
        if (diff < 48 * 60 * 60 * 1000) {
          const hours = date.getHours().toString().padStart(2, "0");
          const minutes = date.getMinutes().toString().padStart(2, "0");
          return `Ontem, ${hours}:${minutes}`;
        }

        
        return (
          date.toLocaleDateString() +
          " às " +
          date.getHours().toString().padStart(2, "0") +
          ":" +
          date.getMinutes().toString().padStart(2, "0")
        );
      }

      
      async function markAsRead(notificationId) {
        try {
          const response = await fetch(
            `/api/system-notifications/${notificationId}`,
            {
              method: "PUT",
              headers: {
                "Content-Type": "application/json",
              },
              body: JSON.stringify({
                viewed: true,
                view_date: new Date().toISOString(),
              }),
            }
          );

          if (!response.ok) {
            const errorText = await response.text();
            console.error("Erro ao atualizar notificação:", errorText);
            throw new Error("Erro ao atualizar notificação: " + errorText);
          }

          
          loadNotifications();
        } catch (error) {
          console.error(
            "Erro detalhado ao marcar notificação como lida:",
            error
          );
          alert(
            "Erro ao atualizar notificação. Verifique o console para detalhes."
          );
        }
      }

      
      window.addEventListener("DOMContentLoaded", loadNotifications);
    </script>
  </body>
</html>
