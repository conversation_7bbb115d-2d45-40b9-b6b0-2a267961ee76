const Joi = require("joi");

class EducationalMaterialModel {
  static get schema() {
    return Joi.object({
      title: Joi.string().max(255).required(),
      content_type: Joi.string().valid('<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>').required(),
      video_uri: Joi.string().max(255).required(),
      description: Joi.string().allow('').optional(),
      target_wound_type: Joi.string().valid('Úlcera de pé diabético', 'Úlcera venosa', 'Lesão por pressão').required(),
      target_severity_level: Joi.number().integer().valid(1, 2, 3).required(),
      creation_date: Joi.date().required()
    });
  }
}

module.exports = EducationalMaterialModel;