const PatientComorbidityModel = require('../../models/patientComorbidityModel');

describe('PatientComorbidityModel Schema', () => {
  const validData = {
    comorbidity_type: 'Hipertensão',
    diagnosis_date: new Date('2023-05-15'),
    under_treatment: true,
    observations: 'Paciente responde bem ao tratamento',
    patient_id: 1
  };

  it('deve validar dados corretos com sucesso', () => {
    const { error } = PatientComorbidityModel.schema.validate(validData);
    expect(error).toBeUndefined();
  });

  it('deve falhar se comorbidity_type estiver ausente', () => {
    const invalidData = { ...validData };
    delete invalidData.comorbidity_type;

    const { error } = PatientComorbidityModel.schema.validate(invalidData);
    expect(error).toBeDefined();
    expect(error.details[0].path).toContain('comorbidity_type');
  });

  it('deve falhar se diagnosis_date for inválido', () => {
    const invalidData = { ...validData, diagnosis_date: 'ontem' };

    const { error } = PatientComorbidityModel.schema.validate(invalidData);
    expect(error).toBeDefined();
    expect(error.details[0].path).toContain('diagnosis_date');
  });

  it('deve aceitar observations vazias', () => {
    const data = { ...validData, observations: '' };

    const { error } = PatientComorbidityModel.schema.validate(data);
    expect(error).toBeUndefined();
  });

  it('deve falhar se under_treatment não for booleano', () => {
    const invalidData = { ...validData, under_treatment: 'sim' };

    const { error } = PatientComorbidityModel.schema.validate(invalidData);
    expect(error).toBeDefined();
    expect(error.details[0].path).toContain('under_treatment');
  });

  it('deve falhar se patient_id não for número inteiro', () => {
    const invalidData = { ...validData, patient_id: 'abc' };

    const { error } = PatientComorbidityModel.schema.validate(invalidData);
    expect(error).toBeDefined();
    expect(error.details[0].path).toContain('patient_id');
  });
});
