 const EmergencyModel = require('../../models/emergencyModel'); 

describe('EmergencyModel Schema', () => {
  const validData = {
    call_datetime: new Date(),
    reason: 'Paciente com dor intensa na perna',
    attended: true,
    observations: 'Encaminhado para avaliação vascular',
    referral: 'UPA Centro',
    patient_id: 1,
    agent_id: 3
  };

  it('deve validar dados corretos com sucesso', () => {
    const { error } = EmergencyModel.schema.validate(validData);
    expect(error).toBeUndefined();
  });

  it('deve aceitar reason e observations vazios', () => {
    const data = { ...validData, reason: '', observations: '' };
    const { error } = EmergencyModel.schema.validate(data);
    expect(error).toBeUndefined();
  });

  it('deve aceitar agent_id como null', () => {
    const data = { ...validData, agent_id: null };
    const { error } = EmergencyModel.schema.validate(data);
    expect(error).toBeUndefined();
  });

  it('deve falhar se call_datetime estiver ausente', () => {
    const invalidData = { ...validData };
    delete invalidData.call_datetime;

    const { error } = EmergencyModel.schema.validate(invalidData);
    expect(error).toBeDefined();
    expect(error.details[0].path).toContain('call_datetime');
  });

  it('deve falhar se attended não for booleano', () => {
    const invalidData = { ...validData, attended: 'sim' };

    const { error } = EmergencyModel.schema.validate(invalidData);
    expect(error).toBeDefined();
    expect(error.details[0].path).toContain('attended');
  });

  it('deve falhar se referral for maior que 50 caracteres', () => {
    const invalidData = { ...validData, referral: 'A'.repeat(51) };

    const { error } = EmergencyModel.schema.validate(invalidData);
    expect(error).toBeDefined();
    expect(error.details[0].path).toContain('referral');
  });

  it('deve falhar se patient_id não for número inteiro', () => {
    const invalidData = { ...validData, patient_id: 'abc' };

    const { error } = EmergencyModel.schema.validate(invalidData);
    expect(error).toBeDefined();
    expect(error.details[0].path).toContain('patient_id');
  });
});
