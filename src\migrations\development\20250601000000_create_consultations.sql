CREATE TABLE consultation (
  id SERIAL PRIMARY KEY,
  consultation_date DATE NOT NULL,
  consultation_time TIME NOT NULL,
  consultation_type VARCHAR(50) NOT NULL, -- presencial, online, domiciliar
  status VARCHAR(20) NOT NULL, -- agendada, concluída, cancelada, pendente
  notes TEXT,
  patient_id INTEGER NOT NULL,
  health_agent_id INTEGER NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (patient_id) REFERENCES patient(id),
  FOREIGN KEY (health_agent_id) REFERENCES health_agent(id)
);

COMMENT ON COLUMN consultation.consultation_type IS 'presencial, online, domiciliar';
COMMENT ON COLUMN consultation.status IS 'agendada, concluída, cancelada, pendente';
