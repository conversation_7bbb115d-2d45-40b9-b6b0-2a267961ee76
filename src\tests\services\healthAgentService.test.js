const HealthAgentService = require('../../services/healthAgentService');
const bcrypt = require('bcrypt');

jest.mock('bcrypt');

describe('HealthAgentService', () => {
  const mockRepository = {
    findAll: jest.fn(),
    findById: jest.fn(),
    findByEmail: jest.fn(),
    findByCpf: jest.fn()
  };

  const service = new HealthAgentService(mockRepository);

  const healthAgentsMock = [
    {
      id: 1,
      full_name: '<PERSON>',
      agent_type: 'Agente Comunitário de Saúde',
      agent_registration: 'ACS-001',
      phone: '(11) 91111-1111',
      email: '<EMAIL>',
      health_unit_id: 1,
      password: 'senha123'
    },
    {
      id: 2,
      full_name: 'Mariana Costa',
      agent_type: 'Enfermeira',
      agent_registration: 'ENF-002',
      phone: '(31) 92222-2222',
      email: '<EMAIL>',
      health_unit_id: 2,
      password: 'senha456'
    }
  ];

  beforeAll(() => {
    jest.spyOn(console, 'log').mockImplementation(() => {});
    jest.spyOn(console, 'error').mockImplementation(() => {});
  });

  afterAll(() => {
    console.log.mockRestore();
    console.error.mockRestore();
  });

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('deve retornar todos os agentes de saúde', async () => {
    mockRepository.findAll.mockResolvedValue(healthAgentsMock);
    const result = await service.findAll();

    expect(mockRepository.findAll).toHaveBeenCalledTimes(1);
    expect(result).toEqual(healthAgentsMock);
  });

  it('deve retornar um agente de saúde pelo ID', async () => {
    const agentId = 1;
    mockRepository.findById.mockResolvedValue(healthAgentsMock[0]);
    const result = await service.findById(agentId);

    expect(mockRepository.findById).toHaveBeenCalledTimes(1);
    expect(mockRepository.findById).toHaveBeenCalledWith(agentId);
    expect(result).toEqual(healthAgentsMock[0]);
  });

  it('deve retornar null quando agente não for encontrado pelo ID', async () => {
    const agentId = 999;
    mockRepository.findById.mockResolvedValue(null);
    
    const result = await service.findById(agentId);
    
    expect(result).toBeNull();
    expect(mockRepository.findById).toHaveBeenCalledTimes(1);
    expect(mockRepository.findById).toHaveBeenCalledWith(agentId);
  });

  it('deve autenticar um agente com email e senha válidos', async () => {
    const email = '<EMAIL>';
    const password = 'senha123';
    
    mockRepository.findByEmail.mockResolvedValue(healthAgentsMock[0]);
    
    const result = await service.authenticate(email, password);
    
    expect(mockRepository.findByEmail).toHaveBeenCalledWith(email);
    expect(result).toEqual(healthAgentsMock[0]);
  });

  it('deve autenticar um agente com CPF e senha válidos', async () => {
    const cpf = '12345678900';
    const password = 'senha123';
    
    mockRepository.findByCpf.mockResolvedValue(healthAgentsMock[0]);
    
    const result = await service.authenticate(cpf, password);
    
    expect(mockRepository.findByCpf).toHaveBeenCalledWith(cpf);
    expect(result).toEqual(healthAgentsMock[0]);
  });

  it('deve retornar null quando email não for encontrado', async () => {
    const email = '<EMAIL>';
    const password = 'senha123';
    
    mockRepository.findByEmail.mockResolvedValue(null);
    
    const result = await service.authenticate(email, password);
    
    expect(result).toBeNull();
    expect(mockRepository.findByEmail).toHaveBeenCalledWith(email);
  });

  it('deve retornar null quando CPF não for encontrado', async () => {
    const cpf = '99999999999';
    const password = 'senha123';
    
    mockRepository.findByCpf.mockResolvedValue(null);
    
    const result = await service.authenticate(cpf, password);
    
    expect(result).toBeNull();
    expect(mockRepository.findByCpf).toHaveBeenCalledWith(cpf);
  });

  it('deve retornar null quando senha for inválida', async () => {
    const email = '<EMAIL>';
    const password = 'senhaerrada';
    
    mockRepository.findByEmail.mockResolvedValue(healthAgentsMock[0]);
    
    const result = await service.authenticate(email, password);
    
    expect(result).toBeNull();
    expect(mockRepository.findByEmail).toHaveBeenCalledWith(email);
  });

  it('deve lidar com valores nulos ou vazios na autenticação', async () => {
    const result1 = await service.authenticate('', 'senha123');
    expect(result1).toBeNull();
    
    const result2 = await service.authenticate('<EMAIL>', '');
    expect(result2).toBeNull();
  });
});
