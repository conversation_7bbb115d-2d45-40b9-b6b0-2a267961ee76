const express = require("express");
const path = require("path");
const app = express();
const routes = require("./routes");
const photoRecordRoutes = require("./routes/photoRecordRoutes");

// Body parser middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Serve static files
app.use("/uploads", express.static(path.join(__dirname, "../uploads")));
app.use("/imagens", express.static(path.join(__dirname, "../public/images")));
app.use("/public", express.static(path.join(__dirname, "../public")));
app.use("/views", express.static(path.join(__dirname, "views")));

// expor assets
app.use(
  "/assets/photos",
  express.static(path.join(__dirname, "public/assets/photos"))
);

// API routes
app.use("/", routes);
app.use("/api/photo-records", photoRecordRoutes);

// Error handler
app.use((err, req, res, next) => {
  console.error("Server error:", err.stack);
  res.status(500).json({
    error: "Erro interno do servidor",
    details: process.env.NODE_ENV === "development" ? err.message : undefined,
  });
});

module.exports = app;
