<!DOCTYPE html>
<html lang="pt-BR">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" href="../../../public/css/global.css" />
    <title>Notificações do Profissional</title>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&display=swap"
      rel="stylesheet"
    />
     <script src="../../../public/js/auth-check.js"></script>
     <style>
      .modal-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1000;
        display: none;
      }
      
      .modal-content {
        background: white;
        border-radius: 8px;
        width: 90%;
        max-width: 500px;
        padding: 20px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      }
      
      .modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
      }
      
      .modal-title {
        font-size: 18px;
        font-weight: 600;
        color: #2d3748;
      }
      
      .modal-close {
        background: none;
        border: none;
        font-size: 20px;
        cursor: pointer;
        color: #718096;
      }
      
      .form-group {
        margin-bottom: 15px;
      }
      
      .form-label {
        display: block;
        margin-bottom: 8px;
        font-weight: 500;
        color: #2d3748;
      }
      
      .form-input {
        width: 100%;
        padding: 10px;
        border: 1px solid #e2e8f0;
        border-radius: 6px;
        font-size: 14px;
      }
      
      .form-textarea {
        min-height: 100px;
        resize: vertical;
      }
      
      .form-actions {
        display: flex;
        justify-content: flex-end;
        gap: 10px;
        margin-top: 20px;
      }
      
      .add-notification-btn {
        position: fixed;
        bottom: 80px;
        right: 30px;
        width: 60px;
        height: 60px;
        background-color: #013c6d;
        color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 30px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        cursor: pointer;
        z-index: 100;
        border: none;
      }
      
      .add-notification-btn:hover {
        background-color: #025497;
      }
      
      .notification-draft {
        opacity: 0.6;
        border-left: 4px dashed #718096;
      }
      
      .notification-status {
        font-size: 12px;
        margin-top: 5px;
        font-style: italic;
      }
      
      .status-success {
        color: #38a169;
      }
      
      .status-error {
        color: #e53e3e;
      }
      
      .notification-action-buttons {
        display: flex;
        gap: 8px;
        margin-top: 10px;
      }
      
      .notification-action-btn {
        background: transparent;
        border: 1px solid #e2e8f0;
        border-radius: 4px;
        padding: 4px 8px;
        font-size: 12px;
        cursor: pointer;
        display: flex;
        align-items: center;
        gap: 4px;
      }
      
      .edit-btn {
        color: #3182ce;
      }
      
      .edit-btn:hover {
        background-color: #ebf8ff;
      }
      
      .delete-btn {
        color: #e53e3e;
      }
      
      .delete-btn:hover {
        background-color: #fff5f5;
      }
     </style>
  </head>
  <body>
    <div class="desktop-frame">
      <div class="sidebar">
        <div class="sidebar-logo">
          <img
            src="../../../public/images/logo/cicatriza_semnada.png"
            alt="Logo"
            class="logo-small"
          />
          <h3 class="sidebar-title">Cicatriza+</h3>
        </div>
        <ul class="sidebar-menu">
          <a href="menu-principal-agente.html">
            <li class="sidebar-item">
              <span class="sidebar-icon">🏠</span>Início
            </li>
          </a>
          </a>
          <a href="gerenciar-pacientes.html">
            <li class="sidebar-item">
              <span class="sidebar-icon">👥</span>Pacientes
            </li>
          </a>
          <a href="tutorial-agente.html">
            <li class="sidebar-item">
              <span class="sidebar-icon">📝</span>Tutorial
            </li>
          </a>
          <a href="perfil-agente.html">
            <li class="sidebar-item">
              <span class="sidebar-icon">👤</span>Perfil
            </li>
          </a>
          <a href="notificacoes-agente.html" class="sidebar-link">
            <li class="sidebar-item active">
              <span class="sidebar-icon">🔔</span>Notificações
            </li>
          </a>
          <a href="emergencia-agente.html">
            <li class="sidebar-item">
              <span class="sidebar-icon">⚠️</span>Emergência
            </li>
          </a>
        </ul>
      </div>
      <div class="desktop-content">
        <div class="desktop-header">
          <div class="header-title">Notificações do Profissional</div>
        </div>
        <div class="main-area" style="text-align: left;">
          <div class="notification-section">
            <h3 class="section-title">Alertas de Pacientes</h3>
          </div>
          
          <div class="notification-section" style="margin-top: 30px;">
            <h3 class="section-title">Notificações Enviadas</h3>
            <div id="sent-notifications-container">
              <div class="loading-indicator">Carregando notificações enviadas...</div>
            </div>
          </div>
        </div>
        
        <button class="add-notification-btn" id="addNotificationBtn">+</button>
        
        <div class="modal-overlay" id="notificationModal">
          <div class="modal-content">
            <div class="modal-header">
              <div class="modal-title">Nova Notificação</div>
              <button class="modal-close" id="modalCloseBtn">&times;</button>
            </div>
            
            <form id="notification-form">
              <div class="form-group">
                <label class="form-label" for="notification-type">Tipo de Notificação</label>
                <select class="form-input" id="notification-type" required>
                  <option value="">Selecione um tipo</option>
                  <option value="Lembrete">Lembrete</option>
                  <option value="Aviso">Aviso</option>
                  <option value="Alerta">Alerta</option>
                </select>
              </div>
              
              <div class="form-group">
                <label class="form-label" for="notification-content">Conteúdo</label>
                <textarea class="form-input form-textarea" id="notification-content" 
                  placeholder="Digite o conteúdo da notificação" required></textarea>
              </div>
              
              <div class="form-group">
                <label class="form-label" for="patient-select">Paciente</label>
                <select class="form-input" id="patient-select" required>
                  <option value="">Selecione o paciente</option>
                </select>
              </div>
              
              <div class="form-actions">
                <button type="button" class="button outline" id="cancelBtn">Cancelar</button>
                <button type="submit" class="button">Enviar Notificação</button>
              </div>
            </form>
          </div>
        </div>

        <div class="modal-overlay" id="editNotificationModal">
          <div class="modal-content">
            <div class="modal-header">
              <div class="modal-title">Editar Notificação</div>
              <button class="modal-close" id="editModalCloseBtn">&times;</button>
            </div>
            
            <form id="edit-notification-form">
              <input type="hidden" id="edit-notification-id">
              
              <div class="form-group">
                <label class="form-label" for="edit-notification-type">Tipo de Notificação</label>
                <select class="form-input" id="edit-notification-type" required>
                  <option value="">Selecione um tipo</option>
                  <option value="Lembrete">Lembrete</option>
                  <option value="Aviso">Aviso</option>
                  <option value="Alerta">Alerta</option>
                </select>
              </div>
              
              <div class="form-group">
                <label class="form-label" for="edit-notification-content">Conteúdo</label>
                <textarea class="form-input form-textarea" id="edit-notification-content" 
                  placeholder="Digite o conteúdo da notificação" required></textarea>
              </div>
              
              <div class="form-actions">
                <button type="button" class="button outline" id="editCancelBtn">Cancelar</button>
                <button type="submit" class="button">Salvar Alterações</button>
              </div>
            </form>
          </div>
        </div>

        <div class="footer-desktop">
          <div class="footer-left">
            Menu - início - Perfil - Login - Contate - nos
          </div>
          <div class="footer-right">Cicatriza+</div>
        </div>
      </div>
    </div>

    <div class="mobile-navbar">
      <div class="mobile-nav-container">
        <a href="menu-principal-agente.html" class="mobile-nav-item">
          <div class="mobile-nav-icon">🏠</div>
          <div class="mobile-nav-text">Início</div>
        </a>
        <a href="gerenciar-pacientes.html" class="mobile-nav-item">
          <div class="mobile-nav-icon">👥</div>
          <div class="mobile-nav-text">Pacientes</div>
        </a>
        <a href="notificacoes-agente.html" class="mobile-nav-item">
          <div class="mobile-nav-icon">🔔</div>
          <div class="mobile-nav-text">Notificações</div>
        </a>
        <a href="perfil-agente.html" class="mobile-nav-item">
          <div class="mobile-nav-icon">👤</div>
          <div class="mobile-nav-text">Perfil</div>
        </a>
      </div>
    </div>
    
    <div vw class="enabled">
      <div vw-access-button class="active"></div>
      <div vw-plugin-wrapper>
        <div class="vw-plugin-top-wrapper"></div>
      </div>
    </div>
    <script src="https://vlibras.gov.br/app/vlibras-plugin.js"></script>
    <script>
      new window.VLibras.Widget("https://vlibras.gov.br/app");
      
      const addNotificationBtn = document.getElementById('addNotificationBtn');
      const notificationModal = document.getElementById('notificationModal');
      const modalCloseBtn = document.getElementById('modalCloseBtn');
      const cancelBtn = document.getElementById('cancelBtn');
      const patientSelect = document.getElementById('patient-select');
      const notificationForm = document.getElementById('notification-form');
      const editNotificationModal = document.getElementById('editNotificationModal');
      const editModalCloseBtn = document.getElementById('editModalCloseBtn');
      const editCancelBtn = document.getElementById('editCancelBtn');
      const editNotificationForm = document.getElementById('edit-notification-form');
      const editNotificationId = document.getElementById('edit-notification-id');
      const editNotificationType = document.getElementById('edit-notification-type');
      const editNotificationContent = document.getElementById('edit-notification-content');
      
      let patientsMap = {};
      
      addNotificationBtn.addEventListener('click', () => {
        notificationModal.style.display = 'flex';
        loadPatients();
      });
      
      modalCloseBtn.addEventListener('click', () => {
        notificationModal.style.display = 'none';
      });
      
      cancelBtn.addEventListener('click', () => {
        notificationModal.style.display = 'none';
      });
      
      notificationModal.addEventListener('click', (e) => {
        if (e.target === notificationModal) {
          notificationModal.style.display = 'none';
        }
      });
      
      editModalCloseBtn.addEventListener('click', () => {
        editNotificationModal.style.display = 'none';
      });
      
      editCancelBtn.addEventListener('click', () => {
        editNotificationModal.style.display = 'none';
      });
      
      editNotificationModal.addEventListener('click', (e) => {
        if (e.target === editNotificationModal) {
          editNotificationModal.style.display = 'none';
        }
      });

      async function loadPatients() {
        try {
          const response = await fetch('/api/patients');
          if (!response.ok) {
            throw new Error('Erro ao carregar pacientes');
          }
          
          const patients = await response.json();
          patientSelect.innerHTML = '<option value="">Selecione o paciente</option>';
          
          patientsMap = {};
          
          patients.forEach(patient => {
            const option = document.createElement('option');
            option.value = patient.id;
            
            const formattedCpf = patient.cpf.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, "$1.$2.$3-$4");
            
            option.textContent = `${patient.full_name} - CPF: ${formattedCpf}`;
            
            patientsMap[patient.id] = {
              name: patient.full_name,
              cpf: formattedCpf
            };
            
            patientSelect.appendChild(option);
          });
        } catch (error) {
          console.error('Erro ao carregar lista de pacientes:', error);
          patientSelect.innerHTML = '<option value="">Erro ao carregar pacientes</option>';
        }
      }
      
      function getUserFromCookie() {
        const match = document.cookie.match('(^|;)\\s*user=([^;]+)');
        return match ? JSON.parse(decodeURIComponent(match[2])) : null;
      }
      
      notificationForm.addEventListener('submit', async (e) => {
        e.preventDefault();
        
        const notificationType = document.getElementById('notification-type').value;
        const notificationContent = document.getElementById('notification-content').value;
        const patientId = document.getElementById('patient-select').value;
        
        const now = new Date().toISOString();
        
        const notification = {
          type: notificationType, 
          content: notificationContent,
          creation_date: now,
          sent_date: now, 
          viewed: false,
          view_date: null,
          patient_id: parseInt(patientId)
        };
        
        console.log('Enviando notificação:', notification);
        
        try {
          const response = await fetch('/api/system-notifications', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(notification),
          });
          
          if (!response.ok) {
            const errorText = await response.text();
            console.error('Resposta do servidor:', errorText);
            throw new Error('Erro ao enviar notificação: ' + errorText);
          }
          
          // Adicionar notificação à lista de enviadas
          const sentNotification = await response.json();
          addNotificationToList(sentNotification);
          
          notificationModal.style.display = 'none';
          notificationForm.reset();
          
          alert('Notificação enviada com sucesso!');
        } catch (error) {
          console.error('Erro detalhado ao enviar notificação:', error);
          alert('Erro ao enviar notificação. Por favor, verifique o console para detalhes.');
        }
      });
      
      editNotificationForm.addEventListener('submit', async (e) => {
        e.preventDefault();
        
        const notificationId = editNotificationId.value;
        const updatedNotification = {
          type: editNotificationType.value,
          content: editNotificationContent.value
        };
        
        try {
          const response = await fetch(`/api/system-notifications/${notificationId}`, {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(updatedNotification),
          });
          
          if (!response.ok) {
            const errorText = await response.text();
            console.error('Resposta do servidor:', errorText);
            throw new Error('Erro ao atualizar notificação: ' + errorText);
          }
          
          editNotificationModal.style.display = 'none';
          
          loadNotifications();
          
          alert('Notificação atualizada com sucesso!');
        } catch (error) {
          console.error('Erro detalhado ao atualizar notificação:', error);
          alert('Erro ao atualizar notificação. Por favor, verifique o console para detalhes.');
        }
      });
      
      async function deleteNotification(notificationId) {
        if (!confirm('Tem certeza que deseja excluir esta notificação?')) {
          return;
        }
        
        try {
          const response = await fetch(`/api/system-notifications/${notificationId}`, {
            method: 'DELETE'
          });
          
          if (!response.ok) {
            const errorText = await response.text();
            console.error('Resposta do servidor:', errorText);
            throw new Error('Erro ao excluir notificação: ' + errorText);
          }
          
          loadNotifications();
          
          alert('Notificação excluída com sucesso!');
        } catch (error) {
          console.error('Erro ao excluir notificação:', error);
          alert('Erro ao excluir notificação. Por favor, verifique o console para detalhes.');
        }
      }
      
      function addNotificationToList(notification) {
        const container = document.getElementById('sent-notifications-container');
        
        const loadingIndicator = container.querySelector('.loading-indicator');
        if (loadingIndicator) {
          loadingIndicator.remove();
        }
        
        const notificationCard = document.createElement('div');
        notificationCard.className = 'card notification-card';
        notificationCard.id = `notification-${notification.id}`;
        
        let icon = '🔔';
        if (notification.type === 'Alerta') icon = '⚠️';
        if (notification.type === 'Lembrete') icon = '📅';
        if (notification.type === 'Aviso') icon = '📢';
        
        const patientInfo = patientsMap[notification.patient_id] || {};
        const patientName = patientInfo.name || `Paciente #${notification.patient_id}`;
        const patientCpf = patientInfo.cpf || '';
        
        const patientDisplay = patientCpf 
          ? `${patientName} - CPF: ${patientCpf}`
          : patientName;
        
        notificationCard.innerHTML = `
          <div class="notification-content">
            <div class="notification-icon">${icon}</div>
            <div class="notification-details">
              <div class="notification-title">
                ${notification.type}: Para ${patientDisplay}
              </div>
              <div class="notification-message">
                ${notification.content}
              </div>
              <div class="notification-time">
                Enviado em ${new Date(notification.sent_date).toLocaleString()}
                <span class="notification-status status-success">
                  • ${notification.viewed ? 'Visualizado' : 'Não visualizado'}
                </span>
              </div>
              <div class="notification-action-buttons">
                <button class="notification-action-btn edit-btn" onclick="editNotificationHandler(${notification.id})">
                  ✏️ Editar
                </button>
                <button class="notification-action-btn delete-btn" onclick="deleteNotificationHandler(${notification.id})">
                  🗑️ Excluir
                </button>
              </div>
            </div>
          </div>
        `;
        
        container.insertBefore(notificationCard, container.firstChild);
      }
      
      function openEditModal(notification) {
        editNotificationId.value = notification.id;
        editNotificationType.value = notification.type;
        editNotificationContent.value = notification.content;
        
        editNotificationModal.style.display = 'flex';
      }
      
      function editNotificationHandler(notificationId) {
        fetch(`/api/system-notifications/${notificationId}`)
          .then(response => {
            if (!response.ok) throw new Error('Erro ao buscar notificação');
            return response.json();
          })
          .then(notification => {
            openEditModal(notification);
          })
          .catch(error => {
            console.error('Erro ao buscar notificação para edição:', error);
            alert('Erro ao buscar dados da notificação. Por favor, tente novamente.');
          });
      }
      
      function deleteNotificationHandler(notificationId) {
        deleteNotification(notificationId);
      }
      
      async function loadNotifications() {
        try {
          console.log('Tentando carregar notificações...');
          
          const patientsResponse = await fetch('/api/patients');
          if (patientsResponse.ok) {
            const patients = await patientsResponse.json();
            patients.forEach(patient => {
              const formattedCpf = patient.cpf.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, "$1.$2.$3-$4");
              patientsMap[patient.id] = {
                name: patient.full_name,
                cpf: formattedCpf
              };
            });
          }
          
          const response = await fetch('/api/system-notifications');
          console.log('Resposta da API:', response.status);
          
          if (!response.ok) {
            const errorText = await response.text();
            console.error('Detalhes do erro:', errorText);
            throw new Error('Erro ao carregar notificações: ' + errorText);
          }
          
          const notifications = await response.json();
          console.log('Notificações carregadas:', notifications);
          
          const container = document.getElementById('sent-notifications-container');
          container.innerHTML = '';
          
          if (notifications.length === 0) {
            container.innerHTML = '<div class="empty-state">Nenhuma notificação enviada ainda.</div>';
            return;
          }
          
          notifications.sort((a, b) => new Date(b.sent_date) - new Date(a.sent_date));
          
          notifications.forEach(notification => {
            addNotificationToList(notification);
          });
        } catch (error) {
          console.error('Erro detalhado ao carregar notificações enviadas:', error);
          document.getElementById('sent-notifications-container').innerHTML = 
            '<div class="error-state">Erro ao carregar notificações. Verifique o console para detalhes.</div>';
        }
      }
      window.addEventListener('DOMContentLoaded', async () => {
        await loadNotifications();
      });
    </script>
  </body>
</html>

