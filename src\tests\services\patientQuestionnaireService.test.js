const PatientQuestionnaireService = require ("../../services/patientQuestionnaireService");

describe("PatientQuestionnaireService", () => {
  const mockRepository ={
    findAll: jest.fn(),
    findById: jest.fn(),
    create: jest.fn(),
    update: jest.fn()
    };

  const service = new PatientQuestionnaireService(mockRepository);
  
  const patientQuestionnaireMock = [
  {
    completion_date:"2023-10-01T00:00:00.000Z",
    pain_level: 3,
    has_discharge: true,
    odor: "normal",
    has_fever: false,
    surrounding_redness: false,
    swelling: true,
    other_symptoms: "none",
    applied_recommended_dressing: true,
    treatment_difficulties: "none",
    patient_id: 1
  },
  {
    completion_date:"2023-10-02T00:00:00.000Z",
    pain_level: 2,
    has_discharge: false,
    odor: "mild",
    has_fever: true,
    surrounding_redness: true,
    swelling: false,
    other_symptoms: "headache",
    applied_recommended_dressing: false,
    treatment_difficulties: "none",
    patient_id: 2
  }
];

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("deve retornar todos os questionários", async () => {
    mockRepository.findAll.mockResolvedValue(patientQuestionnaireMock);
    const result = await service.findAll();

    expect(mockRepository.findAll).toHaveBeenCalledTimes(1);
    expect(result).toEqual(patientQuestionnaireMock);
  });

  it("deve retornar um questionário pelo ID", async () => {
    const questionnaire = patientQuestionnaireMock[0];
    mockRepository.findById.mockResolvedValue(questionnaire);
    
    const result = await service.findById(1);

    expect(mockRepository.findById).toHaveBeenCalledWith(1);
    expect(result).toEqual(questionnaire);
  });

  it("deve lançar um erro se o questionário não for encontrado", async () => {
    mockRepository.findById.mockResolvedValue(null);

    await expect(service.findById(999)).rejects.toThrow("Questionário não encontrado");
    expect(mockRepository.findById).toHaveBeenCalledWith(999);
  });

  it("deve criar um novo questionário", async () => {
    const newQuestionnaire = patientQuestionnaireMock[0];
    mockRepository.create.mockResolvedValue(newQuestionnaire);

    const result = await service.create(newQuestionnaire);

    expect(mockRepository.create).toHaveBeenCalledWith(newQuestionnaire);
    expect(result).toEqual(newQuestionnaire);
  });

  it("deve lançar um erro se os dados do questionário forem inválidos", async () => {
    const invalidQuestionnaire = { ...patientQuestionnaireMock[0], pain_level: 6 };

    await expect(service.create(invalidQuestionnaire)).rejects.toThrow();
    expect(mockRepository.create).not.toHaveBeenCalled();
  });

  it("deve atualizar um questionário existente", async () => {
    const updatedQuestionnaire = { ...patientQuestionnaireMock[0], pain_level: 4 };
    mockRepository.update.mockResolvedValue(updatedQuestionnaire);

    const result = await service.update(1, updatedQuestionnaire);

    expect(mockRepository.update).toHaveBeenCalledWith(1, updatedQuestionnaire);
    expect(result).toEqual(updatedQuestionnaire);
  });

  it("deve lançar um erro ao atualizar com dados inválidos", async () => {
    const invalidUpdate = { ...patientQuestionnaireMock[0], pain_level: 6 };

    await expect(service.update(1, invalidUpdate)).rejects.toThrow();
    expect(mockRepository.update).not.toHaveBeenCalled();
  });
});