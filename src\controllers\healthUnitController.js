class HealthUnitController {
  constructor(service) {
    this.service = service;
  }

  async index(req, res) {
    try {
        const data = await this.service.findAll();
        res.json(data); 
    } catch (err) {
        console.error('[HealthUnitController] - Erro na ubs:', err.message);
        res.status(400).send(err.message);
    }
  }

  async show(req, res) {
    try {
      const data = await this.service.findById(req.params.id);
      res.json(data);
    } catch (err) {
      console.error('[HealthUnitController] - Erro ao encontar ubs:', err.message);
      res.status(404).send(err.message);
    }
  }

}

module.exports = HealthUnitController;
