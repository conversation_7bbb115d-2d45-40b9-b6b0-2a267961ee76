const express = require('express');
const router = express.Router();

const WoundsRepository = require('../repositories/woundsRepository');
const WoundsService = require('../services/woundsService');
const WoundsController = require('../controllers/woundsController');

const controller = new WoundsController(new WoundsService(new WoundsRepository()));
router.get('/', controller.index.bind(controller));
router.get('/:id', controller.show.bind(controller));
router.post('/', controller.create.bind(controller));
router.put('/:id', controller.update.bind(controller));
router.delete('/:id', controller.delete.bind(controller));

module.exports = router;
