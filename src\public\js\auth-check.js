/**
 * Script de verificação de autenticação
 * Este script deve ser o PRIMEIRO elemento no head para garantir que
 * a verificação ocorra antes de qualquer renderização da página.
 */
(function () {
  function getCookie(name) {
    const v = document.cookie.match("(^|;) ?" + name + "=([^;]*)(;|$)");
    return v ? decodeURIComponent(v[2]) : null;
  }

  function getDebugInfo() {
    
    return {
      cookies: document.cookie.split(";").map((c) => c.trim()),
      currentPath: window.location.pathname,
      href: window.location.href,
      userAgent: navigator.userAgent,
      timestamp: new Date().toString(),
    };
  }

  try {
    console.log("🔍 Verificando autenticação...");

    const token = getCookie("authToken");
    const userJson = getCookie("user");
    const currentPath = window.location.pathname;

    console.log("Token encontrado:", !!token);
    console.log("Dados do usuário encontrados:", !!userJson);
    console.log("Caminho atual:", currentPath);

    
    const isProf = currentPath.includes("/profissional_saude/");
    const isPac = currentPath.includes("/paciente/");

    
    if (!isProf && !isPac) {
      console.log("Área não protegida, pulando verificação");
      return;
    }

    
    if (currentPath.includes("login")) {
      console.log("Página de login, pulando verificação");
      return;
    }

    
    const loginPageProf = "/views/profissional_saude/login-agente.html";
    const loginPagePac = "/views/paciente/login-paciente.html";

    const loginPage = isProf ? loginPageProf : loginPagePac;
    console.log("Página de login definida:", loginPage);

    
    if (!token || !userJson) {
      console.log(
        "🚫 Token ou dados do usuário ausentes, redirecionando para login"
      );
      document.cookie =
        "redirectAfterLogin=" +
        encodeURIComponent(window.location.href) +
        ";path=/";
      
      window.location.href = loginPage;
      return;
    }

    try {
      const user = JSON.parse(userJson);
      console.log("Usuário decodificado:", user);

      
      const tipo =
        user.tipo_usuario || (user.tipo_agente ? "agente" : "paciente");
      console.log("Tipo de usuário:", tipo);

      
      if ((isProf && tipo !== "agente") || (isPac && tipo !== "paciente")) {
        console.log("Tipo de usuário não corresponde à área, redirecionando");
        window.location.href = loginPage;
        return;
      }

      
      if (!user.id) {
        console.log("ID do usuário ausente, redirecionando");
        document.cookie = "authToken=;path=/;max-age=0";
        document.cookie = "user=;path=/;max-age=0";
        window.location.href = loginPage;
        return;
      }

      console.log("✅ Usuário autenticado com sucesso!");

      
      const userCookie = getCookie("user");
      if (userCookie) {
        console.log("Renovando cookies de autenticação");
        document.cookie =
          "user=" + encodeURIComponent(userCookie) + ";path=/;max-age=86400"; 
        document.cookie =
          "authToken=" + encodeURIComponent(token) + ";path=/;max-age=86400"; 
      }
    } catch (error) {
      console.error(
        "Erro ao processar dados do usuário:",
        error,
        getDebugInfo()
      );
      document.cookie = "authToken=;path=/;max-age=0";
      document.cookie = "user=;path=/;max-age=0";
      window.location.href = loginPage;
    }
  } catch (error) {
    console.error(
      "Erro na verificação de autenticação:",
      error,
      getDebugInfo()
    );
    
    
  }
})();
