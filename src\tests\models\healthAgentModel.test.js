const HealthAgentModel = require('../../models/healthAgentModel');

describe('HealthAgentModel Schema', () => {
  const validData = {
    full_name: '<PERSON>a<PERSON> Cam<PERSON>uza',
    type: '<PERSON>é<PERSON><PERSON>',
    agent_registration: 'REG12345',
    phone: '11999999999',
    email: '<EMAIL>',
    cpf: '12345678901',
    password: 'senhaSegura123',
    health_unit_id: 1
  };

  it('deve validar dados corretos com sucesso', () => {
    const { error } = HealthAgentModel.schema.validate(validData);
    expect(error).toBeUndefined();
  });

  it('deve falhar se full_name estiver ausente', () => {
    const invalidData = { ...validData };
    delete invalidData.full_name;

    const { error } = HealthAgentModel.schema.validate(invalidData);
    expect(error).toBeDefined();
    expect(error.details[0].path).toContain('full_name');
  });

  it('deve falhar se type for inválido', () => {
    const invalidData = { ...validData, type: 'Fisioterapeuta' };

    const { error } = HealthAgentModel.schema.validate(invalidData);
    expect(error).toBeDefined();
    expect(error.details[0].path).toContain('type');
  });

  it('deve falhar se cpf tiver menos de 11 dígitos', () => {
    const invalidData = { ...validData, cpf: '12345678' };

    const { error } = HealthAgentModel.schema.validate(invalidData);
    expect(error).toBeDefined();
    expect(error.details[0].path).toContain('cpf');
  });

  it('deve falhar se cpf tiver letras', () => {
    const invalidData = { ...validData, cpf: 'abc45678901' };

    const { error } = HealthAgentModel.schema.validate(invalidData);
    expect(error).toBeDefined();
    expect(error.details[0].path).toContain('cpf');
  });

  it('deve falhar se health_unit_id não for inteiro', () => {
    const invalidData = { ...validData, health_unit_id: 'um' };

    const { error } = HealthAgentModel.schema.validate(invalidData);
    expect(error).toBeDefined();
    expect(error.details[0].path).toContain('health_unit_id');
  });

  it('deve falhar se algum campo obrigatório estiver vazio', () => {
    const invalidData = { ...validData, password: '' };

    const { error } = HealthAgentModel.schema.validate(invalidData);
    expect(error).toBeDefined();
    expect(error.details[0].path).toContain('password');
  });
});