<!DOCTYPE html>
<html lang="pt-BR">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100..900;1,100..900&display=swap"
      rel="stylesheet"
    />
    <title>Cicatriza+ - Login</title>
    <link rel="stylesheet" href="../../../public/css/global.css" />
    <script src="../../../public/js/auth.js"></script>
    <style>
      a:visited,
      a:active {
        color: inherit !important;
        text-decoration: none !important;
      }
      .btn-login {
        width: 100%;
        padding: 10px 15px;
        background-color: #3498db;
        color: white;
        border: none;
        border-radius: 50px;
        cursor: pointer;
        font-size: 16px;
        margin-bottom: 15px;
      }
      .btn-login:hover {
        background-color: #2980b9;
      }
      .error-message {
        color: #e74c3c;
        text-align: center;
        margin: 10px 0;
        display: none;
      }

      .signup,
      .signup:visited,
      .signup:active {
        color: white !important;
        text-decoration: none !important;
      }
      .signup:hover {
        transform: scale(1.02);
        color: white !important;
        background-color: #2980b9;
      }

      /* Fix for desktop display */
      @media (min-width: 769px) {
        .container {
          min-height: 100vh !important;
          padding: 40px !important;
        }

        .login-card {
          max-width: 600px !important;
          width: 100% !important;
          margin: 0 auto !important;
          min-height: auto !important;
        }

        .logo-section {
          padding: 30px 20px 20px !important;
          text-align: center !important;
        }

        .logo-placeholder {
          border: none !important;
          background: transparent !important;
          display: inline-block !important;
        }

        .logo {
          width: 120px !important;
          height: 120px !important;
          object-fit: contain !important;
          display: block !important;
        }

        .main-title {
          font-size: 36px !important;
          margin: 20px 0 10px !important;
          text-align: center !important;
        }

        .subtitle {
          font-size: 18px !important;
          margin-bottom: 40px !important;
          text-align: center !important;
          color: #718096 !important;
        }

        .login-form {
          padding: 0 50px 50px !important;
        }

        .form-group {
          margin-bottom: 30px !important;
        }

        .form-group input {
          padding: 18px 24px !important;
          font-size: 16px !important;
        }

        .btn-login {
          padding: 20px !important;
          font-size: 18px !important;
          margin-bottom: 25px !important;
        }

        .footer {
          padding: 20px 30px !important;
          display: flex !important;
          justify-content: space-between !important;
          background-color: #f8fafc !important;
          border-top: 1px solid #e2e8f0 !important;
        }
      }

      /* Ensure mobile doesn't interfere */
      @media (max-width: 768px) {
        .login-card {
          max-width: 100% !important;
          margin: 10px !important;
        }

        .logo-placeholder {
          border: none !important;
          background: transparent !important;
        }

        .logo {
          object-fit: contain !important;
          display: block !important;
        }

        .subtitle {
          text-align: center !important;
        }
      }

      /* Global logo fixes */
      .logo-placeholder {
        border: none !important;
        background: transparent !important;
        width: auto !important;
        height: auto !important;
      }

      .logo {
        object-fit: contain !important;
        display: block !important;
        margin: 0 auto !important;
      }

      .subtitle {
        text-align: center !important;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="login-card">
        <div class="logo-section">
          <div class="logo-placeholder">
            <img
              class="logo"
              src="../../../public/images/logo/cicatriza_semnada.png"
              alt="Logo do Cicatriza+"
            />
          </div>
        </div>

        <h2 class="main-title">Cicatriza+</h2>
        <p class="subtitle">Área do Paciente</p>

        <form class="login-form" id="loginForm">
          <div class="form-group">
            <div class="labelgroup">
              <label for="identifier">Email ou CPF</label>
            </div>
            <input
              type="text"
              id="identifier"
              name="identifier"
              placeholder="Digite seu Email ou CPF"
              required
            />
          </div>

          <div class="form-group">
            <div class="labelgroup"><label for="password">Senha</label></div>
            <input
              type="password"
              id="password"
              name="password"
              placeholder="Digite sua senha"
              required
            />
          </div>

          <button type="submit" class="btn-login">Entrar</button>

          <div id="error-message" class="error-message"></div>

          <a href="#" class="forgot-password">Esqueci minha senha</a>

          <br />
          <br />
          <p>Não tem uma conta?</p>
          <a href="../paciente/cadastro-paciente.html" class="signup"
            >Cadastrar</a
          >
        </form>

        <script>
          document
            .getElementById("loginForm")
            .addEventListener("submit", async function (e) {
              e.preventDefault();

              const identifier = document.getElementById("identifier").value;
              const password = document.getElementById("password").value;
              const errorMessage = document.getElementById("error-message");

              errorMessage.style.display = "none";

              try {
                const response = await fetch("/api/patients/login", {
                  method: "POST",
                  headers: {
                    "Content-Type": "application/json",
                  },
                  body: JSON.stringify({
                    identifier,
                    password,
                  }),
                });

                const data = await response.json();

                if (!response.ok) {
                  throw new Error(data.error || "Erro ao fazer login");
                }
    
                document.cookie = "authToken=" + data.token + ";path=/";
                document.cookie =
                  "user=" + JSON.stringify(data.user) + ";path=/";

                const redirect = document.cookie.match(
                  "(^|;) ?redirectAfterLogin=([^;]*)(;|$)"
                );
                if (redirect) {
                  window.location.href = decodeURIComponent(redirect[2]);
                } else {
                  window.location.href = "menu-principal.html";
                }
              } catch (error) {
                errorMessage.textContent = error.message;
                errorMessage.style.display = "block";
                console.error("Login error:", error);
              }
            });
        </script>

        <div class="footer">
          <div class="footer-left">
            Lorem ipsum dolor sit amet consectetur adip
          </div>
          <div class="footer-right">CICATRIZA+</div>
        </div>
      </div>
    </div>
    <div vw class="enabled">
      <div vw-access-button class="active"></div>
      <div vw-plugin-wrapper>
        <div class="vw-plugin-top-wrapper"></div>
      </div>
    </div>
    <script src="https://vlibras.gov.br/app/vlibras-plugin.js"></script>
    <script>
      new window.VLibras.Widget("https://vlibras.gov.br/app");
    </script>
  </body>
</html>
