const express = require('express');
const router = express.Router();

const WoundMonitoringRepository = require('../repositories/woundMonitoringRepository');
const WoundMonitoringService = require('../services/woundMonitoringService');
const WoundMonitoringController = require('../controllers/woundMonitoringController');

const controller = new WoundMonitoringController(new WoundMonitoringService(new WoundMonitoringRepository()));
router.get('/', controller.index.bind(controller));
router.get('/:id', controller.show.bind(controller));
router.post('/', controller.create.bind(controller));
router.put('/:id', controller.update.bind(controller));
router.delete('/:id', controller.delete.bind(controller));

module.exports = router;
