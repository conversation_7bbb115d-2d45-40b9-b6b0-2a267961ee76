const WoundsService = require("../../services/woundsService");

describe("WoundsService", () => {
  const mockRepository = {
    findAll: jest.fn(),
    findById: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
  };

  const service = new WoundsService(mockRepository);

  const woundsMock = [
    {
      wound_type: "Corte profundo na perna",
      anatomical_location: "perna",
      first_occurrence_date: "2025-05-27T08:30:00Z",
      severity_level: 1,
      status: "ativa",
      registration_date: "2025-05-28T10:00:00Z",
      patient_id: 1,
    },
    {
      wound_type: "Corte leve na perna",
      anatomical_location: "perna",
      first_occurrence_date: "2025-05-26T09:00:00Z",
      severity_level: 3,
      status: "em cicatrização",
      registration_date: "2025-05-28T10:00:00Z",
      patient_id: 2,
    },
  ];

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('deve retornar a lista de ferimentos do repositorio', async () => {
    mockRepository.findAll.mockResolvedValue(woundsMock);

    const resultado = await service.findAll();

    expect(mockRepository.findAll).toHaveBeenCalledTimes(1);
    expect(resultado).toEqual(woundsMock);
  });

  it('deve retornar um ferimento pelo ID', async () => {
    mockRepository.findById.mockResolvedValue(woundsMock[0]);

    const resultado = await service.findById(1);

    expect(mockRepository.findById).toHaveBeenCalledTimes(1);
    expect(resultado).toEqual(woundsMock[0]);
  });

  it('deve criar um novo ferimento', async () => {
    mockRepository.create.mockResolvedValue(woundsMock[0]);
    
    const resultado = await service.create(woundsMock[0]);

    expect(mockRepository.create).toHaveBeenCalledTimes(1);
    expect(resultado).toEqual(woundsMock[0]);
  });

  it('deve lançar erro ao tentar criar ferimento com dados inválidos', async () => {
    const woundsInvalid = {
      anatomical_location: "perna",
      first_occurrence_date: "2025-05-27T08:30:00Z",
      severity_level: 1,
      status: "ativa",
      registration_date: "2025-05-28T10:00:00Z",
      patient_id: "1",
    };

    await expect(service.create(woundsInvalid))
    .rejects.toThrow();
  });

  it('deve atualizar um ferimento existente', async () => {
    mockRepository.update.mockResolvedValue(woundsMock[0]);

    const resultado = await service.update(1, woundsMock[0]);

    expect(mockRepository.update).toHaveBeenCalledTimes(1);
    expect(resultado).toEqual(woundsMock[0]);
  });

  it ('deve lançar um erro ao tentar atualizar um ferimento com dados inválidos', async () => {
    const woundsInvalid = {
      anatomical_location: "perna",
      first_occurrence_date: "2025-05-27T08:30:00Z",
      severity_level: 1,
      status: "cicatrizada",
      registration_date: "2025-05-28T10:00:00Z",
      patient_id: "1",
    };
    
    await expect(service.update(1, woundsInvalid))
    .rejects.toThrow();
  });

  it("deve deletar um ferimento existente", async () => {
    mockRepository.delete.mockResolvedValue(woundsMock[0]);

    const resultado = await service.delete(1);

    expect(mockRepository.delete).toHaveBeenCalledTimes(1);
    expect(resultado).toEqual(woundsMock[0]);
  });
});
