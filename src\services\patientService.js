const bcrypt = require("bcrypt");
const PatientModel = require("../models/patientModel");

class PatientService {
  constructor(repository) {
    this.repository = repository;
  }

  async findAll() {
    return await this.repository.findAll();
  }

  async findById(id) {
    return await this.repository.findById(id);
  }

  async create(data) {
    return await this.repository.create(data);
  }

  async update(id, data) {
    return await this.repository.update(id, data);
  }

  async delete(id) {
    return await this.repository.delete(id);
  }

  async register(patientData) {
    try {
      console.log("[PatientService] Registering new patient");
      const { error } = PatientModel.schema.validate(patientData);
      if (error) {
        console.error(
          "[PatientService] Validation error:",
          error.details[0].message
        );
        throw new Error(`Erro de validação: ${error.details[0].message}`);
      }

      let existingByEmail = null;
      let existingByCpf = null;

      try {
        existingByEmail = await this.repository.findByEmail(patientData.email);
        if (existingByEmail) {
          throw new Error("Email já cadastrado");
        }

        existingByCpf = await this.repository.findByCpf(patientData.cpf);
        if (existingByCpf) {
          throw new Error("CPF já cadastrado");
        }
      } catch (dbError) {

        if (dbError.message.includes("já cadastrado")) {
          throw dbError;
        }
        console.error(
          "[PatientService] Verificação de duplicidade ignorada:",
          dbError.message
        );
      }

      try {
        const salt = await bcrypt.genSalt(10);
        patientData.password = await bcrypt.hash(patientData.password, salt);
      } catch (hashError) {
        console.error(
          "[PatientService] Password hashing error:",
          hashError.message
        );
        throw new Error("Erro ao processar a senha");
      }

      const result = await this.repository.create(patientData);
      console.log(
        `[PatientService] Patient registered successfully with ID ${result.id}`
      );
      return result;
    } catch (error) {
      console.error("[PatientService] Registration error:", error.message);
      throw error;
    }
  }

  formatCpf(digits) {
    return digits.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, "$1.$2.$3-$4");
  }

  async authenticate(identifier, password) {
    try {
      let patient;
      if (identifier.includes("@")) {
        patient = await this.repository.findByEmail(identifier);
      } else {
        const digits = identifier.replace(/\D/g, "");

        patient =
          (await this.repository.findByCpf(identifier)) ||
          (await this.repository.findByCpf(this.formatCpf(digits)));
      }
      if (!patient) return null;

      let validPassword;
      if (patient.password.startsWith("$2")) {
        validPassword = await bcrypt.compare(password, patient.password);
      } else {
        validPassword = password === patient.password;
      }
      if (!validPassword) return null;

      return patient;
    } catch (error) {
      throw error;
    }
  }
}

module.exports = PatientService;
