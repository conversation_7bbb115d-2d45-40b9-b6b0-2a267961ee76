# Inteli - Instituto de Tecnologia e Liderança

<p align="center">
<a href= "https://www.inteli.edu.br/"><img src="/assets/inteli.png" alt="Inteli - Instituto de Tecnologia e Liderança" border="0"></a>
</p>

# Cicatriza+

<p align="center">
<img src="https://res.cloudinary.com/dwewomj84/image/upload/v1748874625/2_jwhxd0.png" alt="Inteli - Instituto de Tecnologia e Liderança" border="0">
</p>

## Backendigans

## :student: Integrantes:

- <a href="https://www.linkedin.com/in/george-kapelius-b54a6a297/"><PERSON></a>
- <a href="https://www.linkedin.com/in/kaian-moura-56b8871b4/"><PERSON><PERSON></a>
- <a href="https://www.linkedin.com/in/leonardo-lameda/"><PERSON></a>
- <a href="https://www.linkedin.com/in/raianearaujobrandao/">Raiane <PERSON>ujo <PERSON>ão</a>
- <a href="https://www.linkedin.com/in/raphael-fischer-skitnevsky-198b77345/">Raphael Fischer Skitnevsky</a>
- <a href="https://www.linkedin.com/in/stefanne-soares-9b31a8256/">Stefanne Victória Andrade Soares</a>
- <a href="https://www.linkedin.com/in/tobias-viana/">Tobias Viana Araújo</a>

## :teacher: Professores:

### Orientador(a)

- <a href="https://www.linkedin.com/in/laizaribeiro/">Laíza Ribeiro Silva</a>

### Instrutores

- <a href="https://www.linkedin.com/in/afonsolelis/">Afonso Cesar Lelis Brandão</a>
- <a href="https://www.linkedin.com/in/fernando-pizzo-208b526a/">Fernando Pizzo Ribeiro</a>
- <a href="https://www.linkedin.com/in/francisco-escobar/">Francisco de Souza Escobar</a>
- <a href="https://www.linkedin.com/in/marcelo-gon%C3%A7alves-phd-a550652/">Marcelo Luiz do Amaral Gonçalves</a>
- <a href="https://www.linkedin.com/in/natalia-k-37a62052/">Natalia Varela da Rocha Kloeckner</a>

## 📝 Descrição

O projeto desenvolvido por estudantes do Inteli em parceria com a Faculdade de Medicina da Universidade de São Paulo (FMUSP) propõe uma solução inovadora e humanizada para o acompanhamento de feridas crônicas, como úlceras por pressão, úlceras venosas ou de pé diabético. Essas condições impactam profundamente a qualidade de vida dos pacientes e representam desafios significativos ao sistema público de saúde, especialmente diante da dificuldade de mobilidade dos pacientes, da falta de educação em saúde e da sobrecarga das unidades básicas de atendimento.

A proposta consiste em uma plataforma web acessível, voltada para pacientes e profissionais da saúde, que permite o envio de imagens das feridas, o acompanhamento clínico remoto, além do acesso a materiais educativos em linguagem acessível. A plataforma oferece também notificações automatizadas, um botão de emergência e um sistema de prontuário digital, otimizando o fluxo de atendimento e promovendo maior engajamento dos pacientes com seu próprio tratamento.

No centro do projeto está o fortalecimento da autonomia do paciente, com foco na prevenção de complicações e na melhora da adesão ao tratamento. A solução foi cuidadosamente construída a partir de uma análise de contexto com o parceiro de projeto, envolvendo ferramentas como 5 Forças de Porter e Análise SWOT, e direcionada a públicos reais representados por personas como pacientes com mobilidade reduzida e enfermeiros com grande demanda de atendimento.

Do ponto de vista técnico, o sistema foi arquitetado seguindo o padrão MVC (Model-View-Controller), com um banco de dados relacional robusto, interface responsiva e recursos de acessibilidade. Foram desenvolvidos protótipos de alta fidelidade, wireframes, e um guia de estilo visual com paleta de cores, tipografia e iconografia adequados ao público-alvo, garantindo uma experiência acolhedora, segura e funcional.

Além da inovação tecnológica, o projeto se destaca pelo caráter socialmente responsável, com potencial de gerar impacto direto na vida de pessoas em situação de vulnerabilidade e nos custos do sistema público de saúde. A integração entre tecnologia, educação e cuidado humanizado transforma a plataforma em uma ferramenta poderosa para o enfrentamento das barreiras atuais no tratamento de feridas crônicas no Brasil.

## 📝 Link de demonstração

_Coloque aqui o link para seu projeto publicado e link para vídeo de demonstração_

## 📁 Estrutura de pastas

Dentre os arquivos e pastas presentes na raiz do projeto, definem-se:

- <b>assets</b>: aqui estão os arquivos relacionados a elementos não-estruturados deste repositório, como imagens.

- <b>document</b>: aqui estão todos os documentos do projeto, como o Web Application Document (WAD) bem como documentos complementares, na pasta "other".

- <b>src</b>: Todo o código fonte criado para o desenvolvimento do projeto de aplicação web.

- <b>README.md</b>: arquivo que serve como guia introdutório e explicação geral sobre o projeto e a aplicação (o mesmo arquivo que você está lendo agora).

## 💻 Configuração e Execução

Para executar este projeto em sua máquina local, siga as instruções abaixo:

### 1. Instalação

Clone o repositório e instale as dependências:

```sh
git clone https://github.com/Inteli-College/2025-1B-T19-IN02-G04.git
cd 2025-1B-T19-IN02-G04
npm install
```

### 2. Dependências

O projeto utiliza as seguintes dependências:
- **bcrypt** (^6.0.0) - Hash de senhas
- **connect-flash** (^0.1.1) - Mensagens flash no Express
- **dotenv** (^16.5.0) - Carregamento de variáveis de ambiente
- **ejs** (^3.1.10) - Template engine
- **express** (^5.1.0) - Framework web para Node.js
- **express-session** (^1.18.1) - Gerenciamento de sessões
- **joi** (^17.13.3) - Validação de dados
- **method-override** (^3.0.0) - Suporte a métodos HTTP PUT e DELETE
- **multer** (^2.0.0) - Upload de arquivos
- **pg** (^8.16.0) - Driver PostgreSQL para Node.js
- **nodemon** (^3.1.10) - Reinicialização automática do servidor (desenvolvimento)

### 3. Configuração do Ambiente

Crie um arquivo `.env` na raiz do projeto:

```
PORT=3000
DB_HOST=db.seu_projeto_id.supabase.co
DB_PORT=5432
DB_DATABASE=postgres
DB_USER=postgres
DB_PASSWORD=sua_senha_do_supabase
DB_SSL=true
```

**⚠️ Configuração do Supabase:**
1. Acesse seu projeto no [Supabase](https://supabase.com)
2. Vá em **Settings > Database**
3. Copie as informações de conexão
4. Teste a conexão: `npm run supabase:test`

### 4. Migrations

Para criar tabelas e inserir dados fictícios:

```sh
npm run migration [development|production]
# ou
node src/migrations/migration.js [development|production]
```

### 5. Execução do Servidor

```sh
# Modo produção
npm start

# Modo desenvolvimento (com hot reload)
npm run dev

# Acesse: http://localhost:3000
```

Para visualizar endpoints alternativos: `http://localhost:3000/index1.html`

## 🗃 Histórico de lançamentos

- ## 0.5.0 - 27/06/2025
- ## 0.4.0 - 13/06/2025
- ## 0.3.0 - 30/05/2025
- ## 0.2.0 - 16/05/2025
- ## 0.1.0 - 02/05/2025

## 📋 Licença/License

<a href="https://github.com/Inteli-College/2025-1B-T19-IN02-G04.git">Cicatriza+</a> © 2025 by <a href="https://github.com/Inteli-College/2025-1B-T19-IN02-G04.git">INTELI, George Mroginski Kapelius, Kaian Santos Moura, Leonardo Nicoli Lameda, Raiane Araujo Brandão, Raphael Fischer Skitnevsky, Stefanne Victória Andrade Soares, Tobias Viana Araújo</a> is licensed under <a href="https://creativecommons.org/licenses/by/4.0/">Creative Commons Attribution 4.0 International</a><img src="https://mirrors.creativecommons.org/presskit/icons/cc.svg" style="max-width: 1em;max-height:1em;margin-left: .2em;"><img src="https://mirrors.creativecommons.org/presskit/icons/by.svg" style="max-width: 1em;max-height:1em;margin-left: .2em;">
