const db = require("../config/db");

class PatientComorbidityRepository {
  async findAll() {
    const result = await db.query(
      "SELECT id, comorbidity_type, diagnosis_date, under_treatment, observations, patient_id FROM patient_comorbidity"
    );
    return result.rows;
  }

  async findById(id) {
    const query =
      "SELECT id, comorbidity_type, diagnosis_date, under_treatment, observations, patient_id FROM patient_comorbidity WHERE id = $1";
    const result = await db.query(query, [id]);
    if (result.rows.length === 0) {
      throw new Error("Comorbidade não encontrada");
    }
    return result.rows[0]; 
  }

  async create(comorbidity) {
    if (
      !comorbidity.comorbidity_type ||
      !comorbidity.diagnosis_date ||
      comorbidity.under_treatment === undefined ||
      !comorbidity.patient_id
    ) {
      throw new Error(
        "Campos obrigatórios não preenchidos: comorbidity_type, diagnosis_date, under_treatment e patient_id são obrigatórios"
      );
    }

    const query =
      "INSERT INTO patient_comorbidity (comorbidity_type, diagnosis_date, under_treatment, observations, patient_id) VALUES ($1, $2, $3, $4, $5) RETURNING *";
    const result = await db.query(query, [
      comorbidity.comorbidity_type,
      comorbidity.diagnosis_date,
      comorbidity.under_treatment,
      comorbidity.observations,
      comorbidity.patient_id,
    ]);

    return result.rows[0];
  }

  async update(id, comorbidity) {
    const query =
      "UPDATE patient_comorbidity SET comorbidity_type = $1, diagnosis_date = $2, under_treatment = $3, observations = $4, patient_id = $5 WHERE id = $6 RETURNING *";
    const result = await db.query(query, [
      comorbidity.comorbidity_type,
      comorbidity.diagnosis_date,
      comorbidity.under_treatment,
      comorbidity.observations,
      comorbidity.patient_id,
      id,
    ]);

    if (result.rowCount === 0) {
      throw new Error("Comorbidade não encontrada");
    }
    return result.rows[0];
  }

  async findByPatient(patientId) {
    const query =
      "SELECT id, comorbidity_type, diagnosis_date, under_treatment, observations, patient_id FROM patient_comorbidity WHERE patient_id = $1";
    const result = await db.query(query, [patientId]);
    return result.rows;
  }

  async findByPatientAndType(patientId, comorbidityType) {
    const query =
      "SELECT id, comorbidity_type, diagnosis_date, under_treatment, observations, patient_id FROM patient_comorbidity WHERE patient_id = $1 AND LOWER(comorbidity_type) = LOWER($2)";
    const result = await db.query(query, [patientId, comorbidityType]);
    return result.rows;
  }
}

module.exports = PatientComorbidityRepository;
