const PhotoRecordRepository = require('../../repositories/photoRecordRepository');
const db = require('../../config/db');

jest.mock('../../config/db');

describe('PhotoRecordRepository', () => {
  let repository;

  const photoRecordMock = [
    {
      id: 1,
      photo_date: '2025-05-27T08:30:00Z',
      file_path: '/path/to/photo1.jpg',
      wound_size_mm: 50.0,
      sent_by_patient: true,
      validated_by_agent: false,
      wound_id: 1,
      health_agent_id: 1,
      patient_id: 1
    },
    {
      id: 2,
      photo_date: '2025-05-28T09:00:00Z',
      file_path: '/path/to/photo2.jpg',
      wound_size_mm: 30.0,
      sent_by_patient: false,
      validated_by_agent: true,
      wound_id: 2,
      health_agent_id: 2,
      patient_id: 2
    }
  ];

  beforeEach(() => {
    repository = new PhotoRecordRepository();
    jest.clearAllMocks();
  });

  it('deve retornar todos os registros fotográficos (findAll)', async () => {
    db.query.mockResolvedValue({ rows: photoRecordMock });

    const result = await repository.findAll();

    expect(db.query).toHaveBeenCalledWith(
      expect.stringContaining('SELECT')
    );
    expect(result).toEqual(photoRecordMock);
  });

  it('deve retornar um registro fotográfico por ID (findById)', async () => {
    db.query.mockResolvedValue({ rows: [photoRecordMock[0]] });

    const result = await repository.findById(1);

    expect(db.query).toHaveBeenCalledWith(
        expect.stringContaining('SELECT'),
        [1]
    );
    expect(result).toEqual(photoRecordMock[0]);
  });

  it('deve lançar erro se o registro fotográfico não for encontrado (findById)', async () => {
    db.query.mockResolvedValue({ rows: [] });

    await expect(repository.findById(99)).rejects.toThrow('Registro de foto não encontrado');

    expect(db.query).toHaveBeenCalledWith(
        expect.stringContaining('SELECT'),
        [99]
    );
  });

  it('deve criar um novo registro fotográfico (create)', async () => {
    const dataToCreate = { ...photoRecordMock[0] };
    delete dataToCreate.id;

    db.query.mockResolvedValue({ rows: [dataToCreate] });

    const result = await repository.create(dataToCreate);

    expect(db.query).toHaveBeenCalledWith(
        expect.stringContaining('INSERT INTO photo_record'),
        expect.any(Array)
    );
    expect(result).toEqual(dataToCreate);
  });

  it('deve lançar erro se o registro fotográfico não for encontrado (create)', async () => {
    db.query.mockRejectedValue(new Error('Erro ao criar registro'));

    await expect(repository.create(photoRecordMock[0])).rejects.toThrow('Erro ao criar registro');
  });

  it('deve atualizar um registro fotográfico existente (update)', async () => {
    const updatedData = { ...photoRecordMock[0], photo_date: '2025-05-29T10:00:00Z' };

    db.query.mockResolvedValue({ rows: [updatedData] });

    const result = await repository.update(1, updatedData);

    expect(db.query).toHaveBeenCalledWith(
        expect.stringContaining('UPDATE photo_record'),
        expect.any(Array)
    );
    expect(result).toEqual(updatedData);
  });

  it('deve lançar erro se o registro fotográfico não for encontrado (update)', async () => {
    db.query.mockResolvedValue({ rowCount: 0, rows: [] });

    await expect(repository.update(99, photoRecordMock[0])).rejects.toThrow('Registro de foto não encontrado');
  });

  it('deve excluir um registro fotográfico existente (delete)', async () => {
    db.query.mockResolvedValue({ rows: [photoRecordMock[0]] });

    const result = await repository.delete(1);

    expect(db.query).toHaveBeenCalledWith(
        expect.stringContaining('DELETE FROM photo_record WHERE id = $1'),
        [1]
    );
    expect(result).toEqual(photoRecordMock[0]);
  });

  it('deve lançar erro se o registro fotográfico não for encontrado (delete)', async () => {
    db.query.mockResolvedValue({ rowCount: 0, rows: [] });

    await expect(repository.delete(99)).rejects.toThrow('Registro de foto não encontrado');
  });
});