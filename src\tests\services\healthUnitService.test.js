const HealthUnitService = require("../../services/healthUnitService");

describe("HealthUnitService", () => {
  let mockRepository;
  let service;

  beforeEach(() => {
    mockRepository = {
      findAll: jest.fn(),
      findById: jest.fn(),
    };
    service = new HealthUnitService(mockRepository);
  });

    it('deve retornar todas as unidades de saúde', async () => {
      const fakeData = [
        { id: 1, nome: "UBS Central" },
        { id: 2, nome: "UBS Norte" },
      ];

      mockRepository.findAll.mockResolvedValue(fakeData);

      const result = await service.findAll();

      expect(mockRepository.findAll).toHaveBeenCalled();
      expect(result).toEqual(fakeData);
    });

    it('deve retornar a unidade de saúde pelo ID', async () => {
      const fakeUnit = { id: 1, nome: "UBS Central" };

      mockRepository.findById.mockResolvedValue(fakeUnit);

      const result = await service.findById(1);

      expect(mockRepository.findById).toHaveBeenCalledWith(1);
      expect(result).toEqual(fakeUnit);
    });

    it('deve lançar erro se a unidade não for encontrada', async () => {
      mockRepository.findById.mockResolvedValue(null);

      await expect(service.findById(999))
      .rejects.toThrow("UBS não encontrada");
      expect(mockRepository.findById).toHaveBeenCalledWith(999);
    });
});