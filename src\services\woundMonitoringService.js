const WoundMonitoringModel = require('../models/woundMonitoringModel');

class WoundMonitoringService {
  constructor(repository) {
        this.repository = repository;
  }

  async findAll() {
    return await this.repository.findAll();
  }

  async findById(id) {
    return await this.repository.findById(id);
  }

  async create(monitoring) {
    return await this.repository.create(monitoring);
  }

  async update(id, monitoring) {
    return await this.repository.update(id, monitoring);
  }

  async delete(id) {
    return await this.repository.delete(id);
  }

}

module.exports = WoundMonitoringService;