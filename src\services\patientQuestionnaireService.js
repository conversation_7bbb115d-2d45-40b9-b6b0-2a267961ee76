const PatientQuestionnaireModel = require('../models/patientQuestionnaireModel');

class PatientQuestionnaireService {
  constructor(repository) {
        this.repository = repository;
  }

  async findAll() {
    return await this.repository.findAll();
  }

  async findById(id) {
    const data = await this.repository.findById(id);
    if (!data) throw new Error('Questionário não encontrado'); 
    return data;
  }

  async create(questionnaire) {
    const { error } = PatientQuestionnaireModel.schema.validate(questionnaire);
    if (error) {
      throw new Error(error.details[0].message);
    }
    return await this.repository.create(questionnaire);
  }

  async update(id, questionnaire) {
    const { error } = PatientQuestionnaireModel.schema.validate(questionnaire);
    if (error) {
      throw new Error(error.details[0].message);
    }
    return await this.repository.update(id, questionnaire);
  }
}

module.exports = PatientQuestionnaireService;