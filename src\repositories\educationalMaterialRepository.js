const db = require('../config/db');

class EducationalMaterialRepository {
    async findAll() {
        const result = await db.query('SELECT id, title, content_type, video_uri, description, target_wound_type, target_severity_level, creation_date FROM educational_material');
        return result.rows;
    }
    
    async findById(id) {
        const query = 'SELECT id, title, content_type, video_uri, description, target_wound_type, target_severity_level, creation_date FROM educational_material WHERE id = $1';
        const result = await db.query(query, [id]);
        if (result.rows.length === 0) {
            throw new Error('Material educacional não encontrado');
        }
        return result.rows;
    }

}

module.exports = EducationalMaterialRepository;