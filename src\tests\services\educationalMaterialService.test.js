const EducationalMaterialService = require('../../services/educationalMaterialService');

describe('EducationalMaterialService', () => {
  const mockRepository = {
    findAll: jest.fn(),
    findById: jest.fn()
  };

  const service = new EducationalMaterialService(mockRepository);

  const educationalMaterialsMock = [
    {
      id: 1,
      title: 'Cuidados Básicos com Úlceras',
      content_type: 'Vídeo',
      video_uri: 'https://youtu.be/exemplo1',
      description: 'Vídeo explicativo sobre cuidados básicos para úlceras.',
      target_wound_type: 'Úlcera de pé diabético',
      target_severity_level: 1,
      creation_date: '2023-01-10 09:00:00'
    },
    {
      id: 2,
      title: 'Prevenção de Lesões por Pressão',
      content_type: 'Texto',
      video_uri: null,
      description: 'Material detalhado sobre prevenção e cuidados.',
      target_wound_type: 'Les<PERSON> por pressão',
      target_severity_level: 2,
      creation_date: '2023-02-15 15:30:00'
    }
  ];

  beforeEach(() => {
    jest.clearAllMocks();
  });

    it('deve retornar todos os materiais educacionais', async () => {
      mockRepository.findAll.mockResolvedValue(educationalMaterialsMock);
      const result = await service.findAll();

      expect(mockRepository.findAll).toHaveBeenCalledTimes(1);
      expect(result).toEqual(educationalMaterialsMock);
    });

    it('deve retornar um material educacional pelo ID', async () => {
      mockRepository.findById.mockResolvedValue(educationalMaterialsMock[0]);
      const result = await service.findById(1);

      expect(mockRepository.findById).toHaveBeenCalledTimes(1);
      expect(result).toEqual(educationalMaterialsMock[0]);
    });
    
    it('deve lançar um erro se o material educacional não for encontrado', async () => {
      mockRepository.findById.mockResolvedValue(null);

      await expect(service.findById(999)).rejects.toThrow('Material não encontrado');
      expect(mockRepository.findById).toHaveBeenCalledTimes(1);
    });
  });



