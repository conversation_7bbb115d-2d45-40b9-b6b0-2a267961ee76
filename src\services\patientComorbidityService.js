const PatientComorbidityModel = require('../models/patientComorbidityModel');

class PatientComorbidityService {
  constructor(repository) {
        this.repository = repository;
  }

  async findAll() {
    return await this.repository.findAll();
  }

  async findById(id) {
    const data = await this.repository.findById(id);
    if (!data) throw new Error('Comorbidade não encontrada'); 
    return data;
  }

  async create(comorbidity) {
    const { error } = PatientComorbidityModel.schema.validate(comorbidity);
    if (error) {
      throw new Error(error.details[0].message);
    }
    return await this.repository.create(comorbidity);
  }

  async update(id, comorbidity) {
    const { error } = PatientComorbidityModel.schema.validate(comorbidity);
    if (error) {
      throw new Error(error.details[0].message);
    }
    return await this.repository.update(id, comorbidity);
  }
}

module.exports = PatientComorbidityService;