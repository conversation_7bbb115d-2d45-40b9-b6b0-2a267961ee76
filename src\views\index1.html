<!DOCTYPE html>
<html lang="pt-br">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Sistema de Monitoramento de Feridas</title>
    <link rel="stylesheet" href="/public/styles.css" />
    <style>
      body {
        font-family: Arial, sans-serif;
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
        text-align: center;
      }
      h1,
      h4 {
        color: #2c3e50;
      }
      .card {
        border: 1px solid #ddd;
        padding: 15px;
        margin-bottom: 15px;
        border-radius: 5px;
      }
      button {
        background-color: #013c6d;
        color: white;
        border: none;
        padding: 8px 15px;
        cursor: pointer;
      }
      button:hover {
        background-color: #056596;
      }
      .loading {
        display: none;
        color: #7f8c8d;
      }
      .section {
        margin-bottom: 30px;
      }
    </style>
  </head>
  <body>
    <h1>Cicatriza+</h1>
    <h4>Sistema de Monitoramento de Feridas</h4>

    <div class="section">
      <h4>Agentes de Saúde</h4>
      <button onclick="fetchHealthAgents()">Carregar Agentes</button>
      <span id="loading-agents" class="loading">Carregando...</span>
      <div id="health-agents-container"></div>
    </div>

    <div class="section">
      <h4>Pacientes</h4>
      <button onclick="fetchPatients()">Carregar Pacientes</button>
      <span id="loading-patients" class="loading">Carregando...</span>
      <div id="patients-container"></div>
      <div class="crud-buttons">
        <button onclick="showPatientForm()">Adicionar Paciente</button>
        <div
          id="patient-form"
          class="form-container"
          style="display: none"
        ></div>
      </div>
    </div>

    <div class="section">
      <h4>Materiais Educativos</h4>
      <button onclick="fetchMaterials()">Carregar Materiais</button>
      <span id="loading-materials" class="loading">Carregando...</span>
      <div id="materials-container"></div>
    </div>

    <div class="section">
      <h4>Emergências</h4>
      <button onclick="fetchEmergencies()">Carregar Emergências</button>
      <span id="loading-emergencies" class="loading">Carregando...</span>
      <div id="emergencies-container"></div>
    </div>

    <div class="section">
      <h4>UBS</h4>
      <button onclick="fetchUBS()">Carregar UBS</button>
      <span id="loading-ubs" class="loading">Carregando...</span>
      <div id="ubs-container"></div>
    </div>

    <div class="section">
      <h4>Comorbidades de Pacientes</h4>
      <button onclick="fetchComorbidities()">Carregar Comorbidades</button>
      <span id="loading-comorbidities" class="loading">Carregando...</span>
      <div id="comorbidities-container"></div>
      <div class="crud-buttons">
        <button onclick="showComorbidityForm()">Adicionar Comorbidade</button>
        <div
          id="comorbidity-form"
          class="form-container"
          style="display: none"
        ></div>
      </div>
    </div>

    <div class="section">
      <h4>Questionários de Pacientes</h4>
      <button onclick="fetchQuestionnaires()">Carregar Questionários</button>
      <span id="loading-questionnaires" class="loading">Carregando...</span>
      <div id="questionnaires-container"></div>
      <div class="crud-buttons">
        <button onclick="showQuestionnaireForm()">
          Adicionar Questionário
        </button>
        <div
          id="questionnaire-form"
          class="form-container"
          style="display: none"
        ></div>
      </div>
    </div>

    <div class="section">
      <h4>Registros de Fotos</h4>
      <button onclick="fetchPhotoRecords()">Carregar Registros de Fotos</button>
      <span id="loading-photo-records" class="loading">Carregando...</span>
      <div id="photo-records-container"></div>
      <div class="crud-buttons">
        <button onclick="showPhotoRecordForm()">
          Adicionar Registro de Foto
        </button>
        <div
          id="photo-record-form"
          class="form-container"
          style="display: none"
        ></div>
      </div>
    </div>

    <div class="section">
      <h4>Feridas</h4>
      <button onclick="fetchWounds()">Carregar Feridas</button>
      <span id="loading-wounds" class="loading">Carregando...</span>
      <div id="wounds-container"></div>
      <div class="crud-buttons">
        <button onclick="showWoundForm()">Adicionar Ferida</button>
        <div id="wound-form" class="form-container" style="display: none"></div>
      </div>
    </div>

    <div class="section">
      <h4>Monitoramento de Feridas</h4>
      <button onclick="fetchWoundMonitoring()">Carregar Monitoramentos</button>
      <span id="loading-wound-monitoring" class="loading">Carregando...</span>
      <div id="wound-monitoring-container"></div>
      <div class="crud-buttons">
        <button onclick="showWoundMonitoringForm()">
          Adicionar Monitoramento
        </button>
        <div
          id="wound-monitoring-form"
          class="form-container"
          style="display: none"
        ></div>
      </div>
    </div>

    <script src="main.js"></script>
  </body>
</html>
