const WoundMonitoringRepository = require('../../repositories/woundMonitoringRepository');
const db = require('../../config/db');

jest.mock('../../config/db');

describe('WoundMonitoringRepository', () => {
  let repository;

  const woundMonitoringMock = [
    {
      id: 1,
      evaluation_date: '2025-05-27',
      agent_observations: 'Observação do agente',
      recommended_treatment: 'Tratamento recomendado',
      monitoring_frequency: 3,
      next_evaluation_date: '2025-06-10',
      wound_id: 1,
      agent_id: 1,
    },
    {
      id: 2,
      evaluation_date: '2025-05-28',
      agent_observations: 'Outra observação do agente',
      recommended_treatment: 'Outro tratamento recomendado',
      monitoring_frequency: 2,
      next_evaluation_date: '2025-06-11',
      wound_id: 2,
      agent_id: 2,
    },
  ];

  beforeEach(() => {
    repository = new WoundMonitoringRepository();
    jest.clearAllMocks();
  });

  it('deve retornar todos os monitoramentos de feridas (findAll)', async () => {
    db.query.mockResolvedValue({ rows: woundMonitoringMock });

    const result = await repository.findAll();

    expect(db.query).toHaveBeenCalledWith(
      'SELECT id, evaluation_date, agent_observations, recommended_treatment, monitoring_frequency, next_evaluation_date, wound_id, agent_id FROM wound_monitoring'
    );
    expect(result).toEqual(woundMonitoringMock);
  });

  it('deve retornar um monitoramento de ferida por ID (findById)', async () => {
    db.query.mockResolvedValue({ rows: [woundMonitoringMock[0]] });

    const result = await repository.findById(1);

    expect(db.query).toHaveBeenCalledWith(
      'SELECT id, evaluation_date, agent_observations, recommended_treatment, monitoring_frequency, next_evaluation_date, wound_id, agent_id FROM wound_monitoring WHERE id = $1',
      [1]
    );
    expect(result).toEqual(woundMonitoringMock[0]);
  });

  it('deve lançar erro se o monitoramento não for encontrado (findById)', async () => {
    db.query.mockResolvedValue({ rows: [] });

    await expect(repository.findById(99)).rejects.toThrow('Acompanhamento de ferida não encontrado');

    expect(db.query).toHaveBeenCalledWith(
        'SELECT id, evaluation_date, agent_observations, recommended_treatment, monitoring_frequency, next_evaluation_date, wound_id, agent_id FROM wound_monitoring WHERE id = $1',
        [99]
    );
  });

  it('deve criar um novo monitoramento de ferida (create)', async () => {
    const dataToCreate = { ...woundMonitoringMock[0] };
    delete dataToCreate.id;

    db.query.mockResolvedValue({ rows: [dataToCreate] });

    const result = await repository.create(dataToCreate);

    expect(db.query).toHaveBeenCalledWith(
        'INSERT INTO wound_monitoring (evaluation_date, agent_observations, recommended_treatment, monitoring_frequency, next_evaluation_date, wound_id, agent_id) VALUES ($1, $2, $3, $4, $5, $6, $7) RETURNING *',
        [dataToCreate.evaluation_date, dataToCreate.agent_observations, dataToCreate.recommended_treatment, dataToCreate.monitoring_frequency, dataToCreate.next_evaluation_date, dataToCreate.wound_id, dataToCreate.agent_id]
    );
    expect(result).toEqual(dataToCreate);
  });

  it('deve atualizar um monitoramento de ferida existente (update)', async () => {
    const updatedData = { ...woundMonitoringMock[0], evaluation_date: '2025-05-29' };

    db.query.mockResolvedValue({ rows: [updatedData] });

    const result = await repository.update(1, updatedData);

    expect(db.query).toHaveBeenCalledWith(
        'UPDATE wound_monitoring SET evaluation_date = $1, agent_observations = $2, recommended_treatment = $3, monitoring_frequency = $4, next_evaluation_date = $5, wound_id = $6, agent_id = $7 WHERE id = $8 RETURNING *',
        [updatedData.evaluation_date, updatedData.agent_observations, updatedData.recommended_treatment, updatedData.monitoring_frequency, updatedData.next_evaluation_date, updatedData.wound_id, updatedData.agent_id, 1]
    );
    expect(result).toEqual(updatedData);
  });

  it('deve lançar erro se o monitoramento não for encontrado (update)', async () => {
    db.query.mockResolvedValue({ rows: [] });

    await expect(repository.update(99, woundMonitoringMock[0])).rejects.toThrow('Acompanhamento de ferida não encontrado');
  });

  it('deve excluir um monitoramento de ferida existente (delete)', async () => {
    db.query.mockResolvedValue({ rows: [woundMonitoringMock[0]] });

    const result = await repository.delete(1);

    expect(db.query).toHaveBeenCalledWith(
        'DELETE FROM wound_monitoring WHERE id = $1 RETURNING *',
        [1]
    );
    expect(result).toEqual(woundMonitoringMock[0]);
  });

  it('deve lançar erro se o monitoramento não for encontrado (delete)', async () => {
    db.query.mockResolvedValue({ rows: [] });

    await expect(repository.delete(99)).rejects.toThrow('Acompanhamento de ferida não encontrado');
  });
});