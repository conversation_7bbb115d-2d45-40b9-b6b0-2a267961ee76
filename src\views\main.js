const API_BASE_URL = "/api";

function showLoading(id) {
  document.getElementById(id).style.display = "inline";
}

function hideLoading(id) {
  document.getElementById(id).style.display = "none";
}

async function fetchHealthAgents() {
  showLoading("loading-agents");
  try {
    const response = await fetch(`${API_BASE_URL}/health-agents`);
    const data = await response.json();

    const container = document.getElementById("health-agents-container");
    container.innerHTML = "";

    data.forEach((agent) => {
      const card = document.createElement("div");
      card.className = "card";
      card.innerHTML = `
                <h3>${agent.nome_completo || "Nome não disponível"}</h3>
                <p>ID: ${agent.id}</p>
                <p>Tipo: ${agent.tipo_agente || "Não especificada"}</p>
                <p>UBS: ${agent.ubs_id || "Não associada"}</p>
            `;
      container.appendChild(card);
    });
  } catch (error) {
    console.error("Erro ao buscar agentes:", error);
    document.getElementById("health-agents-container").innerHTML =
      "<p>Erro ao carregar os dados. Verifique o console para mais detalhes.</p>";
  } finally {
    hideLoading("loading-agents");
  }
}

async function fetchPatients() {
  document.getElementById("loading-patients").style.display = "inline";
  try {
    const response = await fetch("/api/patients");
    const data = await response.json();
    renderPatients(data);
  } catch (error) {
    console.error("Erro ao carregar pacientes:", error);
    document.getElementById("patients-container").innerHTML =
      '<p class="error">Erro ao carregar dados</p>';
  } finally {
    document.getElementById("loading-patients").style.display = "none";
  }
}

function renderPatients(patients) {
  const container = document.getElementById("patients-container");
  if (patients.length === 0) {
    container.innerHTML = "<p>Nenhum paciente encontrado</p>";
    return;
  }

  let html =
    "<table><thead><tr><th>ID</th><th>Nome</th><th>Data Nascimento</th><th>Ações</th></tr></thead><tbody>";
  patients.forEach((patient) => {
    html += `
      <tr>
        <td>${patient.id}</td>
        <td>${patient.nome_completo}</td>
        <td>${new Date(patient.data_nascimento).toLocaleDateString()}</td>
        <td>
          <button onclick="editPatient(${patient.id})">Editar</button>
          <button onclick="deletePatient(${patient.id})">Excluir</button>
        </td>
      </tr>
    `;
  });
  html += "</tbody></table>";
  container.innerHTML = html;
}

function showPatientForm(patientId = null) {
  const formContainer = document.getElementById("patient-form");
  formContainer.style.display = "block";

  let formHtml = `
    <h3>${patientId ? "Editar" : "Adicionar"} Paciente</h3>
    <form id="patient-form-data">
      <input type="hidden" id="patient-id" value="${patientId || ""}">
      <div class="form-group">
        <label for="nome_completo">Nome Completo:</label>
        <input type="text" id="nome_completo" required>
      </div>
      <div class="form-group">
        <label for="data_nascimento">Data de Nascimento:</label>
        <input type="date" id="data_nascimento" required>
      </div>
      <div class="form-group">
        <label for="sexo">Sexo:</label>
        <select id="sexo" required>
          <option value="Masculino">Masculino</option>
          <option value="Feminino">Feminino</option>
          <option value="Outro">Outro</option>
        </select>
      </div>
      <div class="form-group">
        <label for="endereco">Endereço:</label>
        <input type="text" id="endereco">
      </div>
      <div class="form-group">
        <label for="telefone">Telefone:</label>
        <input type="text" id="telefone" required>
      </div>
      <div class="form-group">
        <button type="button" onclick="savePatient()">Salvar</button>
        <button type="button" onclick="document.getElementById('patient-form').style.display = 'none'">Cancelar</button>
      </div>
    </form>
  `;

  formContainer.innerHTML = formHtml;

  if (patientId) {
    loadPatientData(patientId);
  }
}

async function loadPatientData(patientId) {
  try {
    const response = await fetch(`/api/patients/${patientId}`);
    const patient = await response.json();

    document.getElementById("nome_completo").value = patient.nome_completo;
    document.getElementById("data_nascimento").value =
      patient.data_nascimento.split("T")[0];
    document.getElementById("sexo").value = patient.sexo;
    document.getElementById("endereco").value = patient.endereco || "";
    document.getElementById("telefone").value = patient.telefone;
  } catch (error) {
    console.error("Erro ao carregar dados do paciente:", error);
  }
}

async function savePatient() {
  const patientId = document.getElementById("patient-id").value;
  const patientData = {
    nome_completo: document.getElementById("nome_completo").value,
    data_nascimento: document.getElementById("data_nascimento").value,
    sexo: document.getElementById("sexo").value,
    endereco: document.getElementById("endereco").value,
    telefone: document.getElementById("telefone").value,
  };

  try {
    const url = patientId ? `/api/patients/${patientId}` : "/api/patients";
    const method = patientId ? "PUT" : "POST";

    const response = await fetch(url, {
      method: method,
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(patientData),
    });

    if (response.ok) {
      document.getElementById("patient-form").style.display = "none";
      fetchPatients();
    } else {
      const error = await response.json();
      alert(`Erro: ${error.error}`);
    }
  } catch (error) {
    console.error("Erro ao salvar paciente:", error);
    alert("Erro ao salvar paciente");
  }
}

async function editPatient(patientId) {
  showPatientForm(patientId);
}

async function deletePatient(patientId) {
  if (confirm("Tem certeza que deseja excluir este paciente?")) {
    try {
      const response = await fetch(`/api/patients/${patientId}`, {
        method: "DELETE",
      });

      if (response.ok) {
        fetchPatients();
      } else {
        const error = await response.json();
        alert(`Erro: ${error.error}`);
      }
    } catch (error) {
      console.error("Erro ao excluir paciente:", error);
      alert("Erro ao excluir paciente");
    }
  }
}

async function fetchMaterials() {
  const shouldShow = toggleContainer("materials-container");

  if (!shouldShow) return;

  showLoading("loading-materials");
  try {
    const response = await fetch(`${API_BASE_URL}/materials`);
    const data = await response.json();

    const container = document.getElementById("materials-container");
    container.innerHTML = "";

    data.forEach((material) => {
      const card = document.createElement("div");
      card.className = "card";
      card.innerHTML = `
        <h3>${material.title || "Título não disponível"}</h3>
        <p>ID: ${material.id}</p>
        <p>Tipo: ${material.material_type || "Não especificado"}</p>
        <p>URL: <a href="${
          material.material_url
        }" target="_blank">Acessar material</a></p>
      `;
      container.appendChild(card);
    });
  } catch (error) {
    console.error("Erro ao buscar materiais:", error);
    document.getElementById("materials-container").innerHTML =
      "<p>Erro ao carregar os dados. Verifique o console para mais detalhes.</p>";
  } finally {
    hideLoading("loading-materials");
  }
}

async function fetchEmergencies() {
  const shouldShow = toggleContainer("emergencies-container");

  if (!shouldShow) return;

  showLoading("loading-emergencies");
  try {
    const response = await fetch(`${API_BASE_URL}/emergencys`);
    const data = await response.json();

    const container = document.getElementById("emergencies-container");
    container.innerHTML = "";

    data.forEach((emergency) => {
      const card = document.createElement("div");
      card.className = "card";
      card.innerHTML = `
        <h3>Emergência #${emergency.id}</h3>
        <p>Paciente ID: ${emergency.patient_id}</p>
        <p>Motivo: ${emergency.reason || "Não especificado"}</p>
        <p>Status: ${emergency.attended ? "Atendida" : "Não atendida"}</p>
        <p>Data: ${emergency.activation_datetime || "Não registrada"}</p>
      `;
      container.appendChild(card);
    });
  } catch (error) {
    console.error("Erro ao buscar emergências:", error);
    document.getElementById("emergencies-container").innerHTML =
      "<p>Erro ao carregar os dados. Verifique o console para mais detalhes.</p>";
  } finally {
    hideLoading("loading-emergencies");
  }
}

async function fetchUBS() {
  const shouldShow = toggleContainer("ubs-container");

  if (!shouldShow) return;

  showLoading("loading-ubs");
  try {
    const response = await fetch(`${API_BASE_URL}/ubs`);
    const data = await response.json();

    const container = document.getElementById("ubs-container");
    container.innerHTML = "";

    data.forEach((ubs) => {
      const card = document.createElement("div");
      card.className = "card";
      card.innerHTML = `
        <h3>${ubs.unit_name || "Nome não disponível"}</h3>
        <p>ID: ${ubs.id}</p>
        <p>Endereço: ${ubs.address || "Não informado"}</p>
        <p>Telefone: ${ubs.phone || "Não informado"}</p>
        <p>Horário: ${ubs.operating_hours || "Não informado"}</p>
      `;
      container.appendChild(card);
    });
  } catch (error) {
    console.error("Erro ao buscar UBS:", error);
    document.getElementById("ubs-container").innerHTML =
      "<p>Erro ao carregar os dados. Verifique o console para mais detalhes.</p>";
  } finally {
    hideLoading("loading-ubs");
  }
}

async function fetchComorbidities() {
  const shouldShow = toggleContainer("comorbidities-container");

  if (!shouldShow) return;

  document.getElementById("loading-comorbidities").style.display = "inline";
  try {
    const response = await fetch("/api/comorbidity");
    const data = await response.json();
    renderComorbidities(data);
  } catch (error) {
    console.error("Erro ao carregar comorbidades:", error);
    document.getElementById("comorbidities-container").innerHTML =
      '<p class="error">Erro ao carregar dados</p>';
  } finally {
    document.getElementById("loading-comorbidities").style.display = "none";
  }
}

function renderComorbidities(comorbidities) {
  const container = document.getElementById("comorbidities-container");
  if (comorbidities.length === 0) {
    container.innerHTML = "<p>Nenhuma comorbidade encontrada</p>";
    return;
  }

  let html =
    "<table><thead><tr><th>ID</th><th>Tipo</th><th>Paciente</th><th>Em Tratamento</th><th>Ações</th></tr></thead><tbody>";
  comorbidities.forEach((comorbidity) => {
    html += `
      <tr>
        <td>${comorbidity.id}</td>
        <td>${comorbidity.tipo_comorbidade}</td>
        <td>${comorbidity.paciente_id}</td>
        <td>${comorbidity.em_tratamento ? "Sim" : "Não"}</td>
        <td>
          <button onclick="editComorbidity(${comorbidity.id})">Editar</button>
          <button onclick="deleteComorbidity(${
            comorbidity.id
          })">Excluir</button>
        </td>
      </tr>
    `;
  });
  html += "</tbody></table>";
  container.innerHTML = html;
}

function showComorbidityForm(comorbidityId = null) {
  const formContainer = document.getElementById("comorbidity-form");
  formContainer.style.display = "block";

  let formHtml = `
    <h3>${comorbidityId ? "Editar" : "Adicionar"} Comorbidade</h3>
    <form id="comorbidity-form-data">
      <input type="hidden" id="comorbidity-id" value="${comorbidityId || ""}">
      <div class="form-group">
        <label for="tipo_comorbidade">Tipo de Comorbidade:</label>
        <input type="text" id="tipo_comorbidade" required>
      </div>
      <div class="form-group">
        <label for="data_diagnostico">Data do Diagnóstico:</label>
        <input type="date" id="data_diagnostico" required>
      </div>
      <div class="form-group">
        <label for="em_tratamento">Em Tratamento:</label>
        <select id="em_tratamento" required>
          <option value="true">Sim</option>
          <option value="false">Não</option>
        </select>
      </div>
      <div class="form-group">
        <label for="observacoes">Observações:</label>
        <textarea id="observacoes"></textarea>
      </div>
      <div class="form-group">
        <label for="paciente_id">ID do Paciente:</label>
        <input type="number" id="paciente_id" required>
      </div>
      <div class="form-group">
        <button type="button" onclick="saveComorbidity()">Salvar</button>
        <button type="button" onclick="document.getElementById('comorbidity-form').style.display = 'none'">Cancelar</button>
      </div>
    </form>
  `;

  formContainer.innerHTML = formHtml;

  if (comorbidityId) {
    loadComorbidityData(comorbidityId);
  }
}

async function loadComorbidityData(comorbidityId) {
  try {
    const response = await fetch(`/api/comorbidity/${comorbidityId}`);
    const comorbidity = await response.json();

    document.getElementById("tipo_comorbidade").value =
      comorbidity.tipo_comorbidade;
    document.getElementById("data_diagnostico").value =
      comorbidity.data_diagnostico.split("T")[0];
    document.getElementById("em_tratamento").value =
      comorbidity.em_tratamento.toString();
    document.getElementById("observacoes").value =
      comorbidity.observacoes || "";
    document.getElementById("paciente_id").value = comorbidity.paciente_id;
  } catch (error) {
    console.error("Erro ao carregar dados da comorbidade:", error);
  }
}

async function saveComorbidity() {
  const comorbidityId = document.getElementById("comorbidity-id").value;
  const comorbidityData = {
    tipo_comorbidade: document.getElementById("tipo_comorbidade").value,
    data_diagnostico: document.getElementById("data_diagnostico").value,
    em_tratamento: document.getElementById("em_tratamento").value === "true",
    observacoes: document.getElementById("observacoes").value,
    paciente_id: parseInt(document.getElementById("paciente_id").value),
  };

  try {
    const url = comorbidityId
      ? `/api/comorbidity/${comorbidityId}`
      : "/api/comorbidity";
    const method = comorbidityId ? "PUT" : "POST";

    const response = await fetch(url, {
      method: method,
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(comorbidityData),
    });

    if (response.ok) {
      document.getElementById("comorbidity-form").style.display = "none";
      fetchComorbidities();
    } else {
      const error = await response.json();
      alert(`Erro: ${error.error}`);
    }
  } catch (error) {
    console.error("Erro ao salvar comorbidade:", error);
    alert("Erro ao salvar comorbidade");
  }
}

async function editComorbidity(comorbidityId) {
  showComorbidityForm(comorbidityId);
}

async function deleteComorbidity(comorbidityId) {
  if (confirm("Tem certeza que deseja excluir esta comorbidade?")) {
    try {
      const response = await fetch(`/api/comorbidity/${comorbidityId}`, {
        method: "DELETE",
      });

      if (response.ok) {
        fetchComorbidities();
      } else {
        const error = await response.json();
        alert(`Erro: ${error.error}`);
      }
    } catch (error) {
      console.error("Erro ao excluir comorbidade:", error);
      alert("Erro ao excluir comorbidade");
    }
  }
}

async function fetchQuestionnaires() {
  document.getElementById("loading-questionnaires").style.display = "inline";
  try {
    const response = await fetch("/api/patient-questionnaire");
    const data = await response.json();
    renderQuestionnaires(data);
  } catch (error) {
    console.error("Erro ao carregar questionários:", error);
    document.getElementById("questionnaires-container").innerHTML =
      '<p class="error">Erro ao carregar dados</p>';
  } finally {
    document.getElementById("loading-questionnaires").style.display = "none";
  }
}

function renderQuestionnaires(questionnaires) {
  const container = document.getElementById("questionnaires-container");
  if (questionnaires.length === 0) {
    container.innerHTML = "<p>Nenhum questionário encontrado</p>";
    return;
  }

  let html =
    "<table><thead><tr><th>ID</th><th>Data</th><th>Paciente</th><th>Nível de Dor</th><th>Ações</th></tr></thead><tbody>";
  questionnaires.forEach((questionnaire) => {
    html += `
      <tr>
        <td>${questionnaire.id}</td>
        <td>${new Date(
          questionnaire.data_preenchimento
        ).toLocaleDateString()}</td>
        <td>${questionnaire.paciente_id}</td>
        <td>${questionnaire.sentindo_dor}</td>
        <td>
          <button onclick="editQuestionnaire(${
            questionnaire.id
          })">Editar</button>
          <button onclick="deleteQuestionnaire(${
            questionnaire.id
          })">Excluir</button>
        </td>
      </tr>
    `;
  });
  html += "</tbody></table>";
  container.innerHTML = html;
}

function showQuestionnaireForm(questionnaireId = null) {
  const formContainer = document.getElementById("questionnaire-form");
  formContainer.style.display = "block";

  let formHtml = `
    <h3>${questionnaireId ? "Editar" : "Adicionar"} Questionário</h3>
    <form id="questionnaire-form-data">
      <input type="hidden" id="questionnaire-id" value="${
        questionnaireId || ""
      }">
      <div class="form-group">
        <label for="data_preenchimento">Data de Preenchimento:</label>
        <input type="datetime-local" id="data_preenchimento" required>
      </div>
      <div class="form-group">
        <label for="sentindo_dor">Nível de Dor (1-5):</label>
        <input type="number" id="sentindo_dor" min="1" max="5" required>
      </div>
      <div class="form-group">
        <label for="presenca_secrecao">Presença de Secreção:</label>
        <select id="presenca_secrecao" required>
          <option value="true">Sim</option>
          <option value="false">Não</option>
        </select>
      </div>
      <div class="form-group">
        <label for="odor">Odor:</label>
        <select id="odor" required>
          <option value="nenhum">Nenhum</option>
          <option value="leve">Leve</option>
          <option value="forte">Forte</option>
        </select>
      </div>
      <div class="form-group">
        <label for="febre">Febre:</label>
        <select id="febre" required>
          <option value="true">Sim</option>
          <option value="false">Não</option>
        </select>
      </div>
      <div class="form-group">
        <label for="vermelhidao_ao_redor">Vermelhidão ao Redor:</label>
        <select id="vermelhidao_ao_redor" required>
          <option value="true">Sim</option>
          <option value="false">Não</option>
        </select>
      </div>
      <div class="form-group">
        <label for="inchaco">Inchaço:</label>
        <select id="inchaco" required>
          <option value="true">Sim</option>
          <option value="false">Não</option>
        </select>
      </div>
      <div class="form-group">
        <label for="outros_sintomas">Outros Sintomas:</label>
        <textarea id="outros_sintomas"></textarea>
      </div>
      <div class="form-group">
        <label for="fez_curativo_recomendado">Fez Curativo Recomendado:</label>
        <select id="fez_curativo_recomendado" required>
          <option value="true">Sim</option>
          <option value="false">Não</option>
        </select>
      </div>
      <div class="form-group">
        <label for="dificuldades_tratamento">Dificuldades no Tratamento:</label>
        <textarea id="dificuldades_tratamento"></textarea>
      </div>
      <div class="form-group">
        <label for="paciente_id">ID do Paciente:</label>
        <input type="number" id="paciente_id" required>
      </div>
      <div class="form-group">
        <button type="button" onclick="saveQuestionnaire()">Salvar</button>
        <button type="button" onclick="document.getElementById('questionnaire-form').style.display = 'none'">Cancelar</button>
      </div>
    </form>
  `;

  formContainer.innerHTML = formHtml;

  if (questionnaireId) {
    loadQuestionnaireData(questionnaireId);
  }
}

async function loadQuestionnaireData(questionnaireId) {
  try {
    const response = await fetch(
      `/api/patient-questionnaire/${questionnaireId}`
    );
    const questionnaire = await response.json();

    document.getElementById("data_preenchimento").value =
      questionnaire.data_preenchimento.replace("Z", "");
    document.getElementById("sentindo_dor").value = questionnaire.sentindo_dor;
    document.getElementById("presenca_secrecao").value =
      questionnaire.presenca_secrecao.toString();
    document.getElementById("odor").value = questionnaire.odor;
    document.getElementById("febre").value = questionnaire.febre.toString();
    document.getElementById("vermelhidao_ao_redor").value =
      questionnaire.vermelhidao_ao_redor.toString();
    document.getElementById("inchaco").value = questionnaire.inchaco.toString();
    document.getElementById("outros_sintomas").value =
      questionnaire.outros_sintomas || "";
    document.getElementById("fez_curativo_recomendado").value =
      questionnaire.fez_curativo_recomendado.toString();
    document.getElementById("dificuldades_tratamento").value =
      questionnaire.dificuldades_tratamento || "";
    document.getElementById("paciente_id").value = questionnaire.paciente_id;
  } catch (error) {
    console.error("Erro ao carregar dados do questionário:", error);
  }
}

async function saveQuestionnaire() {
  const questionnaireId = document.getElementById("questionnaire-id").value;
  const questionnaireData = {
    data_preenchimento: document.getElementById("data_preenchimento").value,
    sentindo_dor: parseInt(document.getElementById("sentindo_dor").value),
    presenca_secrecao:
      document.getElementById("presenca_secrecao").value === "true",
    odor: document.getElementById("odor").value,
    febre: document.getElementById("febre").value === "true",
    vermelhidao_ao_redor:
      document.getElementById("vermelhidao_ao_redor").value === "true",
    inchaco: document.getElementById("inchaco").value === "true",
    outros_sintomas: document.getElementById("outros_sintomas").value,
    fez_curativo_recomendado:
      document.getElementById("fez_curativo_recomendado").value === "true",
    dificuldades_tratamento: document.getElementById("dificuldades_tratamento")
      .value,
    paciente_id: parseInt(document.getElementById("paciente_id").value),
  };

  try {
    const url = questionnaireId
      ? `/api/patient-questionnaire/${questionnaireId}`
      : "/api/patient-questionnaire";
    const method = questionnaireId ? "PUT" : "POST";

    const response = await fetch(url, {
      method: method,
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(questionnaireData),
    });

    if (response.ok) {
      document.getElementById("questionnaire-form").style.display = "none";
      fetchQuestionnaires();
    } else {
      const error = await response.json();
      alert(`Erro: ${error.error}`);
    }
  } catch (error) {
    console.error("Erro ao salvar questionário:", error);
    alert("Erro ao salvar questionário");
  }
}

async function editQuestionnaire(questionnaireId) {
  showQuestionnaireForm(questionnaireId);
}

async function deleteQuestionnaire(questionnaireId) {
  if (confirm("Tem certeza que deseja excluir este questionário?")) {
    try {
      const response = await fetch(
        `/api/patient-questionnaire/${questionnaireId}`,
        {
          method: "DELETE",
        }
      );

      if (response.ok) {
        fetchQuestionnaires();
      } else {
        const error = await response.json();
        alert(`Erro: ${error.error}`);
      }
    } catch (error) {
      console.error("Erro ao excluir questionário:", error);
      alert("Erro ao excluir questionário");
    }
  }
}

async function fetchPhotoRecords() {
  document.getElementById("loading-photo-records").style.display = "inline";
  try {
    const response = await fetch("/api/photo-records");
    const data = await response.json();
    renderPhotoRecords(data);
  } catch (error) {
    console.error("Erro ao carregar registros de fotos:", error);
    document.getElementById("photo-records-container").innerHTML =
      '<p class="error">Erro ao carregar dados</p>';
  } finally {
    document.getElementById("loading-photo-records").style.display = "none";
  }
}

function renderPhotoRecords(records) {
  const container = document.getElementById("photo-records-container");
  if (records.length === 0) {
    container.innerHTML = "<p>Nenhum registro de foto encontrado</p>";
    return;
  }

  let html =
    "<table><thead><tr><th>ID</th><th>Data</th><th>Ferida ID</th><th>Ações</th></tr></thead><tbody>";
  records.forEach((record) => {
    html += `
      <tr>
        <td>${record.id}</td>
        <td>${new Date(record.data_registro).toLocaleString()}</td>
        <td>${record.ferida_id}</td>
        <td>
          <button onclick="editPhotoRecord(${record.id})">Editar</button>
          <button onclick="deletePhotoRecord(${record.id})">Excluir</button>
        </td>
      </tr>
    `;
  });
  html += "</tbody></table>";
  container.innerHTML = html;
}

function showPhotoRecordForm(recordId = null) {
  const formContainer = document.getElementById("photo-record-form");
  formContainer.style.display = "block";

  let formHtml = `
    <h3>${recordId ? "Editar" : "Adicionar"} Registro de Foto</h3>
    <form id="photo-record-form-data">
      <input type="hidden" id="photo-record-id" value="${recordId || ""}">
      <div class="form-group">
        <label for="ferida_id">ID da Ferida:</label>
        <input type="number" id="ferida_id" required>
      </div>
      <div class="form-group">
        <label for="url_foto">URL da Foto:</label>
        <input type="text" id="url_foto" required>
      </div>
      <div class="form-group">
        <label for="observacoes">Observações:</label>
        <textarea id="observacoes"></textarea>
      </div>
      <div class="form-group">
        <button type="button" onclick="savePhotoRecord()">Salvar</button>
        <button type="button" onclick="document.getElementById('photo-record-form').style.display = 'none'">Cancelar</button>
      </div>
    </form>
  `;

  formContainer.innerHTML = formHtml;

  if (recordId) {
    loadPhotoRecordData(recordId);
  }
}

async function loadPhotoRecordData(recordId) {
  try {
    const response = await fetch(`/api/photo-records/${recordId}`);
    const record = await response.json();

    document.getElementById("ferida_id").value = record.ferida_id;
    document.getElementById("url_foto").value = record.url_foto;
    document.getElementById("observacoes").value = record.observacoes || "";
  } catch (error) {
    console.error("Erro ao carregar dados do registro de foto:", error);
  }
}

async function savePhotoRecord() {
  const recordId = document.getElementById("photo-record-id").value;
  const recordData = {
    ferida_id: document.getElementById("ferida_id").value,
    url_foto: document.getElementById("url_foto").value,
    observacoes: document.getElementById("observacoes").value,
  };

  try {
    const url = recordId
      ? `/api/photo-records/${recordId}`
      : "/api/photo-records";
    const method = recordId ? "PUT" : "POST";

    const response = await fetch(url, {
      method: method,
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(recordData),
    });

    if (response.ok) {
      document.getElementById("photo-record-form").style.display = "none";
      fetchPhotoRecords();
    } else {
      const error = await response.json();
      alert(`Erro: ${error.error}`);
    }
  } catch (error) {
    console.error("Erro ao salvar registro de foto:", error);
    alert("Erro ao salvar registro de foto");
  }
}

async function editPhotoRecord(recordId) {
  showPhotoRecordForm(recordId);
}

async function deletePhotoRecord(recordId) {
  if (confirm("Tem certeza que deseja excluir este registro de foto?")) {
    try {
      const response = await fetch(`/api/photo-records/${recordId}`, {
        method: "DELETE",
      });

      if (response.ok) {
        fetchPhotoRecords();
      } else {
        const error = await response.json();
        alert(`Erro: ${error.error}`);
      }
    } catch (error) {
      console.error("Erro ao excluir registro de foto:", error);
      alert("Erro ao excluir registro de foto");
    }
  }
}

async function fetchWounds() {
  document.getElementById("loading-wounds").style.display = "inline";
  try {
    const response = await fetch("/api/wounds");
    const data = await response.json();
    renderWounds(data);
  } catch (error) {
    console.error("Erro ao carregar feridas:", error);
    document.getElementById("wounds-container").innerHTML =
      '<p class="error">Erro ao carregar dados</p>';
  } finally {
    document.getElementById("loading-wounds").style.display = "none";
  }
}

function renderWounds(wounds) {
  const container = document.getElementById("wounds-container");
  if (wounds.length === 0) {
    container.innerHTML = "<p>Nenhuma ferida encontrada</p>";
    return;
  }

  let html =
    "<table><thead><tr><th>ID</th><th>Tipo</th><th>Localização</th><th>Paciente</th><th>Ações</th></tr></thead><tbody>";
  wounds.forEach((wound) => {
    html += `
      <tr>
        <td>${wound.id}</td>
        <td>${wound.tipo_ferida}</td>
        <td>${wound.localizacao_anatomica}</td>
        <td>${wound.paciente_id}</td>
        <td>
          <button onclick="editWound(${wound.id})">Editar</button>
          <button onclick="deleteWound(${wound.id})">Excluir</button>
        </td>
      </tr>
    `;
  });
  html += "</tbody></table>";
  container.innerHTML = html;
}

function showWoundForm(woundId = null) {
  const formContainer = document.getElementById("wound-form");
  formContainer.style.display = "block";

  let formHtml = `
    <h3>${woundId ? "Editar" : "Adicionar"} Ferida</h3>
    <form id="wound-form-data">
      <input type="hidden" id="wound-id" value="${woundId || ""}">
      <div class="form-group">
        <label for="tipo_ferida">Tipo de Ferida:</label>
        <select id="tipo_ferida" required>
          <option value="úlcera venosa">Úlcera Venosa</option>
          <option value="úlcera pé diabético">Úlcera de Pé Diabético</option>
          <option value="lesão por pressão">Lesão por Pressão</option>
        </select>
      </div>
      <div class="form-group">
        <label for="localizacao_anatomica">Localização Anatômica:</label>
        <input type="text" id="localizacao_anatomica" required>
      </div>
      <div class="form-group">
        <label for="data_primeira_ocorrencia">Data da Primeira Ocorrência:</label>
        <input type="date" id="data_primeira_ocorrencia" required>
      </div>
      <div class="form-group">
        <label for="nivel_gravidade">Nível de Gravidade (1-5):</label>
        <input type="number" id="nivel_gravidade" min="1" max="5" required>
      </div>
      <div class="form-group">
        <label for="status">Status:</label>
        <select id="status" required>
          <option value="ativa">Ativa</option>
          <option value="em cicatrização">Em Cicatrização</option>
          <option value="cicatrizada">Cicatrizada</option>
        </select>
      </div>
      <div class="form-group">
        <label for="paciente_id">ID do Paciente:</label>
        <input type="number" id="paciente_id" required>
      </div>
      <div class="form-group">
        <button type="button" onclick="saveWound()">Salvar</button>
        <button type="button" onclick="document.getElementById('wound-form').style.display = 'none'">Cancelar</button>
      </div>
    </form>
  `;

  formContainer.innerHTML = formHtml;

  if (woundId) {
    loadWoundData(woundId);
  }
}

async function loadWoundData(woundId) {
  try {
    const response = await fetch(`/api/wounds/${woundId}`);
    const wound = await response.json();

    document.getElementById("tipo_ferida").value = wound.tipo_ferida;
    document.getElementById("localizacao_anatomica").value =
      wound.localizacao_anatomica;
    document.getElementById("data_primeira_ocorrencia").value =
      wound.data_primeira_ocorrencia.split("T")[0];
    document.getElementById("nivel_gravidade").value = wound.nivel_gravidade;
    document.getElementById("status").value = wound.status;
    document.getElementById("paciente_id").value = wound.paciente_id;
  } catch (error) {
    console.error("Erro ao carregar dados da ferida:", error);
  }
}

async function saveWound() {
  const woundId = document.getElementById("wound-id").value;
  const woundData = {
    tipo_ferida: document.getElementById("tipo_ferida").value,
    localizacao_anatomica: document.getElementById("localizacao_anatomica")
      .value,
    data_primeira_ocorrencia: document.getElementById(
      "data_primeira_ocorrencia"
    ).value,
    nivel_gravidade: parseInt(document.getElementById("nivel_gravidade").value),
    status: document.getElementById("status").value,
    paciente_id: parseInt(document.getElementById("paciente_id").value),
  };

  try {
    const url = woundId ? `/api/wounds/${woundId}` : "/api/wounds";
    const method = woundId ? "PUT" : "POST";

    const response = await fetch(url, {
      method: method,
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(woundData),
    });

    if (response.ok) {
      document.getElementById("wound-form").style.display = "none";
      fetchWounds();
    } else {
      const error = await response.json();
      alert(`Erro: ${error.error}`);
    }
  } catch (error) {
    console.error("Erro ao salvar ferida:", error);
    alert("Erro ao salvar ferida");
  }
}

async function editWound(woundId) {
  showWoundForm(woundId);
}

async function deleteWound(woundId) {
  if (confirm("Tem certeza que deseja excluir esta ferida?")) {
    try {
      const response = await fetch(`/api/wounds/${woundId}`, {
        method: "DELETE",
      });

      if (response.ok) {
        fetchWounds();
      } else {
        const error = await response.json();
        alert(`Erro: ${error.error}`);
      }
    } catch (error) {
      console.error("Erro ao excluir ferida:", error);
      alert("Erro ao excluir ferida");
    }
  }
}

async function fetchWoundMonitoring() {
  document.getElementById("loading-wound-monitoring").style.display = "inline";
  try {
    const response = await fetch("/api/wound-monitoring");
    const data = await response.json();
    renderWoundMonitoring(data);
  } catch (error) {
    console.error("Erro ao carregar monitoramentos:", error);
    document.getElementById("wound-monitoring-container").innerHTML =
      '<p class="error">Erro ao carregar dados</p>';
  } finally {
    document.getElementById("loading-wound-monitoring").style.display = "none";
  }
}

function renderWoundMonitoring(monitorings) {
  const container = document.getElementById("wound-monitoring-container");
  if (monitorings.length === 0) {
    container.innerHTML = "<p>Nenhum monitoramento encontrado</p>";
    return;
  }

  let html =
    "<table><thead><tr><th>ID</th><th>Data Avaliação</th><th>Ferida</th><th>Agente</th><th>Ações</th></tr></thead><tbody>";
  monitorings.forEach((monitoring) => {
    html += `
      <tr>
        <td>${monitoring.id}</td>
        <td>${new Date(monitoring.data_avaliacao).toLocaleDateString()}</td>
        <td>${monitoring.ferida_id}</td>
        <td>${monitoring.agente_id}</td>
        <td>
          <button onclick="editWoundMonitoring(${
            monitoring.id
          })">Editar</button>
          <button onclick="deleteWoundMonitoring(${
            monitoring.id
          })">Excluir</button>
        </td>
      </tr>
    `;
  });
  html += "</tbody></table>";
  container.innerHTML = html;
}

function showWoundMonitoringForm(monitoringId = null) {
  const formContainer = document.getElementById("wound-monitoring-form");
  formContainer.style.display = "block";

  let formHtml = `
    <h3>${monitoringId ? "Editar" : "Adicionar"} Monitoramento</h3>
    <form id="wound-monitoring-form-data">
      <input type="hidden" id="wound-monitoring-id" value="${
        monitoringId || ""
      }">
      <div class="form-group">
        <label for="data_avaliacao">Data de Avaliação:</label>
        <input type="datetime-local" id="data_avaliacao" required>
      </div>
      <div class="form-group">
        <label for="observacoes_agente">Observações do Agente:</label>
        <textarea id="observacoes_agente" required></textarea>
      </div>
      <div class="form-group">
        <label for="tratamento_recomendado">Tratamento Recomendado:</label>
        <textarea id="tratamento_recomendado" required></textarea>
      </div>
      <div class="form-group">
        <label for="frequencia_monitoramento">Frequência de Monitoramento:</label>
        <select id="frequencia_monitoramento" required>
          <option value="1">Diária</option>
          <option value="2">A cada 2 dias</option>
          <option value="3">A cada 3 dias</option>
          <option value="4">Semanal</option>
          <option value="5">Quinzenal</option>
        </select>
      </div>
      <div class="form-group">
        <label for="proxima_avaliacao_data">Data da Próxima Avaliação:</label>
        <input type="date" id="proxima_avaliacao_data" required>
      </div>
      <div class="form-group">
        <label for="ferida_id">ID da Ferida:</label>
        <input type="number" id="ferida_id" required>
      </div>
      <div class="form-group">
        <label for="agente_id">ID do Agente:</label>
        <input type="number" id="agente_id" required>
      </div>
      <div class="form-group">
        <button type="button" onclick="saveWoundMonitoring()">Salvar</button>
        <button type="button" onclick="document.getElementById('wound-monitoring-form').style.display = 'none'">Cancelar</button>
      </div>
    </form>
  `;

  formContainer.innerHTML = formHtml;

  if (monitoringId) {
    loadWoundMonitoringData(monitoringId);
  }
}

async function loadWoundMonitoringData(monitoringId) {
  try {
    const response = await fetch(`/api/wound-monitoring/${monitoringId}`);
    const monitoring = await response.json();

    document.getElementById("data_avaliacao").value =
      monitoring.evaluation_date.replace("Z", "");
    document.getElementById("observacoes_agente").value =
      monitoring.agent_observations;
    document.getElementById("tratamento_recomendado").value =
      monitoring.recommended_treatment;
    document.getElementById("frequencia_monitoramento").value =
      monitoring.monitoring_frequency;
    document.getElementById("proxima_avaliacao_data").value =
      monitoring.next_evaluation_date.split("T")[0];
    document.getElementById("ferida_id").value = monitoring.wound_id;
    document.getElementById("agente_id").value = monitoring.agent_id;
  } catch (error) {
    console.error("Erro ao carregar dados do monitoramento:", error);
  }
}

async function saveWoundMonitoring() {
  const monitoringId = document.getElementById("wound-monitoring-id").value;
  const monitoringData = {
    evaluation_date: document.getElementById("data_avaliacao").value,
    agent_observations: document.getElementById("observacoes_agente").value,
    recommended_treatment: document.getElementById("tratamento_recomendado")
      .value,
    monitoring_frequency: parseInt(
      document.getElementById("frequencia_monitoramento").value
    ),
    next_evaluation_date: document.getElementById("proxima_avaliacao_data")
      .value,
    wound_id: parseInt(document.getElementById("ferida_id").value),
    agent_id: parseInt(document.getElementById("agente_id").value),
  };

  try {
    const url = monitoringId
      ? `/api/wound-monitoring/${monitoringId}`
      : "/api/wound-monitoring";
    const method = monitoringId ? "PUT" : "POST";

    const response = await fetch(url, {
      method: method,
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(monitoringData),
    });

    if (response.ok) {
      document.getElementById("wound-monitoring-form").style.display = "none";
      fetchWoundMonitoring();
    } else {
      const error = await response.json();
      alert(`Erro: ${error.error}`);
    }
  } catch (error) {
    console.error("Erro ao salvar monitoramento:", error);
    alert("Erro ao salvar monitoramento");
  }
}

async function editWoundMonitoring(monitoringId) {
  showWoundMonitoringForm(monitoringId);
}

async function deleteWoundMonitoring(monitoringId) {
  if (confirm("Tem certeza que deseja excluir este monitoramento?")) {
    try {
      const response = await fetch(`/api/wound-monitoring/${monitoringId}`, {
        method: "DELETE",
      });

      if (response.ok) {
        fetchWoundMonitoring();
      } else {
        const error = await response.json();
        alert(`Erro: ${error.error}`);
      }
    } catch (error) {
      console.error("Erro ao excluir monitoramento:", error);
      alert("Erro ao excluir monitoramento");
    }
  }
}

function toggleContainer(containerId) {
  const container = document.getElementById(containerId);
  if (container.style.display === "none" || container.innerHTML === "") {
    return true;
  } else {
    container.innerHTML = "";
    return false;
  }
}

async function fetchHealthAgents() {
  const shouldShow = toggleContainer("health-agents-container");

  if (!shouldShow) return;

  showLoading("loading-agents");
  try {
    const response = await fetch(`${API_BASE_URL}/health-agents`);
    const data = await response.json();

    const container = document.getElementById("health-agents-container");
    container.innerHTML = "";

    data.forEach((agent) => {
      const card = document.createElement("div");
      card.className = "card";
      card.innerHTML = `
        <h3>${agent.nome_completo || "Nome não disponível"}</h3>
        <p>ID: ${agent.id}</p>
        <p>Tipo: ${agent.tipo_agente || "Não especificada"}</p>
        <p>UBS: ${agent.ubs_id || "Não associada"}</p>
      `;
      container.appendChild(card);
    });
  } catch (error) {
    console.error("Erro ao buscar agentes:", error);
    document.getElementById("health-agents-container").innerHTML =
      "<p>Erro ao carregar os dados. Verifique o console para mais detalhes.</p>";
  } finally {
    hideLoading("loading-agents");
  }
}

async function fetchPatients() {
  const shouldShow = toggleContainer("patients-container");

  if (!shouldShow) return;

  document.getElementById("loading-patients").style.display = "inline";
  try {
    const response = await fetch("/api/patients");
    const data = await response.json();
    renderPatients(data);
  } catch (error) {
    console.error("Erro ao carregar pacientes:", error);
    document.getElementById("patients-container").innerHTML =
      '<p class="error">Erro ao carregar dados</p>';
  } finally {
    document.getElementById("loading-patients").style.display = "none";
  }
}

async function fetchMaterials() {
  const shouldShow = toggleContainer("materials-container");

  if (!shouldShow) return;

  showLoading("loading-materials");
  try {
    const response = await fetch(`${API_BASE_URL}/materials`);
    const data = await response.json();

    const container = document.getElementById("materials-container");
    container.innerHTML = "";

    data.forEach((material) => {
      const card = document.createElement("div");
      card.className = "card";
      card.innerHTML = `
        <h3>${material.title || "Título não disponível"}</h3>
        <p>ID: ${material.id}</p>
        <p>Tipo: ${material.material_type || "Não especificado"}</p>
        <p>URL: <a href="${
          material.material_url
        }" target="_blank">Acessar material</a></p>
      `;
      container.appendChild(card);
    });
  } catch (error) {
    console.error("Erro ao buscar materiais:", error);
    document.getElementById("materials-container").innerHTML =
      "<p>Erro ao carregar os dados. Verifique o console para mais detalhes.</p>";
  } finally {
    hideLoading("loading-materials");
  }
}

async function fetchEmergencies() {
  const shouldShow = toggleContainer("emergencies-container");

  if (!shouldShow) return;

  showLoading("loading-emergencies");
  try {
    const response = await fetch(`${API_BASE_URL}/emergencys`);
    const data = await response.json();

    const container = document.getElementById("emergencies-container");
    container.innerHTML = "";

    data.forEach((emergency) => {
      const card = document.createElement("div");
      card.className = "card";
      card.innerHTML = `
        <h3>Emergência #${emergency.id}</h3>
        <p>Paciente ID: ${emergency.patient_id}</p>
        <p>Motivo: ${emergency.reason || "Não especificado"}</p>
        <p>Status: ${emergency.attended ? "Atendida" : "Não atendida"}</p>
        <p>Data: ${emergency.activation_datetime || "Não registrada"}</p>
      `;
      container.appendChild(card);
    });
  } catch (error) {
    console.error("Erro ao buscar emergências:", error);
    document.getElementById("emergencies-container").innerHTML =
      "<p>Erro ao carregar os dados. Verifique o console para mais detalhes.</p>";
  } finally {
    hideLoading("loading-emergencies");
  }
}

async function fetchUBS() {
  const shouldShow = toggleContainer("ubs-container");

  if (!shouldShow) return;

  showLoading("loading-ubs");
  try {
    const response = await fetch(`${API_BASE_URL}/ubs`);
    const data = await response.json();

    const container = document.getElementById("ubs-container");
    container.innerHTML = "";

    data.forEach((ubs) => {
      const card = document.createElement("div");
      card.className = "card";
      card.innerHTML = `
        <h3>${ubs.unit_name || "Nome não disponível"}</h3>
        <p>ID: ${ubs.id}</p>
        <p>Endereço: ${ubs.address || "Não informado"}</p>
        <p>Telefone: ${ubs.phone || "Não informado"}</p>
        <p>Horário: ${ubs.operating_hours || "Não informado"}</p>
      `;
      container.appendChild(card);
    });
  } catch (error) {
    console.error("Erro ao buscar UBS:", error);
    document.getElementById("ubs-container").innerHTML =
      "<p>Erro ao carregar os dados. Verifique o console para mais detalhes.</p>";
  } finally {
    hideLoading("loading-ubs");
  }
}

async function fetchComorbidities() {
  const shouldShow = toggleContainer("comorbidities-container");

  if (!shouldShow) return;

  document.getElementById("loading-comorbidities").style.display = "inline";
  try {
    const response = await fetch("/api/comorbidity");
    const data = await response.json();
    renderComorbidities(data);
  } catch (error) {
    console.error("Erro ao carregar comorbidades:", error);
    document.getElementById("comorbidities-container").innerHTML =
      '<p class="error">Erro ao carregar dados</p>';
  } finally {
    document.getElementById("loading-comorbidities").style.display = "none";
  }
}

async function fetchQuestionnaires() {
  const shouldShow = toggleContainer("questionnaires-container");

  if (!shouldShow) return;

  document.getElementById("loading-questionnaires").style.display = "inline";
  try {
    const response = await fetch("/api/patient-questionnaire");
    const data = await response.json();
    renderQuestionnaires(data);
  } catch (error) {
    console.error("Erro ao carregar questionários:", error);
    document.getElementById("questionnaires-container").innerHTML =
      '<p class="error">Erro ao carregar dados</p>';
  } finally {
    document.getElementById("loading-questionnaires").style.display = "none";
  }
}

async function fetchPhotoRecords() {
  const shouldShow = toggleContainer("photo-records-container");

  if (!shouldShow) return;

  document.getElementById("loading-photo-records").style.display = "inline";
  try {
    const response = await fetch("/api/photo-records");
    const data = await response.json();
    renderPhotoRecords(data);
  } catch (error) {
    console.error("Erro ao carregar registros de fotos:", error);
    document.getElementById("photo-records-container").innerHTML =
      '<p class="error">Erro ao carregar dados</p>';
  } finally {
    document.getElementById("loading-photo-records").style.display = "none";
  }
}

async function fetchWounds() {
  const shouldShow = toggleContainer("wounds-container");

  if (!shouldShow) return;

  document.getElementById("loading-wounds").style.display = "inline";
  try {
    const response = await fetch("/api/wounds");
    const data = await response.json();
    renderWounds(data);
  } catch (error) {
    console.error("Erro ao carregar feridas:", error);
    document.getElementById("wounds-container").innerHTML =
      '<p class="error">Erro ao carregar dados</p>';
  } finally {
    document.getElementById("loading-wounds").style.display = "none";
  }
}

async function fetchWoundMonitoring() {
  const shouldShow = toggleContainer("wound-monitoring-container");

  if (!shouldShow) return;

  document.getElementById("loading-wound-monitoring").style.display = "inline";
  try {
    const response = await fetch("/api/wound-monitoring");
    const data = await response.json();
    renderWoundMonitoring(data);
  } catch (error) {
    console.error("Erro ao carregar monitoramentos:", error);
    document.getElementById("wound-monitoring-container").innerHTML =
      '<p class="error">Erro ao carregar dados</p>';
  } finally {
    document.getElementById("loading-wound-monitoring").style.display = "none";
  }
}
