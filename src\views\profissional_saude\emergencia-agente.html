<!DOCTYPE html>
<html lang="pt-BR">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" href="../../../public/css/global.css" />
    <title>Emergência do Profissional</title>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&display=swap"
      rel="stylesheet"
    />
     <script src="../../../public/js/auth-check.js"></script>
     <style>
      .section-title {
        font-size: 20px;
        font-weight: 600;
        color: #2d3748;
        margin-bottom: 20px;
      }
      
      .emergency-card {
        border-left: 4px solid #e53e3e;
        margin-bottom: 20px;
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        padding: 16px;
      }
      
      .emergency-content {
        display: flex;
      }
      
      .emergency-icon {
        font-size: 28px;
        color: #e53e3e;
        margin-right: 16px;
        min-width: 40px;
        display: flex;
        align-items: flex-start;
        justify-content: center;
      }
      
      .emergency-details {
        flex-grow: 1;
      }
      
      .emergency-title {
        font-size: 18px;
        font-weight: 600;
        color: #e53e3e;
        margin-bottom: 8px;
      }
      
      .emergency-reason {
        font-size: 16px;
        color: #2d3748;
        margin-bottom: 12px;
      }
      
      .patient-info {
        font-size: 14px;
        color: #4a5568;
        margin-bottom: 8px;
      }
      
      .time-info {
        font-size: 12px;
        color: #718096;
        margin-bottom: 12px;
      }
      
      .emergency-actions {
        display: flex;
        gap: 12px;
        flex-wrap: wrap;
      }
      
      .empty-state {
        text-align: center;
        padding: 30px;
        font-size: 16px;
        color: #718096;
        font-style: italic;
      }
      
      /* Formulário de observações */
      .observations-form {
        margin-top: 20px;
        background-color: #f8fafc;
        padding: 16px;
        border-radius: 8px;
        display: none;
      }
      
      .form-group {
        margin-bottom: 16px;
      }
      
      .form-label {
        font-weight: 500;
        margin-bottom: 8px;
        display: block;
      }
      
      .form-textarea {
        width: 100%;
        min-height: 100px;
        padding: 12px;
        border: 1px solid #e2e8f0;
        border-radius: 6px;
        font-family: 'Roboto', sans-serif;
        font-size: 14px;
      }
      
      .form-select {
        width: 100%;
        padding: 10px;
        border: 1px solid #e2e8f0;
        border-radius: 6px;
        font-size: 14px;
      }
      
      .form-actions {
        display: flex;
        gap: 12px;
      }
      
      .active-emergencies-title {
        color: #e53e3e;
        display: flex;
        align-items: center;
        gap: 10px;
      }
      
      .active-emergencies-count {
        background-color: #e53e3e;
        color: white;
        border-radius: 50%;
        width: 24px;
        height: 24px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
      }
      
      .quick-actions {
        margin-top: 30px;
      }
      
      .menu-buttons {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
      }
      
      /* Estilo para emergência em atendimento */
      .emergency-attending {
        border-left: 4px solid #f6ad55;
        background-color: #fffaf0;
      }
     </style>
  </head>
  <body>
    <div class="desktop-frame">
      <div class="sidebar">
        <div class="sidebar-logo">
          <img
            src="../../../public/images/logo/cicatriza_semnada.png"
            alt="Logo"
            class="logo-small"
          />
          <h3 class="sidebar-title">Cicatriza+</h3>
        </div>
        <ul class="sidebar-menu">
          <a href="menu-principal-agente.html">
            <li class="sidebar-item">
              <span class="sidebar-icon">🏠</span>Início
            </li>
          </a>
          </a>
          <a href="gerenciar-pacientes.html">
            <li class="sidebar-item">
              <span class="sidebar-icon">👥</span>Pacientes
            </li>
          </a>
          <li class="sidebar-item">
            <span class="sidebar-icon">📝</span>Tutorial
          </li>
          <li class="sidebar-item">
            <span class="sidebar-icon">👤</span>Perfil
          </li>
          <a href="notificacoes-agente.html" class="sidebar-link">
            <li class="sidebar-item">
              <span class="sidebar-icon">🔔</span>Notificações
            </li>
          </a>
          <a href="emergencia-agente.html">
            <li class="sidebar-item active">
              <span class="sidebar-icon">⚠️</span>Emergência
            </li>
          </a>
        </ul>
      </div>
      <div class="desktop-content">
        <div class="desktop-header">
          <div class="header-title">Atendimento de Emergência</div>
        </div>
        <div class="main-area">
          <div class="card">
            <h3 class="section-title active-emergencies-title">
              Pacientes em Situação de Emergência
              <span id="emergency-count" class="active-emergencies-count">0</span>
            </h3>
            
            <div id="emergencies-container">
              <div class="empty-state">
                Não há emergências ativas no momento. As novas solicitações aparecerão aqui automaticamente.
              </div>
            </div>
          </div>

          <div class="card quick-actions">
            <h3 class="section-title">Ações Rápidas</h3>
            <div class="menu-buttons">
              <a href="tel:192" class="button danger">Acionar SAMU (192)</a>
              <button class="button" id="contact-hospital-btn">Contatar Hospital de Referência</button>
              <button class="button outline" id="show-protocols-btn">Visualizar Protocolos de Emergência</button>
            </div>
          </div>
        </div>

        <div class="footer-desktop">
          <div class="footer-left">
            Menu - início - Perfil - Login - Contate - nos
          </div>
          <div class="footer-right">Cicatriza+</div>
        </div>
      </div>
    </div>

    <div class="mobile-navbar">
      <div class="mobile-nav-container">
        <a href="menu-principal-agente.html" class="mobile-nav-item">
          <div class="mobile-nav-icon">🏠</div>
          <div class="mobile-nav-text">Início</div>
        </a>
        <a href="gerenciar-pacientes.html" class="mobile-nav-item">
          <div class="mobile-nav-icon">👥</div>
          <div class="mobile-nav-text">Pacientes</div>
        </a>
        <a href="notificacoes-agente.html" class="mobile-nav-item">
          <div class="mobile-nav-icon">🔔</div>
          <div class="mobile-nav-text">Notificações</div>
        </a>
        <a href="perfil-agente.html" class="mobile-nav-item">
          <div class="mobile-nav-icon">👤</div>
          <div class="mobile-nav-text">Perfil</div>
        </a>
      </div>
    </div>

<div vw class="enabled">
  <div vw-access-button class="active"></div>
  <div vw-plugin-wrapper>
    <div class="vw-plugin-top-wrapper"></div>
  </div>
</div>
<script src="https://vlibras.gov.br/app/vlibras-plugin.js"></script>
<script>
  new window.VLibras.Widget("https://vlibras.gov.br/app");
  
      const emergenciesContainer = document.getElementById('emergencies-container');
      const emergencyCount = document.getElementById('emergency-count');
      
      let activeEmergencies = [];
      let checkInterval = null;
      
      const emergenciesBeingAttended = new Map();
      
      function getUserFromCookie() {
        const match = document.cookie.match('(^|;)\\s*user=([^;]+)');
        return match ? JSON.parse(decodeURIComponent(match[2])) : null;
      }
      
      function formatRelativeTime(datetime) {
        const now = new Date();
        const then = new Date(datetime);
        const diffInMinutes = Math.floor((now - then) / (1000 * 60));
        
        if (diffInMinutes < 1) return 'Agora mesmo';
        if (diffInMinutes === 1) return '1 minuto atrás';
        if (diffInMinutes < 60) return `${diffInMinutes} minutos atrás`;
        
        const diffInHours = Math.floor(diffInMinutes / 60);
        if (diffInHours === 1) return '1 hora atrás';
        if (diffInHours < 24) return `${diffInHours} horas atrás`;
        
        const diffInDays = Math.floor(diffInHours / 24);
        if (diffInDays === 1) return '1 dia atrás';
        return `${diffInDays} dias atrás`;
      }
      
      async function loadActiveEmergencies() {
        try {
          const response = await fetch('/api/emergencys?attended=false');
          if (!response.ok) {
            throw new Error('Falha ao buscar emergências');
          }
          
          const data = await response.json();
          
          const updatedEmergencies = data.map(emergency => {
            if (emergenciesBeingAttended.has(emergency.id)) {
              emergency._isBeingAttendedLocally = true;
            }
            return emergency;
          });
          
          activeEmergencies = updatedEmergencies;
          
          emergencyCount.textContent = activeEmergencies.length;
  
          renderEmergencies();
          
          if (!checkInterval) {
            checkInterval = setInterval(loadActiveEmergencies, 10000); // Verificar a cada 10 segundos
          }
          
        } catch (error) {
          console.error('Erro ao carregar emergências:', error);
        }
      }
      
      function renderEmergencies() {
        if (activeEmergencies.length === 0) {
          emergenciesContainer.innerHTML = `
            <div class="empty-state">
              Não há emergências ativas no momento. As novas solicitações aparecerão aqui automaticamente.
            </div>
          `;
          return;
        }
        
        const formsBeingFilled = new Map();
        
        activeEmergencies.forEach(emergency => {
          if (emergenciesBeingAttended.has(emergency.id)) {
            const obsTextarea = document.getElementById(`observations-${emergency.id}`);
            const refSelect = document.getElementById(`referral-${emergency.id}`);
            
            if (obsTextarea && refSelect) {
              formsBeingFilled.set(emergency.id, {
                observations: obsTextarea.value,
                referral: refSelect.value
              });
            }
          }
        });
        
        emergenciesContainer.innerHTML = '';
        
        activeEmergencies.forEach(emergency => {
          const isBeingAttended = emergenciesBeingAttended.has(emergency.id) || emergency._isBeingAttendedLocally;
          const savedForm = formsBeingFilled.get(emergency.id) || { observations: '', referral: '' };
          
          const card = document.createElement('div');
          card.className = `emergency-card ${isBeingAttended ? 'emergency-attending' : ''}`;
          card.id = `emergency-${emergency.id}`;
          
          card.innerHTML = `
            <div class="emergency-content">
              <div class="emergency-icon">🚨</div>
              <div class="emergency-details">
                <div class="emergency-title">EMERGÊNCIA ATIVA ${isBeingAttended ? '(Em Atendimento)' : ''}</div>
                <div class="emergency-reason">${emergency.reason}</div>
                <div class="patient-info">
                  <strong>Paciente:</strong> ${emergency.patient_name || `ID: ${emergency.patient_id}`}
                </div>
                <div class="time-info">
                  <strong>Solicitado:</strong> ${formatRelativeTime(emergency.call_datetime)}
                </div>
                <div class="emergency-actions" ${isBeingAttended ? 'style="display:none"' : ''}>
                  <button class="button danger" onclick="acceptEmergency(${emergency.id})">Atender Emergência</button>
                  <button class="button outline" onclick="showPatientDetails(${emergency.patient_id})">Detalhes do Paciente</button>
                </div>
                
                <!-- Formulário de observações (aparece após aceitar) -->
                <div id="observations-form-${emergency.id}" class="observations-form" ${isBeingAttended ? 'style="display:block"' : ''}>
                  <div class="form-group">
                    <label class="form-label" for="observations-${emergency.id}">Observações:</label>
                    <textarea id="observations-${emergency.id}" class="form-textarea" 
                      placeholder="Descreva suas observações sobre esta emergência">${savedForm.observations}</textarea>
                  </div>
                  
                  <div class="form-group">
                    <label class="form-label" for="referral-${emergency.id}">Encaminhamento:</label>
                    <select id="referral-${emergency.id}" class="form-select">
                      <option value="" ${savedForm.referral === '' ? 'selected' : ''}>Sem encaminhamento</option>
                      <option value="UPA" ${savedForm.referral === 'UPA' ? 'selected' : ''}>UPA</option>
                      <option value="Hospital Municipal" ${savedForm.referral === 'Hospital Municipal' ? 'selected' : ''}>Hospital Municipal</option>
                      <option value="SAMU" ${savedForm.referral === 'SAMU' ? 'selected' : ''}>SAMU</option>
                      <option value="Clínica especializada" ${savedForm.referral === 'Clínica especializada' ? 'selected' : ''}>Clínica especializada</option>
                      <option value="Observação domiciliar" ${savedForm.referral === 'Observação domiciliar' ? 'selected' : ''}>Observação domiciliar</option>
                    </select>
                  </div>
                  
                  <div class="form-actions">
                    <button class="button" onclick="submitObservations(${emergency.id})">Enviar</button>
                    <button class="button outline" onclick="cancelObservations(${emergency.id})">Cancelar</button>
                  </div>
                </div>
              </div>
            </div>
          `;
          
          emergenciesContainer.appendChild(card);
        });
      }
      
      // Aceitar uma emergência
      async function acceptEmergency(emergencyId) {
        try {
          const user = getUserFromCookie();
          if (!user || !user.id) {
            alert('Você precisa estar logado para atender uma emergência.');
            return;
          }
          
          const emergencyCard = document.getElementById(`emergency-${emergencyId}`);
          if (emergencyCard) {
            emergencyCard.classList.add('emergency-attending');
          }
          
          emergenciesBeingAttended.set(emergencyId, user.id);
          
          const observationsForm = document.getElementById(`observations-form-${emergencyId}`);
          if (observationsForm) {
            const emergencyActions = observationsForm.previousElementSibling;
            emergencyActions.style.display = 'none';
            observationsForm.style.display = 'block';
          }
          
          // Nota: NÃO atualizamos o backend neste momento para manter a emergência na lista
        } catch (error) {
          console.error('Erro ao aceitar emergência:', error);
          alert('Ocorreu um erro ao aceitar a emergência. Por favor, tente novamente.');
        }
      }
      
      async function submitObservations(emergencyId) {
        try {
          const observationsInput = document.getElementById(`observations-${emergencyId}`);
          const referralSelect = document.getElementById(`referral-${emergencyId}`);
          
          if (!observationsInput || !referralSelect) return;
          
          const observations = observationsInput.value.trim();
          const referral = referralSelect.value;
          
          if (observations === '') {
            alert('Por favor, preencha as observações antes de enviar.');
            return;
          }
          
          const agentId = emergenciesBeingAttended.get(emergencyId);
          if (!agentId) {
            alert('Erro: Não foi possível identificar o agente atendendo esta emergência.');
            return;
          }

          const currentEmergency = activeEmergencies.find(e => e.id === emergencyId);
          if (!currentEmergency) {
            alert('Erro: Não foi possível encontrar a emergência atual.');
            return;
          }
          
          const response = await fetch(`/api/emergencys/${emergencyId}`, {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              observations: observations,
              referral: referral || null,
              attended: true,
              agent_id: agentId
            })
          });
          
          if (!response.ok) {
            throw new Error('Erro ao atualizar emergência');
          }
          
          await createPatientNotification(currentEmergency.patient_id, observations, referral);
          
          const emergencyCard = document.getElementById(`emergency-${emergencyId}`);
          if (emergencyCard) {
            emergencyCard.remove();
            
            emergenciesBeingAttended.delete(emergencyId);
            
            activeEmergencies = activeEmergencies.filter(e => e.id !== emergencyId);
            emergencyCount.textContent = activeEmergencies.length;
            
            if (activeEmergencies.length === 0) {
              renderEmergencies();
            }
          }

          alert('Emergência atendida com sucesso. O paciente foi notificado.');
          
        } catch (error) {
          console.error('Erro ao enviar observações:', error);
          alert('Ocorreu um erro ao enviar as observações. Por favor, tente novamente.');
        }
      }
      
      async function createPatientNotification(patientId, observations, referral) {
        try {
          const user = getUserFromCookie();
          if (!user) return;
          
          const profName = user.full_name || 'Profissional de Saúde';
          
          let notificationText = `O profissional ${profName} atendeu sua emergência.\n\n`;
          notificationText += `Observações: ${observations}\n\n`;
          
          if (referral && referral !== '') {
            notificationText += `Encaminhamento: ${referral}`;
          } else {
            notificationText += 'Não foi necessário encaminhamento.';
          }
          
          const now = new Date().toISOString();
          
          const notification = {
            type: 'Alerta',
            content: notificationText,
            creation_date: now,
            sent_date: now,
            viewed: false,
            view_date: null,
            patient_id: patientId
          };
          
          const response = await fetch('/api/system-notifications', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(notification)
          });
          
          if (!response.ok) {
            console.error('Erro ao criar notificação para o paciente:', await response.text());
            return false;
          }
          
          return true;
        } catch (error) {
          console.error('Erro ao criar notificação para o paciente:', error);
          return false;
        }
      }
      
      function cancelObservations(emergencyId) {
        const observationsForm = document.getElementById(`observations-form-${emergencyId}`);
        if (observationsForm) {
          const emergencyActions = observationsForm.previousElementSibling;
          emergencyActions.style.display = 'flex';
          observationsForm.style.display = 'none';
          
          const emergencyCard = document.getElementById(`emergency-${emergencyId}`);
          if (emergencyCard) {
            emergencyCard.classList.remove('emergency-attending');
          }
          
          emergenciesBeingAttended.delete(emergencyId);
        }
      }
      
      function showPatientDetails(patientId) {
        window.location.href = `paciente-detalhes.html?id=${patientId}`;
      }
      
      window.addEventListener('DOMContentLoaded', loadActiveEmergencies);
      
    document.getElementById('contact-hospital-btn').addEventListener('click', function() {
        alert('Funcionalidade para contatar hospital será implementada em breve.');
      });
      
      document.getElementById('show-protocols-btn').addEventListener('click', function() {
        alert('Protocolos de emergência serão exibidos em breve.');
      });
    </script>
  </body>
</html>

