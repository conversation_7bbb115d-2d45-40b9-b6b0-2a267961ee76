const express = require("express");
const router = express.Router();
console.log("[DEBUG] patientComorbidityRoutes carregado");

const PatientComorbidityRepository = require("../repositories/patientComorbidityRepository");
const PatientComorbidityService = require("../services/patientComorbidityService");
const PatientComorbidityController = require("../controllers/patientComorbidityController");

const controller = new PatientComorbidityController(
  new PatientComorbidityService(new PatientComorbidityRepository())
);
router.get("/", async (req, res) => {
  try {
    
    if (req.query.patient_id) {
      
      if (req.query.type) {
        const results =
          await controller.service.repository.findByPatientAndType(
            req.query.patient_id,
            req.query.type
          );
        return res.json(results);
      }

      
      const results = await controller.service.repository.findByPatient(
        req.query.patient_id
      );
      return res.json(results);
    }

    
    return controller.index(req, res);
  } catch (error) {
    console.error("[PatientComorbidityRoutes] - Erro:", error.message);
    return res.status(500).json({ error: error.message });
  }
});
router.get("/:id", controller.show.bind(controller));
router.post("/", controller.create.bind(controller));
router.put("/:id", controller.update.bind(controller));

module.exports = router;
