<!-- <!DOCTYPE html>
<html lang="pt-BR">
  <head>
    <!-- <meta charset="UTF-8" /> -->
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" href="../../../public/css/global.css" />
    <title>Enviar Foto da Ferida</title>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&display=swap"
      rel="stylesheet"
    />
    <script src="../../../public/js/auth-check.js"></script>
    <style>
      .upload-container {
        background: white;
        border-radius: 12px;
        padding: 30px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
        text-align: center;
      }
      .upload-area {
        border: 2px dashed #013c6d;
        border-radius: 12px;
        padding: 40px 20px;
        background-color: #f8fafc;
        margin-bottom: 20px;
        cursor: pointer;
        transition: all 0.3s ease;
      }
      .upload-area:hover {
        background-color: #ebf4ff;
        border-color: #3182ce;
      }
      .upload-icon {
        font-size: 48px;
        color: #013c6d;
        margin-bottom: 16px;
      }
      .upload-text {
        font-size: 18px;
        font-weight: 600;
        color: #2d3748;
        margin-bottom: 8px;
      }
      .upload-subtext {
        font-size: 14px;
        color: #718096;
      }
      .photo-preview {
        max-width: 100%;
        max-height: 300px;
        border-radius: 8px;
        margin: 16px 0;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }
      .form-section {
        background: white;
        border-radius: 12px;
        padding: 20px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
      }
      .form-title {
        font-size: 18px;
        font-weight: 600;
        color: #2d3748;
        margin-bottom: 16px;
      }
      .form-group {
        margin-bottom: 16px;
      }
      .form-label {
        display: block;
        font-weight: 500;
        color: #2d3748;
        margin-bottom: 8px;
      }
      .form-input {
        width: 100%;
        padding: 12px 16px;
        border: 1px solid #e2e8f0;
        border-radius: 8px;
        font-size: 14px;
        transition: border-color 0.2s;
      }
      .form-input:focus {
        outline: none;
        border-color: #013c6d;
        box-shadow: 0 0 0 3px rgba(1, 60, 109, 0.1);
      }
      .form-textarea {
        min-height: 100px;
        resize: vertical;
      }
      .pain-scale {
        display: flex;
        gap: 8px;
        margin-top: 8px;
      }
      .pain-level {
        width: 40px;
        height: 40px;
        border: 2px solid #e2e8f0;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        font-weight: 600;
        transition: all 0.2s;
      }
      .pain-level:hover {
        border-color: #013c6d;
      }
      .pain-level.selected {
        background-color: #013c6d;
        color: white;
        border-color: #013c6d;
      }
      .guidelines {
        background: #ebf4ff;
        border-radius: 8px;
        padding: 16px;
        margin-bottom: 20px;
        border-left: 4px solid #3182ce;
      }
      .guidelines-title {
        font-weight: 600;
        color: #2d3748;
        margin-bottom: 8px;
      }
      .guidelines-list {
        list-style: none;
        padding: 0;
      }
      .guidelines-list li {
        margin-bottom: 4px;
        color: #4a5568;
        font-size: 14px;
      }
      .guidelines-list li:before {
        content: "✓ ";
        color: #38a169;
        font-weight: 600;
        margin-right: 8px;
      }
      .action-buttons {
        display: flex;
        gap: 12px;
        justify-content: center;
        margin-top: 20px;
      }
      .notification {
        padding: 12px;
        border-radius: 8px;
        background-color: #f0fff4;
        color: #2f855a;
        border: 1px solid #c6f6d5;
        margin-bottom: 16px;
      }
      .checkbox-group {
        margin-top: 8px;
      }
      .checkbox-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: 10px;
        margin-top: 12px;
      }

      .checkbox-label {
        display: flex;
        align-items: center;
        padding: 8px 12px;
        background-color: #f8fafc;
        border-radius: 6px;
        border: 1px solid #e2e8f0;
        cursor: pointer;
        transition: all 0.2s;
      }

      .checkbox-label:hover {
        background-color: #edf2f7;
      }

      .checkbox-label input[type="checkbox"] {
        margin-right: 10px;
        width: 16px;
        height: 16px;
      }

      .form-hint {
        font-size: 14px;
        color: #718096;
        margin-top: 4px;
        margin-bottom: 8px;
      }
      .progress-steps {
        display: flex;
        justify-content: space-between;
        margin-bottom: 30px;
        position: relative;
      }
      .progress-step {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: #e2e8f0;
        color: #4a5568;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        z-index: 2;
      }
      .progress-step.active {
        background-color: #013c6d;
        color: white;
      }
      .progress-step.completed {
        background-color: #38a169;
        color: white;
      }
      .progress-bar {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        left: 20px;
        right: 20px;
        height: 4px;
        background-color: #e2e8f0;
        z-index: 1;
      }
      .progress-bar-filled {
        height: 100%;
        background-color: #013c6d;
        width: 0%;
        transition: width 0.3s;
      }
      .form-step {
        display: none;
      }
      .form-step.active {
        display: block;
        animation: fadeIn 0.5s;
      }
      @keyframes fadeIn {
        from {
          opacity: 0;
        }
        to {
          opacity: 1;
        }
      }
      .radio-group {
        display: flex;
        gap: 16px;
        margin-top: 8px;
      }
      .radio-label {
        display: flex;
        align-items: center;
      }
      .radio-label input {
        margin-right: 6px;
      }
    </style>
  </head>
  <body>
    <div class="desktop-frame">
      <div class="sidebar">
        <div class="sidebar-logo">
          <img
            src="../../../public/images/logo/cicatriza_semnada.png"
            alt="Logo"
            class="logo-small"
          />
          <h3 class="sidebar-title">Cicatriza+</h3>
        </div>
        <ul class="sidebar-menu">
          <a href="menu-principal.html">
            <li class="sidebar-item">
              <span class="sidebar-icon">🏠</span>Início
            </li>
          </a>
          <a href="tutorial-paciente.html">
            <li class="sidebar-item">
              <span class="sidebar-icon">📝</span>Tutorial
            </li>
          </a>
          <a href="enviar-foto-paciente.html">
            <li class="sidebar-item">
              <span class="sidebar-icon">📷</span>Enviar foto
            </li>
          </a>
          <a href="videos-paciente.html">
            <li class="sidebar-item">
              <span class="sidebar-icon">🎥</span>Vídeos
            </li>
          </a>
          <a href="perfil-paciente.html">
            <li class="sidebar-item">
              <span class="sidebar-icon">👤</span>Perfil
            </li>
          </a>
          <a href="notificacoes-paciente.html">
            <li class="sidebar-item">
              <span class="sidebar-icon">🔔</span>Notificações
            </li>
          </a>
          <a href="emergencia.html">
            <li class="sidebar-item">
              <span class="sidebar-icon">⚠️</span>Emergência
            </li>
          </a>
        </ul>
      </div>
      <div class="desktop-content">
        <div class="desktop-header">
          <div class="header-title">Enviar Foto da Ferida</div>
        </div>
        <div class="main-area">
          <div
            id="submission-message"
            class="notification"
            style="display: none; color: green; margin-bottom: 16px"
          ></div>

          <div class="progress-steps">
            <div class="progress-bar">
              <div class="progress-bar-filled" id="progress-bar-fill"></div>
            </div>
            <div class="progress-step active" id="step1">1</div>
            <div class="progress-step" id="step2">2</div>
            <div class="progress-step" id="step3">3</div>
          </div>

          <form id="photo-form">
            <div class="form-step active" id="step1-content">
              <div class="form-section"></div>
                <div class="form-title">Cadastro de Ferida</div>
                <div class="form-group"></div>
                  <label class="form-label">Tipo de Ferida</label>
                  <select id="wound-type" class="form-input" required>
                    <option value="">Selecione</option>
                    <option value="úlcera pé diabético">
                      Úlcera de pé diabético
                    </option>
                    <option value="úlcera venosa">Úlcera venosa</option>
                    <option value="lesão por pressão">Lesão por pressão</option>
                    <option value="queimadura">Queimadura</option>
                    <option value="ferida cirúrgica">Ferida cirúrgica</option>
                  </select>
                </div>
                <div class="form-group"></div>
                  <label class="form-label">Localização da Ferida</label>
                  <input
                    id="wound-location"
                    class="form-input"
                    type="text"
                    placeholder="Ex: Tornozelo esquerdo"
                    required
                  />
                </div>
                <div class="form-group"></div>
                  <label class="form-label">Data de Início</label>
                  <input
                    id="first-occurrence-date"
                    class="form-input"
                    type="date"
                    required
                  />
                </div>
              </div>
              <div class="action-buttons"></div>
                <button type="button" class="button" onclick="nextStep(1)">
                  Próxima
                </button>
              </div>
            </div>

            <div class="form-step" id="step2-content">
              <div class="form-section">
                <div class="form-title">Avaliação da Ferida</div>

                <div class="form-group">
                  <label class="form-label"
                    >Nível de dor (0 = sem dor, 10 = dor máxima)</label
                  >
                  <div class="pain-scale"></div>
                    <div class="pain-level" onclick="selectPainLevel(this, 0)">
                      0
                    </div>
                    <div class="pain-level" onclick="selectPainLevel(this, 1)">
                      1
                    </div>
                    <div class="pain-level" onclick="selectPainLevel(this, 2)">
                      2
                    </div>
                    <div class="pain-level" onclick="selectPainLevel(this, 3)">
                      3
                    </div>
                    <div class="pain-level" onclick="selectPainLevel(this, 4)">
                      4
                    </div>
                    <div class="pain-level" onclick="selectPainLevel(this, 5)">
                      5
                    </div>
                    <div class="pain-level" onclick="selectPainLevel(this, 6)">
                      6
                    </div>
                    <div class="pain-level" onclick="selectPainLevel(this, 7)">
                      7
                    </div>
                    <div class="pain-level" onclick="selectPainLevel(this, 8)">
                      8
                    </div>
                    <div class="pain-level" onclick="selectPainLevel(this, 9)">
                      9
                    </div>
                    <div class="pain-level" onclick="selectPainLevel(this, 10)">
                      10
                    </div>
                  </div>
                </div>

                <div class="form-group">
                  <label class="form-label">A ferida apresenta secreção?</label>
                  <div class="radio-group">
                    <label class="radio-label">
                      <input type="radio" name="has-discharge" value="true" />
                      Sim
                    </label>
                    <label class="radio-label">
                      <input type="radio" name="has-discharge" value="false" />
                      Não
                    </label>
                  </div>
                </div>

                <div class="form-group">
                  <label class="form-label">Existe odor na ferida?</label>
                  <select id="odor" class="form-input">
                    <option value="nenhum">Nenhum</option>
                    <option value="leve">Leve</option>
                    <option value="forte">Forte</option>
                  </select>
                </div>

                <div class="form-group"></div>
                  <label class="form-label">Você está com febre?</label>
                  <div class="radio-group">
                    <label class="radio-label">
                      <input type="radio" name="has-fever" value="true" /> Sim
                    </label>
                    <label class="radio-label">
                      <input type="radio" name="has-fever" value="false" /> Não
                    </label>
                  </div>
                </div>

                <div class="form-group">
                  <label class="form-label"
                    >Existe vermelhidão ao redor da ferida?</label
                  >
                  <div class="radio-group">
                    <label class="radio-label">
                      <input
                        type="radio"
                        name="surrounding-redness"
                        value="true"
                      />
                      Sim
                    </label>
                    <label class="radio-label">
                      <input
                        type="radio"
                        name="surrounding-redness"
                        value="false"
                      />
                      Não
                    </label>
                  </div>
                </div>

                <div class="form-group"></div>
                  <label class="form-label">Há inchaço na região?</label>
                  <div class="radio-group">
                    <label class="radio-label">
                      <input type="radio" name="swelling" value="true" /> Sim
                    </label>
                    <label class="radio-label">
                      <input type="radio" name="swelling" value="false" /> Não
                    </label>
                  </div>
                </div>

                <div class="form-group">
                  <label class="form-label"
                    >Outros sintomas ou observações</label
                  >
                  <textarea
                    id="other-symptoms"
                    class="form-input form-textarea"
                    placeholder="Descreva qualquer outro sintoma ou observação importante"
                  ></textarea>
                </div>

                <div class="form-group">
                  <label class="form-label"
                    >Você aplicou o curativo recomendado?</label
                  >
                  <div class="radio-group">
                    <label class="radio-label">
                      <input
                        type="radio"
                        name="applied-dressing"
                        value="true"
                      />
                      Sim
                    </label>
                    <label class="radio-label">
                      <input
                        type="radio"
                        name="applied-dressing"
                        value="false"
                      />
                      Não
                    </label>
                  </div>
                </div>

                <div class="form-group"></div>
                  <label class="form-label"
                    >Teve dificuldades no tratamento?</label
                  >
                  <textarea
                    id="treatment-difficulties"
                    class="form-input form-textarea"
                    placeholder="Descreva eventuais dificuldades no tratamento da ferida"
                  ></textarea>
                </div>

                <div class="form-group"></div>
                  <label class="form-label">Condições de Saúde</label>
                  <p class="form-hint">
                    Selecione todas as condições de saúde que você possui:
                  </p>

                  <div class="checkbox-grid">
                    <label class="checkbox-label">
                      <input
                        type="checkbox"
                        name="comorbidities"
                        value="diabetes"
                        id="comorbidity-diabetes"
                      />
                      Diabetes
                    </label>

                    <label class="checkbox-label">
                      <input
                        type="checkbox"
                        name="comorbidities"
                        value="hipertensão"
                        id="comorbidity-hipertensao"
                      />
                      Hipertensão
                    </label>

                    <label class="checkbox-label">
                      <input
                        type="checkbox"
                        name="comorbidities"
                        value="obesidade"
                        id="comorbidity-obesidade"
                      />
                      Obesidade
                    </label>

                    <label class="checkbox-label">
                      <input
                        type="checkbox"
                        name="comorbidities"
                        value="asma"
                        id="comorbidity-asma"
                      />
                      Asma
                    </label>

                    <label class="checkbox-label">
                      <input
                        type="checkbox"
                        name="comorbidities"
                        value="artrite reumatoide"
                        id="comorbidity-artrite"
                      />
                      Artrite Reumatoide
                    </label>

                    <label class="checkbox-label">
                      <input
                        type="checkbox"
                        name="comorbidities"
                        value="doenca renal"
                        id="comorbidity-renal"
                      />
                      Doença Renal
                    </label>
                  </div>

                  <div class="form-group" style="margin-top: 12px">
                    <label class="form-label">Outras condições de saúde</label>
                    <textarea
                      id="other-comorbidities"
                      class="form-input form-textarea"
                      placeholder="Informe outras condições de saúde que não estão na lista"
                    ></textarea>
                  </div>
                </div>
              </div>

              <div class="action-buttons">
                <button
                  type="button"
                  class="button outline"
                  onclick="prevStep(2)"
                >
                  Anterior
                </button>
                <button type="button" class="button" onclick="nextStep(2)">
                  Próxima
                </button>
              </div>
            </div>

            <div class="form-step" id="step3-content">
              <div class="guidelines">
                <div class="guidelines-title"></div>
                  📋 Orientações para uma boa foto:
                </div>
                <ul class="guidelines-list"></ul>
                  <li>Use boa iluminação natural (próximo a uma janela)</li>
                  <li>Limpe a ferida antes de fotografar</li>
                  <li>Mantenha o celular estável e próximo à ferida</li>
                  <li>Tire a foto de frente para a ferida</li>
                  <li>Evite sombras sobre a área fotografada</li>
                </ul>
              </div>

              <div class="upload-container">
                <div
                  class="upload-area"
                  onclick="document.getElementById('photo-input').click()"
                >
                  <div class="upload-icon">📷</div>
                  <div class="upload-text"></div>
                    Clique aqui para tirar ou selecionar uma foto
                  </div>
                  <div class="upload-subtext"></div>
                    Formatos aceitos: JPG, PNG (máx. 10MB)
                  </div>
                </div>
                <input
                  type="file"
                  id="photo-input"
                  name="photo"
                  accept="image/*"
                  capture="environment"
                  style="display: none"
                  required
                />
                <div id="photo-preview" style="display: none"></div>
                  <img id="preview-image" class="photo-preview" />
                  <button
                    type="button"
                    class="button outline"
                    onclick="removePhoto()"
                  >
                    Remover Foto
                  </button>
                </div>
              </div>

              <div class="form-group"></div>
                <label class="form-label">Tamanho da ferida (mm)</label>
                <input
                  id="wound-size"
                  class="form-input"
                  type="number"
                  placeholder="Ex: 25.4"
                  step="0.1"
                  required
                />
              </div>

              <div class="action-buttons">
                <button
                  type="button"
                  class="button outline"
                  onclick="prevStep(3)"
                >
                  Anterior
                </button>
                <button type="submit" class="button">
                  Enviar para Análise
                </button>
              </div>
            </div>
          </form>
        </div>

        <div class="footer-desktop">
          <div class="footer-left"></div>
            Menu - início - Perfil - Login - Contate - nos
          </div>
          <div class="footer-right">Cicatriza+</div>
        </div>
      </div>
    </div>

    <div class="mobile-navbar">
      <div class="mobile-nav-container"></div>
    </div>

    <div class="mobile-navbar">
      <div class="mobile-nav-container">
        <a href="menu-principal.html" class="mobile-nav-item active">
          <div class="mobile-nav-icon">🏠</div>
          <div class="mobile-nav-text">Início</div>
        </a>
        <a href="consultas-paciente.html" class="mobile-nav-item">
          <div class="mobile-nav-icon">📋</div>
          <div class="mobile-nav-text">Consultas</div>
        </a>
        <a href="saude-paciente.html" class="mobile-nav-item">
          <div class="mobile-nav-icon">📊</div>
          <div class="mobile-nav-text">Saúde</div>
        </a>
        <a href="perfil-paciente.html" class="mobile-nav-item">
          <div class="mobile-nav-icon">👤</div>
          <div class="mobile-nav-text">Perfil</div>
        </a>
      </div>
    </div>

    <script>
      let currentStep = 1;
      let painLevel = 0;

      function selectPainLevel(element, level) {
        document
          .querySelectorAll(".pain-level")
          .forEach((el) => el.classList.remove("selected"));
        element.classList.add("selected");
        painLevel = level;
      }

      function removePhoto() {
        document.getElementById("photo-preview").style.display = "none";
        document.getElementById("photo-input").value = "";
      }

      function nextStep(step) {
        
        if (step === 1) {
          if (
            !document.getElementById("wound-type").value ||
            !document.getElementById("wound-location").value ||
            !document.getElementById("first-occurrence-date").value
          ) {
            alert("Por favor, preencha todos os campos obrigatórios.");
            return;
          }
        }

        
        document
          .getElementById(`step${step}-content`)
          .classList.remove("active");
        document.getElementById(`step${step}`).classList.add("completed");
        currentStep = step + 1;
        document.getElementById(`step${currentStep}`).classList.add("active");
        document
          .getElementById(`step${currentStep}-content`)
          .classList.add("active");

        
        const progressPercentage = ((currentStep - 1) / 2) * 100;
        document.getElementById(
          "progress-bar-fill"
        ).style.width = `${progressPercentage}%`;
      }

      function prevStep(step) {
        document
          .getElementById(`step${step}-content`)
          .classList.remove("active");
        document.getElementById(`step${step}`).classList.remove("active");
        currentStep = step - 1;
        document.getElementById(`step${currentStep}`).classList.add("active");
        document
          .getElementById(`step${currentStep}-content`)
          .classList.add("active");

        
        const progressPercentage = ((currentStep - 1) / 2) * 100;
        document.getElementById(
          "progress-bar-fill"
        ).style.width = `${progressPercentage}%`;
      }

      document
        .getElementById("photo-input")
        .addEventListener("change", function (e) {
          const file = e.target.files[0];
          if (file) {
            const reader = new FileReader();
            reader.onload = function (e) {
              document.getElementById("preview-image").src = e.target.result;
              document.getElementById("photo-preview").style.display = "block";
            };
            reader.readAsDataURL(file);
          }
        });

      
      function getUserFromCookie() {
        const m = document.cookie
          .split("; ")
          .find((r) => r.startsWith("user="));
        return m ? JSON.parse(decodeURIComponent(m.split("=")[1])) : null;
      }

      
      document
        .getElementById("photo-form")
        .addEventListener("submit", async (e) => {
          e.preventDefault();
          const usr = getUserFromCookie();
          try {
            
            const wound = await createWound(usr.id);

            
            await createPatientQuestionnaire(usr.id);

            
            await savePatientComorbidities(usr.id);

            
            await createPhotoRecord(wound.id);

            document.getElementById("submission-message").textContent =
              "Questionário e foto enviados com sucesso! Um profissional irá avaliar suas informações.";
            document.getElementById("submission-message").style.display =
              "block";
            document.getElementById("photo-form").reset();
            document.getElementById("photo-preview").style.display = "none";

            
            document
              .querySelectorAll(".form-step")
              .forEach((step) => step.classList.remove("active"));
            document.querySelectorAll(".progress-step").forEach((step) => {
              step.classList.remove("active");
              step.classList.remove("completed");
            });
            document.getElementById("step1").classList.add("active");
            document.getElementById("step1-content").classList.add("active");
            document.getElementById("progress-bar-fill").style.width = "0%";
            currentStep = 1;
          } catch (err) {
            console.error(err);
            alert("Erro ao enviar dados: " + err.message);
          }
        });

      
      async function savePatientComorbidities(patientId) {
        try {
          
          const selectedComorbidities = Array.from(
            document.querySelectorAll('input[name="comorbidities"]:checked')
          ).map((checkbox) => checkbox.value);

          
          const otherComorbidities = document.getElementById("other-comorbidities").value;

          
          if (otherComorbidities && otherComorbidities.trim()) {
            const otherItems = otherComorbidities
              .split(",")
              .map((item) => item.trim())
              .filter((item) => item.length > 0);

            selectedComorbidities.push(...otherItems);
          }

          
          if (selectedComorbidities.length === 0) {
            return;
          }

          
          const currentResponse = await fetch(`/api/comorbidity?patient_id=${patientId}`);
          const currentComorbidities = currentResponse.ok ? await currentResponse.json() : [];
          
          
          const existingTypes = currentComorbidities.map(c => 
            c.comorbidity_type.toLowerCase()
          );

          
          const comorbidityPromises = selectedComorbidities
            .filter(type => !existingTypes.includes(type.toLowerCase()))
            .map(async (type) => {
              const payload = {
                comorbidity_type: type,
                diagnosis_date: new Date().toISOString().split("T")[0],
                under_treatment: true,
                observations: "Registrado pelo paciente",
                patient_id: patientId,
              };

              
              const res = await fetch("/api/comorbidity", {
                method: "POST",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify(payload),
              });

              if (!res.ok) {
                const errText = await res.text();
                console.warn(`Falha ao registrar comorbidade ${type}: ${errText}`);
                return null;
              }

              return res.json();
            });

          
          const results = await Promise.all(comorbidityPromises);
          const successCount = results.filter(r => r !== null).length;
          console.log(`Comorbidades atualizadas com sucesso: ${successCount} novas condições adicionadas`);
          
          return results.filter(r => r !== null);
        } catch (err) {
          
          console.error("Erro ao salvar comorbidades:", err);
        }
      }

      async function createWound(patientId) {
        const payload = {
          wound_type: document.getElementById("wound-type").value,
          anatomical_location: document.getElementById("wound-location").value,
          first_occurrence_date: document.getElementById(
            "first-occurrence-date"
          ).value,
          severity_level: 1, 
          status: "ativa",
          registration_date: new Date().toISOString(),
          patient_id: patientId,
        };
        const res = await fetch("/api/wounds", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(payload),
        });
        if (!res.ok) throw new Error("Falha ao criar ferida");
        return res.json();
      }

      async function createPatientQuestionnaire(patientId) {
        try {
          
          let mappedPainLevel = 1; 
          if (painLevel >= 0 && painLevel <= 2) mappedPainLevel = 1;
          else if (painLevel >= 3 && painLevel <= 4) mappedPainLevel = 2;
          else if (painLevel >= 5 && painLevel <= 6) mappedPainLevel = 3;
          else if (painLevel >= 7 && painLevel <= 8) mappedPainLevel = 4;
          else if (painLevel >= 9) mappedPainLevel = 5;

          
          
          const payload = {
            completion_date: new Date().toISOString(),
            pain_level: mappedPainLevel,
            has_discharge: "false", 
            odor: document.getElementById("odor").value || "nenhum",
            has_fever: "false", 
            surrounding_redness: "false", 
            swelling: "false", 
            other_symptoms:
              document.getElementById("other-symptoms").value || "",
            applied_recommended_dressing: "false", 
            treatment_difficulties:
              document.getElementById("treatment-difficulties").value || "",
            patient_id: patientId,
          };

          
          if (document.querySelector('input[name="has-discharge"]:checked')) {
            payload.has_discharge = document.querySelector(
              'input[name="has-discharge"]:checked'
            ).value;
          }
          if (document.querySelector('input[name="has-fever"]:checked')) {
            payload.has_fever = document.querySelector(
              'input[name="has-fever"]:checked'
            ).value;
          }
          if (
            document.querySelector('input[name="surrounding-redness"]:checked')
          ) {
            payload.surrounding_redness = document.querySelector(
              'input[name="surrounding-redness"]:checked'
            ).value;
          }
          if (document.querySelector('input[name="swelling"]:checked')) {
            payload.swelling = document.querySelector(
              'input[name="swelling"]:checked'
            ).value;
          }
          if (
            document.querySelector('input[name="applied-dressing"]:checked')
          ) {
            payload.applied_recommended_dressing = document.querySelector(
              'input[name="applied-dressing"]:checked'
            ).value;
          }

          console.log("Enviando payload:", JSON.stringify(payload, null, 2));

          const res = await fetch("/api/patient-questionnaire", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify(payload),
          });

          if (!res.ok) {
            const errText = await res.text();
            console.error("Erro ao salvar questionário:", res.status, errText);
            try {
              const jsonError = JSON.parse(errText);
              throw new Error(
                jsonError.error || "Falha ao salvar questionário"
              );
            } catch (jsonErr) {
              throw new Error(
                "Falha ao salvar questionário: " + errText.substring(0, 100)
              );
            }
          }

          let responseData;
          try {
            const responseText = await res.text();
            responseData = responseText ? JSON.parse(responseText) : {};
          } catch (jsonErr) {
            console.error("Erro ao parsear resposta JSON:", jsonErr);
            throw new Error("Resposta inválida do servidor");
          }

          return responseData;
        } catch (err) {
          console.error("Erro detalhado:", err);
          throw err;
        }
      }

      async function createPhotoRecord(woundId) {
        const fd = new FormData();
        const photoInput = document.getElementById("photo-input");

        fd.append("photo", photoInput.files[0]);
        fd.append("wound_size_mm", document.getElementById("wound-size").value);
        fd.append("sent_by_patient", "true");
        fd.append("wound_id", woundId);
        fd.append("pain_level", painLevel);
        fd.append(
          "observations",
          document.getElementById("other-symptoms").value
        );

        const resPhoto = await fetch("/api/photo-records", {
          method: "POST",
          body: fd,
        });

        if (!resPhoto.ok) {
          const errText = await resPhoto.text();
          throw new Error(errText || `Erro ${resPhoto.status}`);
        }

        return resPhoto.json();
      }

      document.addEventListener("DOMContentLoaded", async function () {
        try {
          const usr = getUserFromCookie();
          if (!usr) return;

          const response = await fetch(`/api/comorbidity?patient_id=${usr.id}`);
          if (!response.ok) return;

          const comorbidities = await response.json();
          if (!comorbidities || comorbidities.length === 0) return;

          
          comorbidities.forEach((comorbidity) => {
            const value = comorbidity.comorbidity_type.toLowerCase();
            const checkbox = document.querySelector(
              `input[name="comorbidities"][value="${value}"]`
            );

            if (checkbox) {
              checkbox.checked = true;
            } else {
              const otherComorbidities = document.getElementById(
                "other-comorbidities"
              );
              const currentValue = otherComorbidities.value;
              otherComorbidities.value = currentValue
                ? `${currentValue}, ${comorbidity.comorbidity_type}`
                : comorbidity.comorbidity_type;
            }
          });
        } catch (error) {
          console.error("Erro ao carregar comorbidades:", error);
        }
      });
    </script>

    <div vw class="enabled">
      <div vw-access-button class="active"></div>
      <div vw-plugin-wrapper>
        <div class="vw-plugin-top-wrapper"></div>
      </div>
    </div>
    <script src="https://vlibras.gov.br/app/vlibras-plugin.js"></script>
    <script>
      new window.VLibras.Widget("https://vlibras.gov.br/app");
    </script>
  </body>
</html>
      <div vw-plugin-wrapper>
        <div class="vw-plugin-top-wrapper"></div>
      </div>
    </div>
    <script src="https://vlibras.gov.br/app/vlibras-plugin.js"></script>
    <script>
      new window.VLibras.Widget("https://vlibras.gov.br/app");
    </script>
  </body>
</html>
