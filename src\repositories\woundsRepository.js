const db = require("../config/db");

class WoundsRepository {
  async findAll() {
    const result = await db.query(
      "SELECT id, wound_type, anatomical_location, first_occurrence_date, severity_level, status, registration_date, patient_id FROM wound"
    );
    return result.rows;
  }

  async findById(id) {
    const query =
      "SELECT id, wound_type, anatomical_location, first_occurrence_date, severity_level, status, registration_date, patient_id FROM wound WHERE id = $1";
    const result = await db.query(query, [id]);
    if (result.rows.length === 0) {
      throw new Error("Ferida não encontrada");
    }
    return result.rows[0];
  }

  async create(wounds) {
    if (
      !wounds.wound_type ||
      !wounds.anatomical_location ||
      !wounds.first_occurrence_date ||
      !wounds.severity_level ||
      !wounds.status ||
      !wounds.registration_date ||
      !wounds.patient_id
    ) {
      throw new Error(
        "Campos obrigatórios não preenchidos: wound_type, anatomical_location, first_occurrence_date, severity_level, status e patient_id são obrigatórios"
      );
    }

    const query =
      "INSERT INTO wound (wound_type, anatomical_location, first_occurrence_date, severity_level, status, registration_date, patient_id) VALUES ($1, $2, $3, $4, $5, $6, $7) RETURNING *";
    const result = await db.query(query, [
      wounds.wound_type,
      wounds.anatomical_location,
      wounds.first_occurrence_date,
      wounds.severity_level,
      wounds.status,
      wounds.registration_date,
      wounds.patient_id,
    ]);

    return result.rows[0];
  }

  async update(id, wounds) {
    const query =
      "UPDATE wound SET wound_type = $1, anatomical_location = $2, first_occurrence_date = $3, severity_level = $4, status = $5, registration_date = $6, patient_id = $7 WHERE id = $8 RETURNING *";
    const result = await db.query(query, [
      wounds.wound_type,
      wounds.anatomical_location,
      wounds.first_occurrence_date,
      wounds.severity_level,
      wounds.status,
      wounds.registration_date,
      wounds.patient_id,
      id,
    ]);

    if (result.rowCount === 0) {
      throw new Error("Ferida não encontrada");
    }
    return result.rows[0];
  }

  async delete(id) {
    const query = "DELETE FROM wound WHERE id = $1";
    const result = await db.query(query, [id]);

    if (result.rowCount === 0) {
      throw new Error("Ferida não encontrada");
    }
    return result.rows[0];
  }

  async findByPatient(patientId) {
    const query = `
          SELECT id, wound_type, anatomical_location, first_occurrence_date, severity_level, status, registration_date, patient_id
          FROM wound WHERE patient_id = $1
        `;
    const result = await db.query(query, [patientId]);
    return result.rows;
  }
}

module.exports = WoundsRepository;
