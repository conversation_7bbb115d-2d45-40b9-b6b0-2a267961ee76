const db = require("../config/db");

class EmergencyRepository {
  async findAll() {
    const result = await db.query(`
            SELECT e.id, e.call_datetime, e.reason, e.attended, e.observations, 
                   e.referral, e.patient_id, e.agent_id,
                   p.full_name as patient_name, ha.full_name as agent_name
            FROM emergency e
            LEFT JOIN patient p ON e.patient_id = p.id
            LEFT JOIN health_agent ha ON e.agent_id = ha.id
        `);
    return result.rows;
  }

  async findById(id) {
    const query = `
            SELECT e.id, e.call_datetime, e.reason, e.attended, e.observations, 
                   e.referral, e.patient_id, e.agent_id,
                   p.full_name as patient_name, ha.full_name as agent_name
            FROM emergency e
            LEFT JOIN patient p ON e.patient_id = p.id
            LEFT JOIN health_agent ha ON e.agent_id = ha.id
            WHERE e.id = $1
        `;
    const result = await db.query(query, [id]);
    if (result.rows.length === 0) {
      throw new Error("Emergência não encontrada");
    }
    return result.rows[0];
  }

  async create(emergency) {
    const query = `
            INSERT INTO emergency (call_datetime, reason, attended, observations, referral, patient_id, agent_id)
            VALUES ($1, $2, $3, $4, $5, $6, $7)
            RETURNING *
        `;

    const values = [
      emergency.call_datetime,
      emergency.reason,
      emergency.attended,
      emergency.observations,
      emergency.referral,
      emergency.patient_id,
      emergency.agent_id,
    ];

    const result = await db.query(query, values);
    return result.rows[0];
  }

  async update(id, emergency) {
    const existingEmergency = await this.findById(id);
    const updatedEmergency = {
      ...existingEmergency,
      ...emergency,
    };

    const query = `
            UPDATE emergency
            SET attended = $1,
                observations = $2,
                referral = $3,
                agent_id = $4
            WHERE id = $5
            RETURNING *
        `;

    const values = [
      updatedEmergency.attended,
      updatedEmergency.observations,
      updatedEmergency.referral,
      updatedEmergency.agent_id,
      id,
    ];

    const result = await db.query(query, values);
    if (result.rows.length === 0) {
      throw new Error("Erro ao atualizar emergência");
    }

    return result.rows[0];
  }

  async findByFilters(filters) {
    let query = `
            SELECT e.id, e.call_datetime, e.reason, e.attended, e.observations, 
                   e.referral, e.patient_id, e.agent_id,
                   p.full_name as patient_name, ha.full_name as agent_name
            FROM emergency e
            LEFT JOIN patient p ON e.patient_id = p.id
            LEFT JOIN health_agent ha ON e.agent_id = ha.id
            WHERE 1=1
        `;

    const values = [];
    let paramCounter = 1;

    if (filters.attended !== undefined) {
      query += ` AND e.attended = $${paramCounter}`;
      values.push(filters.attended);
      paramCounter++;
    }

    if (filters.patient_id) {
      query += ` AND e.patient_id = $${paramCounter}`;
      values.push(filters.patient_id);
      paramCounter++;
    }

    if (filters.agent_id) {
      query += ` AND e.agent_id = $${paramCounter}`;
      values.push(filters.agent_id);
      paramCounter++;
    }
    query += ` ORDER BY e.call_datetime DESC`;

    const result = await db.query(query, values);
    return result.rows;
  }
}

module.exports = EmergencyRepository;
