const EducationalMaterialModel = require('../../models/educationalMaterialModel'); 

describe('EducationalMaterialModel Schema', () => {
  const validData = {
    title: 'Como tratar úlceras venosas',
    content_type: 'Vídeo',
    video_uri: 'https://exemplo.com/video123',
    description: 'Um vídeo explicativo sobre úlceras venosas',
    target_wound_type: 'Úlcera venosa',
    target_severity_level: 2,
    creation_date: new Date()
  };

  it('deve validar dados corretos com sucesso', () => {
    const { error } = EducationalMaterialModel.schema.validate(validData);
    expect(error).toBeUndefined();
  });

  it('deve falhar se o título estiver ausente', () => {
    const invalidData = { ...validData };
    delete invalidData.title;

    const { error } = EducationalMaterialModel.schema.validate(invalidData);
    expect(error).toBeDefined();
    expect(error.details[0].path).toContain('title');
  });

  it('deve falhar se content_type for inválido', () => {
    const invalidData = { ...validData, content_type: 'Podcast' };

    const { error } = EducationalMaterialModel.schema.validate(invalidData);
    expect(error).toBeDefined();
    expect(error.details[0].path).toContain('content_type');
  });

  it('deve aceitar description vazia', () => {
    const data = { ...validData, description: '' };

    const { error } = EducationalMaterialModel.schema.validate(data);
    expect(error).toBeUndefined();
  });

  it('deve falhar se target_severity_level for inválido', () => {
    const invalidData = { ...validData, target_severity_level: 5 };

    const { error } = EducationalMaterialModel.schema.validate(invalidData);
    expect(error).toBeDefined();
    expect(error.details[0].path).toContain('target_severity_level');
  });

  it('deve falhar se creation_date não for uma data', () => {
    const invalidData = { ...validData, creation_date: 'não é uma data' };

    const { error } = EducationalMaterialModel.schema.validate(invalidData);
    expect(error).toBeDefined();
    expect(error.details[0].path).toContain('creation_date');
  });
});
