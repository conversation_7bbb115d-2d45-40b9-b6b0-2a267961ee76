class PatientQuestionnaireController {
  constructor(service) {
        this.service = service;
  }

  async index(req, res) {
    try {
        const data = await this.service.findAll();
        res.json(data); 
    } catch (err) {
        console.error('[PatientQuestionnaireController] - Erro na questionário do paciente:', err.message);
        res.status(400).send(err.message);
    }
  }

  async show(req, res) {
    try {
      const data = await this.service.findById(req.params.id);
      res.json(data);
    } catch (err) {
      console.error('[PatientQuestionnaireController] - Erro no questionário do paciente:', err.message);
      res.status(404).send(err.message);
    }
  }

  async create(req, res) {
    try {
      const data = await this.service.create(req.body);
      res.json(data);
    } catch (err) {
      console.error('[PatientQuestionnaireController] - Erro ao criar question<PERSON>rio do paciente:', err.message);
      res.status(400).json({ error: err.message });
    }
  }

  async update(req, res) {
    try {
      const data = await this.service.update(req.params.id, req.body);
      res.json(data);
    } catch (err) {
      console.error('[PatientQuestionnaireController] - Erro ao atualizar questionário do paciente:', err.message);
      res.status(400).json({ error: err.message });
    }
  }   

}

module.exports = PatientQuestionnaireController;
