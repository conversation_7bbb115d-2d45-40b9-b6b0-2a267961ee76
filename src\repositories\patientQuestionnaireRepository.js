const db = require('../config/db');

class PatientQuestionnaireRepository {
    async findAll() {
        const result = await db.query('SELECT id, completion_date, pain_level, has_discharge, odor, has_fever, surrounding_redness, swelling, other_symptoms, applied_recommended_dressing, treatment_difficulties, patient_id FROM patient_questionnaire');
        return result.rows;
    }
    
    async findById(id) {
        const query = 'SELECT id, completion_date, pain_level, has_discharge, odor, has_fever, surrounding_redness, swelling, other_symptoms, applied_recommended_dressing, treatment_difficulties, patient_id FROM patient_questionnaire WHERE id = $1';
        const result = await db.query(query, [id]);
        if (result.rows.length === 0) {
            throw new Error('Questionário não encontrado');
        }
        return result.rows;
    }

    async create(questionnaire) {
        if (!questionnaire.completion_date || !questionnaire.pain_level || !questionnaire.has_discharge || !questionnaire.odor || !questionnaire.has_fever || !questionnaire.surrounding_redness || !questionnaire.swelling || !questionnaire.other_symptoms || !questionnaire.applied_recommended_dressing || !questionnaire.treatment_difficulties || !questionnaire.patient_id) {
            throw new Error('Campos obrigatórios não preenchidos: completion_date, pain_level, has_discharge, odor, has_fever, surrounding_redness, swelling, other_symptoms, applied_recommended_dressing, treatment_difficulties e patient_id,  são obrigatórios');
        }

        const query = 'INSERT INTO patient_questionnaire (completion_date, pain_level, has_discharge, odor, has_fever, surrounding_redness, swelling, other_symptoms, applied_recommended_dressing, treatment_difficulties, patient_id ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)';
        const result = await db.query(query, [questionnaire.completion_date, questionnaire.pain_level, questionnaire.has_discharge, questionnaire.odor, questionnaire.has_fever, questionnaire.surrounding_redness, questionnaire.swelling, questionnaire.other_symptoms, questionnaire.applied_recommended_dressing, questionnaire.treatment_difficulties, questionnaire.patient_id]);

        if (result.rowCount === 0) {
            throw new Error('Questionário não encontrado');
        }
        return result.rows[0];  
    }   

    async update(id, questionnaire) {
        const query = 'UPDATE patient_questionnaire SET completion_date = $1, pain_level = $2, has_discharge = $3, odor = $4, has_fever = $5, surrounding_redness = $6, swelling = $7, other_symptoms = $8, applied_recommended_dressing = $9, treatment_difficulties = $10, patient_id = $11 WHERE id = $12';
        const result = await db.query(query, [questionnaire.completion_date, questionnaire.pain_level, questionnaire.has_discharge, questionnaire.odor, questionnaire.has_fever, questionnaire.surrounding_redness, questionnaire.swelling, questionnaire.other_symptoms, questionnaire.applied_recommended_dressing, questionnaire.treatment_difficulties, questionnaire.patient_id, id]);
        
        if (result.rowCount === 0) {
            throw new Error('Questionário não encontrado');
        }
        return result.rows[0];
    }
}

module.exports = PatientQuestionnaireRepository;