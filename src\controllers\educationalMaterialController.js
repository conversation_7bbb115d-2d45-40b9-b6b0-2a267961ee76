class EducationalMaterialController {
  constructor(service) {
        this.service = service;
  }

  async index(req, res) {
    try {
        const data = await this.service.findAll();
        res.json(data); 
    } catch (err) {
        console.error('[EducationalMaterialController] - Erro ao encontrar o material educacional:', err.message);
        res.status(400).send(err.message);
    }
  }

  async show(req, res) {
    try {
      const data = await this.service.findById(req.params.id);
      res.json(data);
    } catch (err) {
      console.error('[EducationalMaterialController] - Erro ao encontar material educacional:', err.message);
      res.status(404).send(err.message);
    }
  }

}

module.exports = EducationalMaterialController;
