const Joi = require("joi");

class EmergencyModel {
  static get schema() {
    return Joi.object({
      call_datetime: Joi.date().required(),
      reason: Joi.string().allow('').required(),
      attended: Joi.boolean().required(),
      observations: Joi.string().allow('').required(),
      referral: Joi.string().max(50).optional(),
      patient_id: Joi.number().integer().required(),
      agent_id: Joi.number().integer().optional().allow(null)
    });
  }
}

module.exports = EmergencyModel;