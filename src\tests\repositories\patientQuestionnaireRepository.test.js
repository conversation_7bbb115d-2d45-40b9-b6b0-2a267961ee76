const PatientQuestionnaireRepository = require('../../repositories/patientQuestionnaireRepository');
const db = require('../../config/db');

jest.mock('../../config/db');

describe('PatientQuestionnaireRepository', () => {
  let repository;

  const patientQuestionnaireMock = [
    {
      id: 1,
      completion_date: '2023-10-01T00:00:00.000Z',
      pain_level: 3,
      has_discharge: true,
      odor: 'leve',
      has_fever: false,
      surrounding_redness: false,
      swelling: true,
      other_symptoms: 'none',
      applied_recommended_dressing: true,
      treatment_difficulties: 'none',
      patient_id: 1
    },
    {
      id: 2,
      completion_date: '2023-10-02T00:00:00.000Z',
      pain_level: 2,
      has_discharge: false,
      odor: 'nenhum',
      has_fever: true,
      surrounding_redness: true,
      swelling: false,
      other_symptoms: 'none',
      applied_recommended_dressing: false,
      treatment_difficulties: 'none',
      patient_id: 2
    }
  ];

  beforeEach(() => {
    repository = new PatientQuestionnaireRepository();
    jest.clearAllMocks();
  });

  it('deve retornar todos os questionários (findAll)', async () => {
    db.query.mockResolvedValue({ rows: patientQuestionnaireMock });

    const result = await repository.findAll();

    expect(db.query).toHaveBeenCalledWith(
        'SELECT id, completion_date, pain_level, has_discharge, odor, has_fever, surrounding_redness, swelling, other_symptoms, applied_recommended_dressing, treatment_difficulties, patient_id FROM patient_questionnaire'
    );
    expect(result).toEqual(patientQuestionnaireMock);
  });

  it('deve retornar um questionário por ID (findById)', async () => {
    db.query.mockResolvedValue({ rows: [patientQuestionnaireMock[0]] });

    const result = await repository.findById(1);

    expect(db.query).toHaveBeenCalledWith(
        'SELECT id, completion_date, pain_level, has_discharge, odor, has_fever, surrounding_redness, swelling, other_symptoms, applied_recommended_dressing, treatment_difficulties, patient_id FROM patient_questionnaire WHERE id = $1',
        [1]
    );
    expect(result).toEqual([patientQuestionnaireMock[0]]);
  });

  it('deve lançar erro se o questionário não for encontrado (findById)', async () => {
    db.query.mockResolvedValue({ rows: [] });

    await expect(repository.findById(99)).rejects.toThrow('Questionário não encontrado');

    expect(db.query).toHaveBeenCalledWith(
        'SELECT id, completion_date, pain_level, has_discharge, odor, has_fever, surrounding_redness, swelling, other_symptoms, applied_recommended_dressing, treatment_difficulties, patient_id FROM patient_questionnaire WHERE id = $1',
        [99]
    );
  });

  it('deve criar um novo questionário (create)', async () => {
    const dataToCreate = { ...patientQuestionnaireMock[0] };
    delete dataToCreate.id;

    db.query.mockResolvedValue({ rows: [dataToCreate] });

    const result = await repository.create(dataToCreate);

    expect(db.query).toHaveBeenCalledWith(
        'INSERT INTO patient_questionnaire (completion_date, pain_level, has_discharge, odor, has_fever, surrounding_redness, swelling, other_symptoms, applied_recommended_dressing, treatment_difficulties, patient_id) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)',
        [dataToCreate.completion_date, dataToCreate.pain_level, dataToCreate.has_discharge, dataToCreate.odor, dataToCreate.has_fever, dataToCreate.surrounding_redness, dataToCreate.swelling, dataToCreate.other_symptoms, dataToCreate.applied_recommended_dressing, dataToCreate.treatment_difficulties, dataToCreate.patient_id]
    );
    expect(result).toEqual(dataToCreate);
  });

  it('deve lançar erro se o questionário não for encontrado (create)', async () => {
    db.query.mockRejectedValue(new Error('Erro ao criar questionário'));

    await expect(repository.create(patientQuestionnaireMock[0])).rejects.toThrow('Erro ao criar questionário');
  });

  it('deve atualizar um questionário existente (update)', async () => {
    const updatedData = { ...patientQuestionnaireMock[0], pain_level: 4 };

    db.query.mockResolvedValue({ rows: [updatedData] });

    const result = await repository.update(1, updatedData);

    expect(db.query).toHaveBeenCalledWith(
        'UPDATE patient_questionnaire SET completion_date = $1, pain_level = $2, has_discharge = $3, odor = $4, has_fever = $5, surrounding_redness = $6, swelling = $7, other_symptoms = $8, applied_recommended_dressing = $9, treatment_difficulties = $10, patient_id = $11 WHERE id = $12',
        [updatedData.completion_date, updatedData.pain_level, updatedData.has_discharge, updatedData.odor, updatedData.has_fever, updatedData.surrounding_redness, updatedData.swelling, updatedData.other_symptoms, updatedData.applied_recommended_dressing, updatedData.treatment_difficulties, updatedData.patient_id, 1]
    );
    expect(result).toEqual(updatedData);
  });

  it('deve lançar erro se o questionário não for encontrado (update)', async () => {
    db.query.mockResolvedValue({ rowCount: 0, rows: [] });

    await expect(repository.update(99, patientQuestionnaireMock[0])).rejects.toThrow('Questionário não encontrado');
  });
});