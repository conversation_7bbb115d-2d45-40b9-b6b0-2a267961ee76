const HealthUnitModel = require('../models/healthUnitModel');

class HealthUnitService {
  constructor(repository) {
        this.repository = repository;
  }

  async findAll() {
    return await this.repository.findAll();
  }

  async findById(id) {
    const data = await this.repository.findById(id);
    if (!data) throw new Error('UBS não encontrada'); 
    return data;
  }

}

module.exports = HealthUnitService;