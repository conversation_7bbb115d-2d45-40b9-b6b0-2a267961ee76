const WoundsModel = require('../models/woundsModel');

class WoundsService {
  constructor(repository) {
        this.repository = repository;
  }

  async findAll() {
    return await this.repository.findAll();
  }

  async findById(id) {
    return await this.repository.findById(id);
  }

  async create(wounds) {
    const { error } = WoundsModel.schema.validate(wounds);
    if (error) { 
      throw new Error(error.details[0].message);
    }
    return await this.repository.create(wounds);
  }

  async update(id, wounds) {
    const { error } = WoundsModel.schema.validate(wounds);
    if (error) {
      throw new Error(error.details[0].message);
    }
    return await this.repository.update(id, wounds);
  }

  async delete(id) {
    return await this.repository.delete(id);
  }

}

module.exports = WoundsService;