const HealthUnitRepository = require('../../repositories/healthUnitRepository');

describe('HealthUnitRepository', () => {
  let repository;

  const healthUnitMock = {
    id: 1,
    unit_name: 'UBS Central',
    address: 'Rua Principal, 123',
    phone: '(11) 91111-1111',
    coverage_area: 'Bairro Central',
    opening_hours: '08:00 - 18:00'
  };

  beforeEach(() => {
    repository = new HealthUnitRepository();
    jest.clearAllMocks();
  });

  it('deve retornar todas as unidades de saúde (findAll)', async () => {
    db.query.mockResolvedValue({ rows: [healthUnitMock] });

    const result = await repository.findAll();

    expect(db.query).toHaveBeenCalledTimes(1);
    expect(result).toEqual([healthUnitMock]);
  });

  it('deve retornar uma unidade de saúde por ID (findById)', async () => {
    db.query.mockResolvedValue({ rows: [healthUnitMock] });

    const result = await repository.findById(1);

    expect(db.query).toHaveBeenCalledWith(expect.stringContaining('WHERE id = $1'), [1]);
    expect(result).toEqual([healthUnitMock]);
  });

  it('deve lançar erro se a unidade de saúde não for encontrada (findById)', async () => {
    db.query.mockResolvedValue({ rows: [] });

    await expect(repository.findById(99)).rejects.toThrow('UBS não encontrada');

    expect(db.query).toHaveBeenCalledWith(expect.stringContaining('WHERE id = $1'), [99]);
  });
});