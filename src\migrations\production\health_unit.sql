CREATE TABLE if not exists "health_unit" (
  "id" SERIAL PRIMARY KEY,
  "unit_name" VARCHAR(255),
  "address" TEXT,
  "phone" VARCHAR(20),
  "coverage_area" TEXT,
  "opening_hours" VARCHAR(100)
);

INSERT INTO health_unit (unit_name, address, phone, coverage_area, opening_hours) 
VALUES 
('UBS Vila Mariana', 'R<PERSON>, 1235, Vila Mariana, São Paulo - SP', '(11) 3876-9021', 'Vila Mariana, Paraíso, Ana Rosa', '08:00 - 18:00 Segunda a Sexta'),
('UPA Jardim <PERSON>', 'Av. <PERSON><PERSON>, 789, <PERSON><PERSON><PERSON>, São Paulo - SP', '(11) 2345-7890', '<PERSON><PERSON><PERSON>, Bela Vista, Consolação', '24 horas'),
('Pronto Socorro Municipal Santo Amaro', 'Av. <PERSON>, 1456, <PERSON>, São Paulo - SP', '(11) 5521-0789', '<PERSON>, Campo Limpo, Capão Redondo', '24 horas'),
('UBS Butantã', '<PERSON><PERSON>, 234, <PERSON>ant<PERSON>, São Paulo - SP', '(11) 3714-2380', 'Butantã, Rio Pequeno, Jaguaré', '07:00 - 19:00 Segunda a S<PERSON><PERSON>'),
('Centro de Saúde Mo<PERSON><PERSON>', '<PERSON><PERSON> dos Tri<PERSON>, 597, <PERSON><PERSON><PERSON>, São Paulo - SP', '(11) 2695-3421', '<PERSON>, Brás, <PERSON><PERSON>m', '08:00 - 17:00 <PERSON> a <PERSON><PERSON>');
