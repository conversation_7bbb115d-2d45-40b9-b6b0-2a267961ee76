const express = require('express');
const router = express.Router();

const PatientQuestionnaireRepository = require('../repositories/patientQuestionnaireRepository');
const PatientQuestionnaireService = require('../services/patientQuestionnaireService');
const PatientQuestionnaireController = require('../controllers/patientQuestionnaireController');

const controller = new PatientQuestionnaireController(new PatientQuestionnaireService(new PatientQuestionnaireRepository()));
router.get('/', controller.index.bind(controller));
router.get('/:id', controller.show.bind(controller));
router.post('/', controller.create.bind(controller));
router.put('/:id', controller.update.bind(controller));

module.exports = router;
