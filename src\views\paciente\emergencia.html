<!DOCTYPE html>
<html lang="pt-br">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" href="../../../public/css/global.css" />
    <title>Emergência</title>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&display=swap"
      rel="stylesheet"
    />
    <script src="../../../public/js/auth-check.js"></script>
    <style>
      .emergency-container {
        text-align: center;
        padding: 20px;
      }

      .emergency-icon-large {
        font-size: 72px;
        margin-bottom: 20px;
      }

      .emergency-title {
        font-size: 24px;
        font-weight: 700;
        color: #e53e3e;
        margin-bottom: 16px;
      }

      .emergency-message {
        font-size: 18px;
        margin-bottom: 24px;
      }

      .reason-textarea {
        width: 100%;
        max-width: 500px;
        height: 120px;
        margin-bottom: 20px;
        padding: 12px;
        border: 1px solid #e2e8f0;
        border-radius: 8px;
        font-family: "Roboto", sans-serif;
        font-size: 16px;
      }

      .emergency-buttons {
        display: flex;
        justify-content: center;
        gap: 16px;
        flex-wrap: wrap;
      }

      .status-container {
        margin-top: 30px;
        padding: 20px;
        border-radius: 8px;
        background-color: #f8f9fa;
        display: none;
      }

      .status-title {
        font-weight: 600;
        font-size: 18px;
        margin-bottom: 10px;
      }

      .status-message {
        font-size: 16px;
        margin-bottom: 16px;
      }

      .agent-info {
        padding: 15px;
        background-color: #e6f7ff;
        border-radius: 8px;
        margin-top: 15px;
        text-align: left;
      }

      .agent-name {
        font-weight: 600;
        margin-bottom: 5px;
      }
    </style>
  </head>
  <body>
    <div class="desktop-frame">
      <div class="sidebar">
        <div class="sidebar-logo">
          <img
            src="../../../public/images/logo/cicatriza_semnada.png"
            alt="Logo"
            class="logo-small"
          />
          <h3 class="sidebar-title">Cicatriza+</h3>
        </div>
        <ul class="sidebar-menu">
          <a href="menu-principal.html">
            <li class="sidebar-item">
              <span class="sidebar-icon">🏠</span>Início
            </li>
          </a>
          <a href="tutorial-paciente.html">
            <li class="sidebar-item">
              <span class="sidebar-icon">📝</span>Tutorial
            </li>
          </a>
          <a href="enviar-foto-paciente.html">
            <li class="sidebar-item">
              <span class="sidebar-icon">📷</span>Enviar foto
            </li>
          </a>
          <a href="videos-paciente.html">
            <li class="sidebar-item">
              <span class="sidebar-icon">🎥</span>Vídeos
            </li>
          </a>
          <a href="perfil-paciente.html">
            <li class="sidebar-item">
              <span class="sidebar-icon">👤</span>Perfil
            </li>
          </a>
          <a href="notificacoes-paciente.html">
            <li class="sidebar-item">
              <span class="sidebar-icon">🔔</span>Notificações
            </li>
          </a>
          <a href="emergencia.html">
            <li class="sidebar-item">
              <span class="sidebar-icon">⚠️</span>Emergência
            </li>
          </a>
        </ul>
      </div>
      <div class="desktop-content">
        <div class="desktop-header">
          <div class="header-title">Confirmação de Emergência</div>
        </div>

        <!-- Seção inicial de confirmação -->
        <div id="emergency-confirmation" class="main-area emergency-container">
          <div class="emergency-icon-large">⚠️</div>
          <h2 class="emergency-title">Confirmação de emergência</h2>
          <p class="emergency-message">
            Você realmente está em uma situação de emergência?
          </p>
          <div class="emergency-buttons">
            <button class="button danger" id="confirm-emergency-btn">
              Sim, preciso de ajuda urgente
            </button>
            <a href="menu-principal.html"
              ><button class="button outline">
                Não, foi um toque acidental
              </button></a
            >
          </div>
        </div>

        <div
          id="emergency-details"
          class="main-area emergency-container"
          style="display: none"
        >
          <div class="emergency-icon-large">🚨</div>
          <h2 class="emergency-title">Descreva sua emergência</h2>
          <p class="emergency-message">
            Por favor, descreva brevemente o que está acontecendo para que
            possamos enviar a ajuda adequada.
          </p>
          <textarea
            id="emergency-reason"
            class="reason-textarea"
            placeholder="Ex: Estou com sangramento excessivo na ferida; Sinto muita dor; A ferida está com aparência estranha..."
          ></textarea>
          <div class="emergency-buttons">
            <button class="button danger" id="send-emergency-btn">
              Enviar pedido de emergência
            </button>
            <button class="button outline" id="back-btn">Voltar</button>
          </div>
        </div>

        <div
          id="emergency-status"
          class="main-area emergency-container"
          style="display: none"
        >
          <div class="emergency-icon-large">⏳</div>
          <h2 class="emergency-title">Pedido de Emergência Enviado</h2>
          <div class="status-container">
            <p class="status-title">Status:</p>
            <p id="status-message" class="status-message">
              Aguardando profissional de saúde...
            </p>

            <div id="agent-accepted" class="agent-info" style="display: none">
              <p class="agent-name">
                Profissional: <span id="agent-name">Nome do Profissional</span>
              </p>
              <p>
                Mantenha a calma, o profissional está analisando sua situação.
              </p>
              <p>Você será contatado em breve.</p>
            </div>
          </div>
          <div class="emergency-buttons" style="margin-top: 20px">
            <button class="button outline" id="cancel-emergency-btn">
              Cancelar emergência
            </button>
          </div>
        </div>

        <div class="footer-desktop">
          <div class="footer-left">
            Menu - início - Perfil - Login - Contate - nos
          </div>
          <div class="footer-right">Cicatriza+</div>
        </div>
      </div>
    </div>

    <div class="mobile-navbar">
      <div class="mobile-nav-container">
        <a href="menu-principal.html" class="mobile-nav-item active">
          <div class="mobile-nav-icon">🏠</div>
          <div class="mobile-nav-text">Início</div>
        </a>
        <a href="consultas-paciente.html" class="mobile-nav-item">
          <div class="mobile-nav-icon">📋</div>
          <div class="mobile-nav-text">Consultas</div>
        </a>
        <a href="saude-paciente.html" class="mobile-nav-item">
          <div class="mobile-nav-icon">📊</div>
          <div class="mobile-nav-text">Saúde</div>
        </a>
        <a href="perfil-paciente.html" class="mobile-nav-item">
          <div class="mobile-nav-icon">👤</div>
          <div class="mobile-nav-text">Perfil</div>
        </a>
      </div>
    </div>
    <div vw class="enabled">
      <div vw-access-button class="active"></div>
      <div vw-plugin-wrapper>
        <div class="vw-plugin-top-wrapper"></div>
      </div>
    </div>
    <script src="https://vlibras.gov.br/app/vlibras-plugin.js"></script>
    <script>
      new window.VLibras.Widget("https://vlibras.gov.br/app");

      const confirmationSection = document.getElementById(
        "emergency-confirmation"
      );
      const detailsSection = document.getElementById("emergency-details");
      const statusSection = document.getElementById("emergency-status");

      const confirmEmergencyBtn = document.getElementById(
        "confirm-emergency-btn"
      );
      const sendEmergencyBtn = document.getElementById("send-emergency-btn");
      const backBtn = document.getElementById("back-btn");
      const cancelEmergencyBtn = document.getElementById(
        "cancel-emergency-btn"
      );

      const emergencyReasonInput = document.getElementById("emergency-reason");
      const statusMessage = document.getElementById("status-message");
      const agentAcceptedSection = document.getElementById("agent-accepted");
      const agentNameElement = document.getElementById("agent-name");

      let currentEmergencyId = null;
      let emergencyCheckInterval = null;

      function getUserFromCookie() {
        const match = document.cookie.match("(^|;)\\s*user=([^;]+)");
        return match ? JSON.parse(decodeURIComponent(match[2])) : null;
      }

      window.addEventListener("DOMContentLoaded", function () {
        const savedEmergencyId = localStorage.getItem("activeEmergencyId");
        const savedEmergencyStatus = localStorage.getItem("emergencyStatus");

        if (savedEmergencyId) {
          currentEmergencyId = savedEmergencyId;

          if (savedEmergencyStatus === "waiting") {
            confirmationSection.style.display = "none";
            detailsSection.style.display = "none";
            statusSection.style.display = "block";

            document.querySelector(".status-container").style.display = "block";

            startEmergencyStatusCheck();
          }
        }
      });

      confirmEmergencyBtn.addEventListener("click", function () {
        confirmationSection.style.display = "none";
        detailsSection.style.display = "block";
      });

      backBtn.addEventListener("click", function () {
        detailsSection.style.display = "none";
        confirmationSection.style.display = "block";
      });

      sendEmergencyBtn.addEventListener("click", function () {
        const emergencyReason = emergencyReasonInput.value.trim();
        if (emergencyReason === "") {
          alert(
            "Por favor, descreva sua emergência para que possamos ajudar melhor."
          );
          return;
        }

        sendEmergencyRequest(emergencyReason);
      });

      cancelEmergencyBtn.addEventListener("click", function () {
        if (
          confirm("Tem certeza que deseja cancelar o pedido de emergência?")
        ) {
          cancelEmergency();
        }
      });

      async function sendEmergencyRequest(reason) {
        try {
          const user = getUserFromCookie();
          if (!user) {
            alert("Você precisa estar logado para usar esta funcionalidade.");
            window.location.href = "/login";
            return;
          }

          const emergencyData = {
            call_datetime: new Date().toISOString(),
            reason: reason,
            attended: false,
            observations: "",
            referral: null,
            patient_id: user.id,
            agent_id: null,
          };

          const response = await fetch("/api/emergencys", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify(emergencyData),
          });

          if (!response.ok) {
            throw new Error("Falha ao enviar solicitação de emergência");
          }

          const data = await response.json();
          currentEmergencyId = data.id;

          localStorage.setItem("activeEmergencyId", currentEmergencyId);
          localStorage.setItem("emergencyStatus", "waiting");

          detailsSection.style.display = "none";
          statusSection.style.display = "block";
          document.querySelector(".status-container").style.display = "block";

          startEmergencyStatusCheck();
        } catch (error) {
          console.error("Erro ao enviar emergência:", error);
          alert(
            "Ocorreu um erro ao enviar sua solicitação de emergência. Por favor, tente novamente ou ligue para o serviço de emergência."
          );
        }
      }

      function startEmergencyStatusCheck() {
        emergencyCheckInterval = setInterval(checkEmergencyStatus, 5000); // Verificar a cada 5 segundos
      }

      async function checkEmergencyStatus() {
        if (!currentEmergencyId) return;

        try {
          const response = await fetch(`/api/emergencys/${currentEmergencyId}`);
          if (!response.ok) {
            throw new Error("Falha ao buscar status da emergência");
          }

          const data = await response.json();

          if (data.attended && data.agent_id) {
            statusMessage.textContent = "Sua emergência foi aceita e atendida!";
            agentNameElement.textContent =
              data.agent_name || "Profissional de Saúde";
            agentAcceptedSection.style.display = "block";

            let infoHtml = "";

            if (data.observations) {
              infoHtml += `<p><strong>Observações:</strong> ${data.observations}</p>`;
            }

            if (data.referral) {
              infoHtml += `<p><strong>Encaminhamento:</strong> ${data.referral}</p>`;
            }

            if (infoHtml) {
              let infoDiv = document.getElementById("emergency-info");
              if (!infoDiv) {
                infoDiv = document.createElement("div");
                infoDiv.id = "emergency-info";
                infoDiv.style.marginTop = "15px";
                infoDiv.style.padding = "10px";
                infoDiv.style.backgroundColor = "#f0f9ff";
                infoDiv.style.borderRadius = "8px";
                agentAcceptedSection.appendChild(infoDiv);
              }

              infoDiv.innerHTML = infoHtml;
            }

            const cancelEmergencyBtn = document.getElementById(
              "cancel-emergency-btn"
            );
            if (cancelEmergencyBtn) {
              cancelEmergencyBtn.textContent = "Reconhecer e Fechar";
              cancelEmergencyBtn.classList.remove("outline");
              cancelEmergencyBtn.classList.add("primary");
            }

            localStorage.setItem("emergencyStatus", "attended");

            clearInterval(emergencyCheckInterval);
          }
        } catch (error) {
          console.error("Erro ao verificar status da emergência:", error);
        }
      }

      async function cancelEmergency() {
        if (!currentEmergencyId) {
          window.location.href = "menu-principal.html";
          return;
        }

        try {
          const status = localStorage.getItem("emergencyStatus");

          if (status === "attended") {
            clearInterval(emergencyCheckInterval);
            localStorage.removeItem("activeEmergencyId");
            localStorage.removeItem("emergencyStatus");
            window.location.href = "menu-principal.html";
            return;
          }

          const response = await fetch(
            `/api/emergencys/${currentEmergencyId}`,
            {
              method: "PUT",
              headers: {
                "Content-Type": "application/json",
              },
              body: JSON.stringify({
                attended: true,
                observations: "Cancelada pelo paciente",
                referral: "Nenhum",
              }),
            }
          );

          clearInterval(emergencyCheckInterval);
          localStorage.removeItem("activeEmergencyId");
          localStorage.removeItem("emergencyStatus");

          window.location.href = "menu-principal.html";
        } catch (error) {
          console.error("Erro ao cancelar emergência:", error);
          alert(
            "Não foi possível cancelar a emergência. Tentando voltar à tela inicial..."
          );
          window.location.href = "menu-principal.html";
        }
      }
    </script>
  </body>
</html>
