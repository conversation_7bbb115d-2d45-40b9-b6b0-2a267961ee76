const EmergencyRepository = require('../../repositories/emergencyRepository');
const db = require('../../config/db');

jest.mock('../../config/db');

describe('EmergencyRepository', () => {
  let repository;

  const emergencyMock = {
    id: 1,
    call_datetime: '2025-06-20T10:00:00Z',
    reason: 'Queda',
    attended: true,
    observations: 'Paciente atendido no local',
    referral: 'UBS Central',
    patient_id: 2,
    agent_id: 3,
    patient_name: '<PERSON>',
    agent_name: '<PERSON>'
  };

  beforeEach(() => {
    repository = new EmergencyRepository();
    jest.clearAllMocks();
  });

  it('deve retornar todas as emergências (findAll)', async () => {
    db.query.mockResolvedValue({ rows: [emergencyMock] });

    const result = await repository.findAll();

    expect(db.query).toHaveBeenCalledTimes(1);
    expect(result).toEqual([emergencyMock]);
  });

  it('deve retornar uma emergência por ID (findById)', async () => {
    db.query.mockResolvedValue({ rows: [emergencyMock] });

    const result = await repository.findById(1);

    expect(db.query).toHaveBeenCalledWith(expect.stringContaining('WHERE e.id = $1'), [1]);
    expect(result).toEqual(emergencyMock);
  });

  it('deve lançar erro se a emergência não for encontrada (findById)', async () => {
    db.query.mockResolvedValue({ rows: [] });

    await expect(repository.findById(99)).rejects.toThrow('Emergência não encontrada');
    expect(db.query).toHaveBeenCalledWith(expect.stringContaining('WHERE e.id = $1'), [99]);
  });

  it('deve criar uma nova emergência (create)', async () => {
    const dataToCreate = { ...emergencyMock };
    delete dataToCreate.id;
    delete dataToCreate.patient_name;
    delete dataToCreate.agent_name;

    db.query.mockResolvedValue({ rows: [emergencyMock] });

    const result = await repository.create(dataToCreate);

    expect(db.query).toHaveBeenCalledWith(expect.stringContaining('INSERT INTO emergency'), expect.any(Array));
    expect(result).toEqual(emergencyMock);
  });

  it('deve atualizar uma emergência existente (update)', async () => {
    const updatedFields = {
      attended: false,
      observations: 'Paciente transferido',
      referral: 'Hospital Municipal',
      agent_id: 4
    };

    const existingEmergency = { ...emergencyMock };
    const updatedResult = { ...existingEmergency, ...updatedFields };

    db.query
      .mockResolvedValueOnce({ rows: [existingEmergency] })
      .mockResolvedValueOnce({ rows: [updatedResult] });

    const result = await repository.update(1, updatedFields);

    expect(db.query).toHaveBeenCalledTimes(2);
    expect(result).toEqual(updatedResult);
  });

  it('deve lançar erro se nenhuma linha for atualizada (update)', async () => {
    const updatedFields = {
      attended: false,
      observations: 'Não houve atendimento',
      referral: 'Hospital X',
      agent_id: 5
    };

    const existingEmergency = { ...emergencyMock };

    db.query
      .mockResolvedValueOnce({ rows: [existingEmergency] })
      .mockResolvedValueOnce({ rows: [] });

    await expect(repository.update(1, updatedFields)).rejects.toThrow('Erro ao atualizar emergência');
  });

  it('deve retornar emergências filtradas (findByFilters)', async () => {
    const filters = {
      attended: true,
      patient_id: 2,
      agent_id: 3
    };

    db.query.mockResolvedValue({ rows: [emergencyMock] });

    const result = await repository.findByFilters(filters);

    expect(db.query).toHaveBeenCalledWith(expect.stringContaining('WHERE 1=1'), [true, 2, 3]);
    expect(result).toEqual([emergencyMock]);
  });

  it('deve funcionar com filtros parciais (findByFilters)', async () => {
    const filters = {
      attended: false
    };

    db.query.mockResolvedValue({ rows: [emergencyMock] });

    const result = await repository.findByFilters(filters);

    expect(db.query).toHaveBeenCalledWith(expect.stringContaining('WHERE 1=1'), [false]);
    expect(result).toEqual([emergencyMock]);
  });
});
