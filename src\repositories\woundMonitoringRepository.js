const db = require("../config/db");

class WoundMonitoringRepository {
  async findAll() {
    const result = await db.query(
      "SELECT id, evaluation_date, agent_observations, recommended_treatment, monitoring_frequency, next_evaluation_date, wound_id, agent_id FROM wound_monitoring"
    );
    return result.rows;
  }

  async findById(id) {
    const query =
      "SELECT id, evaluation_date, agent_observations, recommended_treatment, monitoring_frequency, next_evaluation_date, wound_id, agent_id FROM wound_monitoring WHERE id = $1";
    const result = await db.query(query, [id]);
    if (result.rows.length === 0) {
      throw new Error("Acompanhamento de ferida não encontrado");
    }
    return result.rows[0];
  }

  async create(monitoring) {
    if (
      !monitoring.evaluation_date ||
      !monitoring.agent_observations ||
      !monitoring.recommended_treatment ||
      !monitoring.monitoring_frequency ||
      !monitoring.next_evaluation_date ||
      !monitoring.wound_id ||
      !monitoring.agent_id
    ) {
      throw new Error(
        "Campos obrigatórios não preenchidos: evaluation_date, agent_observations, recommended_treatment, monitoring_frequency, next_evaluation_date, wound_id e agent_id são obrigatórios"
      );
    }

    const query =
      "INSERT INTO wound_monitoring (evaluation_date, agent_observations, recommended_treatment, monitoring_frequency, next_evaluation_date, wound_id, agent_id) VALUES ($1, $2, $3, $4, $5, $6, $7) RETURNING *";
    const result = await db.query(query, [
      monitoring.evaluation_date,
      monitoring.agent_observations,
      monitoring.recommended_treatment,
      monitoring.monitoring_frequency,
      monitoring.next_evaluation_date,
      monitoring.wound_id,
      monitoring.agent_id,
    ]);

    return result.rows[0];
  }

  async update(id, monitoring) {
    const query =
      "UPDATE wound_monitoring SET evaluation_date = $1, agent_observations = $2, recommended_treatment = $3, monitoring_frequency = $4, next_evaluation_date = $5, wound_id = $6, agent_id = $7 WHERE id = $8 RETURNING *";
    const result = await db.query(query, [
      monitoring.evaluation_date,
      monitoring.agent_observations,
      monitoring.recommended_treatment,
      monitoring.monitoring_frequency,
      monitoring.next_evaluation_date,
      monitoring.wound_id,
      monitoring.agent_id,
      id,
    ]);

    if (result.rowCount === 0) {
      throw new Error("Acompanhamento de ferida não encontrado");
    }
    return result.rows[0];
  }

  async delete(id) {
    const query = "DELETE FROM wound_monitoring WHERE id = $1 RETURNING *";
    const result = await db.query(query, [id]);

    if (result.rowCount === 0) {
      throw new Error("Acompanhamento de ferida não encontrado");
    }
    return result.rows[0];
  }
}

module.exports = WoundMonitoringRepository;
