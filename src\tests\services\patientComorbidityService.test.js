const PatientComorbidityService = require('../../services/patientComorbidityService');

describe('PatientComorbidityService', () => {
  const mockRepository = {
    findAll: jest.fn(),
    findById: jest.fn(),
    create: jest.fn(),
    update: jest.fn()
  };

  const service = new PatientComorbidityService(mockRepository);

  const patientComorbidityMock = [
  {
    comorbidity_type: 'Diabetes',
    diagnosis_date: '2015-06-15',
    under_treatment: true,
    observations: 'Paciente controla com medicação oral.',
    patient_id: 1
  },
  {
    comorbidity_type: 'Hipertensão',
    diagnosis_date: '2018-11-02',
    under_treatment: true,
    observations: 'Controle feito com dieta e remédios.',
    patient_id: 2
  }
];

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('deve retornar todas as comorbidades', async () => {
    mockRepository.findAll.mockResolvedValue(patientComorbidityMock);
    const result = await service.findAll();

    expect(mockRepository.findAll).toHaveBeenCalledTimes(1);
    expect(result).toEqual(patientComorbidityMock);
  });

  it('deve retornar uma comorbidade pelo ID', async () => {
    mockRepository.findById.mockResolvedValue(patientComorbidityMock[0]);
    const result = await service.findById(1);

    expect(mockRepository.findById).toHaveBeenCalledTimes(1);
    expect(result).toEqual(patientComorbidityMock[0]);
  });

  it('deve criar uma nova comorbidade', async () => {
    mockRepository.create.mockResolvedValue(patientComorbidityMock[0]);

    const resultado = await service.create(patientComorbidityMock[0]);

    expect(mockRepository.create).toHaveBeenCalledTimes(1);
    expect(resultado).toEqual(patientComorbidityMock[0]);
  });

  it('deve lançar erro ao tentar criar comorbidade com dados inválidos', async () => {
  const comorbidityInvalidates = {
    diagnosis_date: '2022-05-10',
    under_treatment: true,
    observations: 'Sem detalhes',
    patient_id: 1
  };

  await expect(service.create(comorbidityInvalidates))
  .rejects.toThrow();
});

  it('deve atualizar uma comorbidade existente', async () => {
    mockRepository.update.mockResolvedValue(patientComorbidityMock[0]);

    const resultado = await service.update(1, patientComorbidityMock[0]);

    expect(mockRepository.update).toHaveBeenCalledTimes(1);
    expect(resultado).toEqual(patientComorbidityMock[0]);
  });

  it('deve lançar erro ao tentar atualizar comorbidade com dados inválidos', async () => {
  const comorbidityInvalidates = {
    comorbidity_type: 123,
    diagnosis_date: '2022-05-10',
    under_treatment: true,
    observations: 'Observação.',
    patient_id: 1
  };

  await expect(service.update(1, comorbidityInvalidates))
  .rejects.toThrow();
  });

  it('deve lançar um erro se a comorbidade não for encontrada', async () => {
    mockRepository.findById.mockResolvedValue(null);

    await expect(service.findById(999)).rejects.toThrow('Comorbidade não encontrada');
    expect(mockRepository.findById).toHaveBeenCalledTimes(1);
  });
});