const express = require("express");
const path = require("path");
const db = require("./config/db");

require("dotenv").config({ path: path.resolve(__dirname, "../.env") });

const indexRoutes = require("./routes/index");

const app = express();

app.use(express.json());

app.use("/public", express.static(path.join(__dirname, "/public")));

app.use(express.static(path.join(__dirname, "views")));

app.use("/", indexRoutes);

app.get("/", async (req, res) => {
  res.sendFile(path.join(__dirname, "views", "index.html"));
});

app.get("/api-status", async (req, res) => {
  try {
    const result = await db.query("SELECT NOW()");
    res.send(`Hora atual no banco: ${result.rows[0].now}`);
  } catch (err) {
    res.status(500).send("Erro ao conectar com o banco.");
  }
});

app.use((req, res, next) => {
  res.status(404).send("Página não encontrada");
});

app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).send("Erro interno do servidor");
});

const PORT = process.env.PORT || 3000;

async function startServer() {
  try {
    await db.connect();
    console.log("✅ Conectado ao banco de dados!");

    app.listen(PORT, "0.0.0.0", () => {
      console.log(`🚀 Servidor rodando na porta ${PORT}`);
      console.log(`🌐 Acesse: http://localhost:${PORT}`);
    });
  } catch (err) {
    console.error("❌ Erro ao conectar ao banco de dados:", err);
    console.error("Verifique suas variáveis de ambiente no arquivo .env");
    process.exit(1);
  }
}

startServer();
