const SystemNotificationRepository = require('../../repositories/systemNotificationRepository');
const db = require('../../config/db');

jest.mock('../../config/db');

describe('SystemNotificationRepository', () => {
  let repository;

  const systemNotificationMock = [
    {
      id: 1,
      type: 'Alerta',
      content: 'Você tem uma nova atualização.',
      creation_date: '2025-06-24T12:00:00Z',
      sent_date: '2025-06-24T12:05:00Z',
      viewed: false,
      view_date: null,
      patient_id: 1
    },
    {
      id: 2,
      type: 'Lembrete',
      content: 'Lembrete: sua consulta está agendada para amanhã às 10h.',
      creation_date: '2025-06-25T09:00:00Z',
      sent_date: '2025-06-25T09:01:00Z',
      viewed: true,
      view_date: '2025-06-25T10:00:00Z',
      patient_id: 2
    }
  ];

  beforeEach(() => {
    repository = new SystemNotificationRepository();
    jest.clearAllMocks();
  });

  it('deve retornar todas as notificações do sistema (findAll)', async () => {
    db.query.mockResolvedValue({ rows: systemNotificationMock });

    const result = await repository.findAll();

    expect(db.query).toHaveBeenCalledWith(
      expect.stringContaining('SELECT id, type, content, creation_date, sent_date, viewed, view_date, patient_id FROM system_notification')
    );
    expect(result).toEqual(systemNotificationMock);
  });

  it('deve retornar uma notificação do sistema por ID (findById)', async () => {
    db.query.mockResolvedValue({ rows: [systemNotificationMock[0]] });

    const result = await repository.findById(1);

    expect(db.query).toHaveBeenCalledWith(
        expect.stringContaining('SELECT id, type, content, creation_date, sent_date, viewed, view_date, patient_id FROM system_notification WHERE id = $1'),
        [1]
    );
    expect(result).toEqual(systemNotificationMock[0]);
  });

  it('deve lançar erro se a notificação não for encontrada (findById)', async () => {
    db.query.mockResolvedValue({ rows: [] });

    await expect(repository.findById(99)).rejects.toThrow('Notificação não encontrada');

    expect(db.query).toHaveBeenCalledWith(
        expect.stringContaining('SELECT id, type, content, creation_date, sent_date, viewed, view_date, patient_id FROM system_notification WHERE id = $1'),
        [99]
    );
  });

  it('deve retornar notificações de um paciente por ID (findByPatientId)', async () => {
    db.query.mockResolvedValue({ rows: [systemNotificationMock[0]] });

    const result = await repository.findByPatientId(1);

    expect(db.query).toHaveBeenCalledWith(
        expect.stringContaining('SELECT id, type, content, creation_date, sent_date, viewed, view_date, patient_id FROM system_notification WHERE patient_id = $1'),
        [1]
    );
    expect(result).toEqual([systemNotificationMock[0]]);
  });

  it('deve criar uma nova notificação do sistema (create)', async () => {
    const dataToCreate = { ...systemNotificationMock[0] };
    delete dataToCreate.id;

    db.query.mockResolvedValue({ rows: [dataToCreate] });

    const result = await repository.create(dataToCreate);

    expect(db.query).toHaveBeenCalledWith(
        expect.stringContaining('INSERT INTO system_notification'),
        expect.any(Array)
    );
    expect(result).toEqual(dataToCreate);
  });

  it('deve lançar erro se a notificação não for encontrada (create)', async () => {
    db.query.mockRejectedValue(new Error('Erro ao criar notificação'));

    await expect(repository.create(systemNotificationMock[0])).rejects.toThrow('Erro ao criar notificação');
  });

  it('deve atualizar uma notificação existente (update)', async () => {
    const updatedData = { ...systemNotificationMock[0], content: 'Conteúdo atualizado' };

    db.query.mockResolvedValue({ rows: [updatedData] });

    const result = await repository.update(1, updatedData);

    expect(db.query).toHaveBeenCalledWith(
        expect.stringContaining('UPDATE system_notification'),
        expect.any(Array)
    );
    expect(result).toEqual(updatedData);
  });

  it('deve lançar erro se a notificação não for encontrada (update)', async () => {
    db.query.mockResolvedValue({ rows: [] });

    await expect(repository.update(99, systemNotificationMock[0])).rejects.toThrow('Erro ao atualizar notificação');
  });

  it('deve excluir uma notificação existente (delete)', async () => {
    db.query.mockResolvedValue({ rows: [systemNotificationMock[0]] });

    const result = await repository.delete(1);

    expect(db.query).toHaveBeenCalledWith(
        expect.stringContaining('DELETE FROM system_notification WHERE id = $1'),
        [1]
    );
    expect(result).toEqual({
      message: "Notificação excluída com sucesso",
      deletedNotification: systemNotificationMock[0]
    });
  });

  it('deve lançar erro se a notificação não for encontrada (delete)', async () => {
    db.query.mockResolvedValue({ rows: [] });

    await expect(repository.delete(99)).rejects.toThrow('Erro ao excluir notificação');
  });

  it('deve lançar erro se campos obrigatórios não forem preenchidos no create', async () => {
    const incompleteData = { type: 'info' };

    await expect(repository.create(incompleteData)).rejects.toThrow('Campos obrigatórios não preenchidos');
  });

  it('deve lançar erro se não conseguir criar notificação no banco', async () => {
    const dataToCreate = { ...systemNotificationMock[0] };
    delete dataToCreate.id;

    db.query.mockResolvedValue({ rows: [] });

    await expect(repository.create(dataToCreate)).rejects.toThrow('Erro ao criar notificação');
  });

  it('deve lançar erro se não conseguir atualizar notificação no banco', async () => {
    db.query.mockResolvedValue({ rows: [] });

    await expect(repository.update(1, systemNotificationMock[0])).rejects.toThrow('Erro ao atualizar notificação');
  });
});