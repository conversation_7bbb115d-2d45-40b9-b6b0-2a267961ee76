const PhotoRecordService = require("../../services/photoRecordService");

describe("PhotoRecordService", () => {
  const mockRepository = {
    findAll: jest.fn(),
    findById: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
  };

  const service = new PhotoRecordService(mockRepository);
  const photoRecordsMock = [
    {
      photo_date: "2025-05-27T08:30:00Z",
      file_path: "/path/to/photo1.jpg",
      wound_size_mm: 50.0,
      sent_by_patient: true,
      validated_by_agent: false,
      wound_id: 1,
      health_agent_id: 1,
    },
    {
      photo_date: "2025-05-28T09:00:00Z",
      file_path: "/path/to/photo2.jpg",
      wound_size_mm: 30.0,
      sent_by_patient: false,
      validated_by_agent: true,
      wound_id: 2,
      health_agent_id: 2,
    },
  ];

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('deve retornar a lista de registros fotográficos do repositório', async () => {
    mockRepository.findAll.mockResolvedValue(photoRecordsMock);

    const resultado = await service.findAll();

    expect(mockRepository.findAll).toHaveBeenCalledTimes(1);
    expect(resultado).toEqual(photoRecordsMock);
  });

  it('deve retornar um registro fotográfico pelo ID', async () => {
    mockRepository.findById.mockResolvedValue(photoRecordsMock[0]);

    const resultado = await service.findById(1);

    expect(mockRepository.findById).toHaveBeenCalledTimes(1);
    expect(resultado).toEqual(photoRecordsMock[0]);
  });

  it('deve criar um novo registro fotográfico', async () => {
    mockRepository.create.mockResolvedValue(photoRecordsMock[0]);

    const resultado = await service.create(photoRecordsMock[0]);

    expect(mockRepository.create).toHaveBeenCalledTimes(1);
    expect(resultado).toEqual(photoRecordsMock[0]);
  });

  it('deve atualizar um registro fotográfico existente', async () => {
    mockRepository.update.mockResolvedValue(photoRecordsMock[0]);

    const resultado = await service.update(1, photoRecordsMock[0]);

    expect(mockRepository.update).toHaveBeenCalledTimes(1);
    expect(resultado).toEqual(photoRecordsMock[0]);
  });

  it('deve deletar um registro fotográfico existente', async () => {
    mockRepository.delete.mockResolvedValue(photoRecordsMock[0]);

    const resultado = await service.delete(1);

    expect(mockRepository.delete).toHaveBeenCalledTimes(1);
    expect(resultado).toEqual(photoRecordsMock[0]);
  });
});
