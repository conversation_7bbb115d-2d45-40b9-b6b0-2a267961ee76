const express = require("express");
const router = express.Router();

const PatientRepository = require("../repositories/patientRepository");
const patientService = require("../services/patientService");
const PatientController = require("../controllers/patientController");

const controller = new PatientController(
  new patientService(new PatientRepository())
);
router.get("/", controller.index.bind(controller));
router.get("/:id", controller.show.bind(controller));
router.post("/", controller.create.bind(controller));
router.put("/:id", controller.update.bind(controller));
router.delete("/:id", controller.delete.bind(controller));


router.post("/register", controller.register.bind(controller));
router.post("/login", controller.login.bind(controller));

module.exports = router;
