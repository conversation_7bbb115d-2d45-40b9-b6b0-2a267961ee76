module.exports = {
  testMatch: ['**/__tests__/**/*.test.js', '**/tests/**/*.test.js'],
  testEnvironment: 'node',
  clearMocks: true,
  coverageDirectory: 'coverage',
  collectCoverageFrom: [
    'src/services/**/*.js',
    'src/repositories/**/*.js',
    'src/models/**/*.js',
    '!**/node_modules/**',
    '!**/config/**',
    '!src/config/**',
    '!**/*config*',
    '!**/*db*'
  ],
  coverageReporters: ['text', 'lcov', 'html'],
  verbose: true,
  setupFiles: ['dotenv/config']
};