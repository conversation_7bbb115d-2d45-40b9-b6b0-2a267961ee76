const Auth = {
  isLoggedIn() {
    return !!localStorage.getItem("authToken");
  },

  getToken() {
    return localStorage.getItem("authToken");
  },

  getUser() {
    const userJson = localStorage.getItem("user");
    return userJson ? JSON.parse(userJson) : null;
  },

  logout() {
    localStorage.removeItem("authToken");
    localStorage.removeItem("user");
    window.location.href = "/views/paciente/login-paciente.html";
  },

  requireAuth() {
    if (!this.isLoggedIn()) {
      window.location.href = "/views/paciente/login-paciente.html";
      return false;
    }
    return true;
  },

  
  authFetch(url, options = {}) {
    const token = this.getToken();

    if (!token) {
      return fetch(url, options);
    }

    
    const authOptions = {
      ...options,
      headers: {
        ...options.headers,
        Authorization: `Bearer ${token}`,
      },
    };

    return fetch(url, authOptions);
  },
};
