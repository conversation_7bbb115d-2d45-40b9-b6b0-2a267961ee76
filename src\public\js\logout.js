function logout() {
  document.cookie = "authToken=;path=/;max-age=0";
  document.cookie = "user=;path=/;max-age=0";
  document.cookie = "redirectAfterLogin=;path=/;max-age=0";

  
  const isHealthAgent = window.location.pathname.includes("profissional_saude");

  
  if (isHealthAgent) {
    window.location.href = "../profissional_saude/login-agente.html";
  } else {
    window.location.href = "../paciente/login-paciente.html";
  }
}


document.addEventListener("DOMContentLoaded", function () {
  const logoutButtons = document.querySelectorAll(".logout-button");

  logoutButtons.forEach((button) => {
    button.addEventListener("click", logout);
  });
});
