/**
 * Script para gerenciar o logout da aplicação
 */

function logout() {
  // Limpar dados de autenticação (cookies, não localStorage)
  document.cookie = "authToken=;path=/;max-age=0";
  document.cookie = "user=;path=/;max-age=0";
  document.cookie = "redirectAfterLogin=;path=/;max-age=0";

  // Identificar o tipo de página atual
  const isHealthAgent = window.location.pathname.includes("profissional_saude");

  // Redirecionar para a página de login correta
  if (isHealthAgent) {
    window.location.href = "../profissional_saude/login-agente.html";
  } else {
    window.location.href = "../paciente/login-paciente.html";
  }
}

// Adicionar o evento de logout ao botão quando o documento estiver carregado
document.addEventListener("DOMContentLoaded", function () {
  const logoutButtons = document.querySelectorAll(".logout-button");

  logoutButtons.forEach((button) => {
    button.addEventListener("click", logout);
  });
});
