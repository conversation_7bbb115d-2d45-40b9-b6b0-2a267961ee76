const SystemNotificationModel = require('../../models/systemNotificationModel'); 

describe('SystemNotificationModel Schema', () => {
  const validData = {
    type: 'Alerta',
    content: 'Você tem uma nova atualização.',
    creation_date: '2025-06-24T12:00:00Z',
    sent_date: '2025-06-24T12:05:00Z',
    viewed: false,
    view_date: null,
    patient_id: 1
  };

  it('deve validar dados corretos com sucesso', () => {
    const { error } = SystemNotificationModel.schema.validate(validData);
    expect(error).toBeUndefined();
  });

  it('deve falhar se type for maior que 50 caracteres', () => {
    const invalidData = { ...validData, type: 'A'.repeat(51) };

    const { error } = SystemNotificationModel.schema.validate(invalidData);
    expect(error).toBeDefined();
    expect(error.details[0].path).toContain('type');
  });

  it('deve falhar se content estiver ausente', () => {
    const invalidData = { ...validData };
    delete invalidData.content;

    const { error } = SystemNotificationModel.schema.validate(invalidData);
    expect(error).toBeDefined();
    expect(error.details[0].path).toContain('content');
  });

  it('deve falhar se creation_date não for ISO', () => {
    const invalidData = { ...validData, creation_date: '24/06/2025' };

    const { error } = SystemNotificationModel.schema.validate(invalidData);
    expect(error).toBeDefined();
    expect(error.details[0].path).toContain('creation_date');
  });

  it('deve falhar se view_date for string inválida', () => {
    const invalidData = { ...validData, view_date: 'ontem' };

    const { error } = SystemNotificationModel.schema.validate(invalidData);
    expect(error).toBeDefined();
    expect(error.details[0].path).toContain('view_date');
  });

  it('deve aceitar view_date como null', () => {
    const data = { ...validData, view_date: null };

    const { error } = SystemNotificationModel.schema.validate(data);
    expect(error).toBeUndefined();
  });

  it('deve falhar se viewed não for booleano', () => {
    const invalidData = { ...validData, viewed: 'sim' };

    const { error } = SystemNotificationModel.schema.validate(invalidData);
    expect(error).toBeDefined();
    expect(error.details[0].path).toContain('viewed');
  });

  it('deve falhar se patient_id não for número positivo', () => {
    const invalidData = { ...validData, patient_id: -1 };

    const { error } = SystemNotificationModel.schema.validate(invalidData);
    expect(error).toBeDefined();
    expect(error.details[0].path).toContain('patient_id');
  });
});
