const systemNotificationService = require("../../services/systemNotificationService");

describe("SystemNotificationService", () => {
  const mockRepository = {
    findAll: jest.fn(),
    findById: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
  };

  const service = new systemNotificationService(mockRepository);

  const systemNotificationMock = [
    {
      type: "alerta",
      content: "Alerta de sistema",
      creation_date: "2025-05-27T08:30:00Z",
      sent_date: "2025-05-27T09:00:00Z",
      viewed: false,
      view_date: null,
      patient_id: 1,
    },
    {
      type: "informativo",
      content: "Notificação informativa",
      creation_date: "2025-05-26T09:00:00Z",
      sent_date: "2025-05-26T10:00:00Z",
      viewed: true,
      view_date: "2025-05-26T11:00:00Z",
      patient_id: 2,
    },
  ];

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("deve retornar a lista de notificações do repositório", async () => {
    mockRepository.findAll.mockResolvedValue(systemNotificationMock);

    const resultado = await service.findAll();

    expect(mockRepository.findAll).toHaveBeenCalledTimes(1);
    expect(resultado).toEqual(systemNotificationMock);
  });

  it("deve retornar uma notificação pelo ID", async () => {
    mockRepository.findById.mockResolvedValue(systemNotificationMock[0]);

    const resultado = await service.findById(1);

    expect(mockRepository.findById).toHaveBeenCalledTimes(1);
    expect(resultado).toEqual(systemNotificationMock[0]);
  });

  it("deve criar uma nova notificação", async () => {
    mockRepository.create.mockResolvedValue(systemNotificationMock[0]);

    const resultado = await service.create(systemNotificationMock[0]);

    expect(mockRepository.create).toHaveBeenCalledTimes(1);
    expect(resultado).toEqual(systemNotificationMock[0]);
  });
  
  it("deve atualizar uma notificação existente", async () => {
    mockRepository.update.mockResolvedValue(systemNotificationMock[0]);

    const resultado = await service.update(1, systemNotificationMock[0]);

    expect(mockRepository.update).toHaveBeenCalledTimes(1);
    expect(resultado).toEqual(systemNotificationMock[0]);
  });

  it("deve deletar uma notificação existente", async () => {
    mockRepository.delete.mockResolvedValue(systemNotificationMock[0]);

    const resultado = await service.delete(1);

    expect(mockRepository.delete).toHaveBeenCalledTimes(1);
    expect(resultado).toEqual(systemNotificationMock[0]);
  });
});
