const HealthAgentRepository = require("../../repositories/healthAgentRepository");
const db = require("../../config/db");

jest.mock("../../config/db");

describe("HealthAgentRepository", () => {
  let repository;

  const healthAgentMock = {
    id: 1,
    full_name: "<PERSON>",
    agent_type: "Agente Comunitário de Saúde",
    agent_registration: "ACS-001",
    phone: "(11) 91111-1111",
    email: "<EMAIL>",
    health_unit_id: 1,
    password: "senha123"
  };

  beforeEach(() => {
    repository = new HealthAgentRepository();
    jest.clearAllMocks();
  });

  it("deve retornar todos os agentes de saúde (findAll)", async () => {
    db.query.mockResolvedValue({ rows: [healthAgentMock] });

    const result = await repository.findAll();

    expect(db.query).toHaveBeenCalledTimes(1);
    expect(result).toEqual([healthAgentMock]);
  });

  it("deve retornar um agente de saúde por ID (findById)", async () => {
    db.query.mockResolvedValue({ rows: [healthAgentMock] });

    const result = await repository.findById(1);

    expect(db.query).toHaveBeenCalledWith(expect.stringContaining("WHERE id = $1"), [1]);
    expect(result).toEqual(healthAgentMock);
  });

  it("deve lançar erro se o agente não for encontrado (findById)", async () => {
    db.query.mockResolvedValue({ rows: [] });

    await expect(repository.findById(99)).rejects.toThrow("Agente de saúde não encontrado");

    expect(db.query).toHaveBeenCalledWith(expect.stringContaining("WHERE id = $1"), [99]);
  });

  it("deve retornar um agente de saúde por CPF (findByCpf)", async () => {
    db.query.mockResolvedValue({ rows: [healthAgentMock] });

    const result = await repository.findByCpf("123.456.789-00");

    expect(db.query).toHaveBeenCalledWith(
      expect.stringContaining("WHERE cpf = $1 OR cpf = $2 OR cpf = $3"),
      ["123.456.789-00", "12345678900", "123.456.789-00"]
    );
    expect(result).toEqual(healthAgentMock);
  });

  it("deve retornar um agente de saúde por email (findByEmail)", async () => {
    db.query.mockResolvedValue({ rows: [healthAgentMock] });

    const result = await repository.findByEmail("<EMAIL>");

    expect(db.query).toHaveBeenCalledWith(expect.stringContaining("WHERE email = $1"), ["<EMAIL>"]);
    expect(result).toEqual(healthAgentMock);
  });

  it("deve lançar erro quando falha na busca por CPF", async () => {
    const errorMessage = "Erro de conexão com banco de dados";
    db.query.mockRejectedValue(new Error(errorMessage));

    const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

    await expect(repository.findByCpf("123.456.789-00")).rejects.toThrow(errorMessage);

    expect(consoleSpy).toHaveBeenCalledWith(
      "[HealthAgentRepository] Erro ao buscar por CPF:",
      errorMessage
    );

    consoleSpy.mockRestore();
  });

  it("deve lançar erro quando falha na busca por email", async () => {
    const errorMessage = "Erro de conexão com banco de dados";
    db.query.mockRejectedValue(new Error(errorMessage));

    const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

    await expect(repository.findByEmail("<EMAIL>")).rejects.toThrow(errorMessage);

    expect(consoleSpy).toHaveBeenCalledWith(
      "[HealthAgentRepository] Erro ao buscar por email:",
      errorMessage
    );

    consoleSpy.mockRestore();
  });
});