const PhotoRecordModel = require('../../models/photoRecordModel'); 

describe('PhotoRecordModel Schema', () => {
  const validData = {
    photo_date: '2025-06-24',
    file_path: 'https://exemplo.com/foto123.jpg',
    wound_size_mm: 25.4,
    submitted_by_patient: '<PERSON><PERSON>',
    validated_by_agent: 'Dr. <PERSON>',
    wound_id: 10,
    validating_agent_id: 3
  };

  it('deve validar dados corretos com sucesso', () => {
    const { error } = PhotoRecordModel.schema.validate(validData);
    expect(error).toBeUndefined();
  });

  it('deve falhar se photo_date estiver ausente', () => {
    const invalidData = { ...validData };
    delete invalidData.photo_date;

    const { error } = PhotoRecordModel.schema.validate(invalidData);
    expect(error).toBeDefined();
    expect(error.details[0].path).toContain('photo_date');
  });

  it('deve falhar se file_path não for uma URL válida', () => {
    const invalidData = { ...validData, file_path: 'arquivo123.jpg' };

    const { error } = PhotoRecordModel.schema.validate(invalidData);
    expect(error).toBeDefined();
    expect(error.details[0].path).toContain('file_path');
  });

  it('deve falhar se wound_size_mm não for número', () => {
    const invalidData = { ...validData, wound_size_mm: 'grande' };

    const { error } = PhotoRecordModel.schema.validate(invalidData);
    expect(error).toBeDefined();
    expect(error.details[0].path).toContain('wound_size_mm');
  });

  it('deve falhar se validating_agent_id não for número', () => {
    const invalidData = { ...validData, validating_agent_id: 'xyz' };

    const { error } = PhotoRecordModel.schema.validate(invalidData);
    expect(error).toBeDefined();
    expect(error.details[0].path).toContain('validating_agent_id');
  });

  it('deve falhar se submitted_by_patient estiver vazio', () => {
    const invalidData = { ...validData, submitted_by_patient: '' };

    const { error } = PhotoRecordModel.schema.validate(invalidData);
    expect(error).toBeDefined();
    expect(error.details[0].path).toContain('submitted_by_patient');
  });

  it('deve falhar se wound_id for ausente', () => {
    const invalidData = { ...validData };
    delete invalidData.wound_id;

    const { error } = PhotoRecordModel.schema.validate(invalidData);
    expect(error).toBeDefined();
    expect(error.details[0].path).toContain('wound_id');
  });
});
