const express = require("express");
const multer = require("multer");
const path = require("path");
const fs = require("fs");
const PhotoRecordController = require("../controllers/photoRecordController");
const PhotoRecordService = require("../services/photoRecordService");
const PhotoRecordRepository = require("../repositories/photoRecordRepository");

// Certifique-se que o diretório existe
const uploadDir = path.join(__dirname, "../public/assets/photos");
if (!fs.existsSync(uploadDir)) {
  fs.mkdirSync(uploadDir, { recursive: true });
  console.log("Diretório de upload criado:", uploadDir);
}

const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    console.log("Multer destination:", uploadDir);
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = `${Date.now()}-${Math.round(Math.random() * 1e9)}`;
    const ext = path.extname(file.originalname) || ".jpg";
    cb(null, `photo-${uniqueSuffix}${ext}`);
  },
});

const upload = multer({
  storage,
  // Log para depuração
  fileFilter: (req, file, cb) => {
    console.log("Recebido arquivo:", file.originalname, file.mimetype);
    cb(null, true);
  },
});

// Instanciar controller e rotas
const repo = new PhotoRecordRepository();
const service = new PhotoRecordService(repo);
const controller = new PhotoRecordController(service);

const router = express.Router();
router.get("/", controller.index.bind(controller));
router.get("/:id", controller.show.bind(controller));
router.post("/", upload.single("photo"), controller.create.bind(controller));
router.put("/:id", controller.update.bind(controller));
router.delete("/:id", controller.delete.bind(controller));

module.exports = router;
