<!DOCTYPE html>
<html lang="pt-BR">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" href="../../../public/css/global.css" />
    <title>Cadastrar-se</title>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&display=swap"
      rel="stylesheet"
    />
    <style>
      .error-message {
        color: #e74c3c;
        margin-top: 10px;
        display: none;
        text-align: center;
      }
      .form-steps {
        display: flex;
        justify-content: center;
        margin-bottom: 20px;
      }
      .step {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        background-color: #ddd;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 10px;
      }
      .step.active {
        background-color: #3498db;
        color: white;
      }
      .step-content {
        display: none;
      }
      .step-content.active {
        display: block;
      }
    </style>
  </head>
  <body>
    <div class="desktop-frame">
      <div class="desktop-content">
        <div class="desktop-header">
          <div class="header-title">Cadastro de Paciente</div>
        </div>
        <div class="main-area">
          <div class="form-steps">
            <div class="step active" id="step1-indicator">1</div>
            <div class="step" id="step2-indicator">2</div>
          </div>

          <form id="registration-form">
            <div class="step-content active" id="step1-content">
              <div class="card cadastro-form">
                <div class="input-group">
                  <label class="input-label">Nome completo</label>
                  <input
                    type="text"
                    id="nome_completo"
                    name="nome_completo"
                    class="input-field"
                    placeholder="Digite seu nome completo"
                    required
                  />
                </div>

                <div class="input-group">
                  <label class="input-label">CPF</label>
                  <input
                    type="text"
                    id="cpf"
                    name="cpf"
                    class="input-field"
                    placeholder="000.000.000-00"
                    required
                  />
                </div>

                <div class="input-group">
                  <label class="input-label">Email</label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    class="input-field"
                    placeholder="<EMAIL>"
                    required
                  />
                </div>

                <div class="input-group">
                  <label class="input-label">Data de Nascimento</label>
                  <input
                    type="date"
                    id="data_nascimento"
                    name="data_nascimento"
                    class="input-field"
                    required
                  />
                </div>

                <div class="input-group">
                  <label class="input-label">Sexo</label>
                  <select id="sexo" name="sexo" class="input-field" required>
                    <option value="" selected disabled>Selecione</option>
                    <option value="Masculino">Masculino</option>
                    <option value="Feminino">Feminino</option>
                    <option value="Outro">Outro</option>
                  </select>
                </div>
              </div>

              <button type="button" class="button" onclick="nextStep()">
                Próximo
              </button>
            </div>

            <div class="step-content" id="step2-content">
              <div class="card cadastro-form">
                <div class="input-group">
                  <label class="input-label">CEP</label>
                  <input
                    type="text"
                    id="cep"
                    name="cep"
                    class="input-field"
                    placeholder="00000-000"
                    required
                  />
                </div>

                <div class="input-group">
                  <label class="input-label">Endereço</label>
                  <input
                    type="text"
                    id="endereco"
                    name="endereco"
                    class="input-field"
                    placeholder="Rua, número, bairro"
                    required
                  />
                </div>

                <div class="input-group">
                  <label class="input-label">Telefone</label>
                  <input
                    type="tel"
                    id="telefone"
                    name="telefone"
                    class="input-field"
                    placeholder="(00) 00000-0000"
                    required
                  />
                </div>

                <div class="input-group">
                  <label class="input-label">Senha</label>
                  <input
                    type="password"
                    id="senha"
                    name="senha"
                    class="input-field"
                    placeholder="Digite sua senha"
                    required
                  />
                </div>

                <div class="input-group">
                  <label class="input-label">Confirmar Senha</label>
                  <input
                    type="password"
                    id="confirmar_senha"
                    class="input-field"
                    placeholder="Digite sua senha novamente"
                    required
                  />
                </div>
              </div>

              <button type="button" class="button" onclick="previousStep()">
                Voltar
              </button>
              <button type="submit" class="button">Cadastrar</button>
            </div>

            <div id="error-message" class="error-message"></div>
          </form>
        </div>
        <div class="footer-desktop">
          <div class="footer-left">
            Menu - início - Perfil - Login - Contate - nos
          </div>
          <div class="footer-right">Cicatriza+</div>
        </div>
      </div>
    </div>

    <div vw class="enabled">
      <div vw-access-button class="active"></div>
      <div vw-plugin-wrapper>
        <div class="vw-plugin-top-wrapper"></div>
      </div>
    </div>
    <script src="https://vlibras.gov.br/app/vlibras-plugin.js"></script>
    <script>
      new window.VLibras.Widget("https://vlibras.gov.br/app");
    </script>

    <script>

      function nextStep() {
        document.getElementById("step1-content").classList.remove("active");
        document.getElementById("step2-content").classList.add("active");
        document.getElementById("step1-indicator").classList.remove("active");
        document.getElementById("step2-indicator").classList.add("active");
      }

      function previousStep() {
        document.getElementById("step2-content").classList.remove("active");
        document.getElementById("step1-content").classList.add("active");
        document.getElementById("step2-indicator").classList.remove("active");
        document.getElementById("step1-indicator").classList.add("active");
      }

      document
        .getElementById("registration-form")
        .addEventListener("submit", async function (e) {
          e.preventDefault();

          const errorMessage = document.getElementById("error-message");
          errorMessage.style.display = "none";

          const senha = document.getElementById("senha").value;
          const confirmarSenha =
            document.getElementById("confirmar_senha").value;

          if (senha !== confirmarSenha) {
            errorMessage.textContent = "As senhas não coincidem";
            errorMessage.style.display = "block";
            return;
          }

          const submitButton = document.querySelector('button[type="submit"]');
          const originalButtonText = submitButton.textContent;
          submitButton.textContent = "Processando...";
          submitButton.disabled = true;
    
          const userData = {
            nome_completo: document.getElementById("nome_completo").value,
            cpf: document.getElementById("cpf").value,
            email: document.getElementById("email").value,
            data_nascimento: document.getElementById("data_nascimento").value,
            sexo: document.getElementById("sexo").value,
            endereco:
              document.getElementById("endereco").value +
              " - CEP: " +
              document.getElementById("cep").value,
            telefone: document.getElementById("telefone").value,
            senha: senha,
            tipo_usuario: "paciente",
          };

          try {
        
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 15000);

            const response = await fetch("/api/patients/register", {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
              },
              body: JSON.stringify(userData),
              signal: controller.signal,
            });

           
            clearTimeout(timeoutId);

            let data;
            try {
              data = await response.json();
            } catch (parseError) {
              throw new Error("Erro ao processar resposta do servidor");
            }

            if (!response.ok) {
              throw new Error(data.error || "Erro ao registrar usuário");
            }

            if (data.success) {
           
              alert(
                "Cadastro realizado com sucesso! Faça login para continuar."
              );
              window.location.href = "login-paciente.html";
            } else {
              throw new Error(data.error || "Erro desconhecido no registro");
            }
          } catch (error) {
            console.error("Registration error:", error);

            let message = error.message;
            if (error.name === "AbortError") {
              message =
                "O servidor demorou muito para responder. Por favor, tente novamente.";
            } else if (error.message === "Failed to fetch") {
              message =
                "Não foi possível conectar ao servidor. Verifique sua conexão.";
            }

            errorMessage.textContent = message;
            errorMessage.style.display = "block";
          } finally {
           
            submitButton.textContent = originalButtonText;
            submitButton.disabled = false;
          }
        });
    </script>
  </body>
</html>
