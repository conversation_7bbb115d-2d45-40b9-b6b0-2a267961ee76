const Joi = require("joi");

class WoundsModel {
  static get schema() {
    return Joi.object({
      wound_type: Joi.string().valid('Úlcera venosa', 'Lesão por pressão', 'Úlcera de pé diabético').required(),
      anatomical_location: Joi.string().max(255).required(),
      first_occurrence_date: Joi.date().required(),
      severity_level: Joi.number().min(1).max(5).required(),
      status: Joi.string().valid('ativa', 'em cicatrização', 'cicatrizada').required(),
      registration_date: Joi.date().required(),
      patient_id: Joi.number().required()
    });
  }
}

module.exports = WoundsModel;