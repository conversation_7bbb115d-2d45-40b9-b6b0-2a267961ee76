const PatientQuestionnaireModel = require('../../models/patientQuestionnaireModel'); 

describe('PatientQuestionnaireModel Schema', () => {
  const validData = {
    completion_date: new Date(),
    pain_level: 3,
    has_discharge: true,
    odor: 'forte', 
    has_fever: false,
    surrounding_redness: true,
    swelling: true,
    other_symptoms: '',
    applied_recommended_dressing: false,
    treatment_difficulties: '',
    patient_id: 5
  };

  it('deve validar dados corretos com sucesso', () => {
    const { error } = PatientQuestionnaireModel.schema.validate(validData);
    expect(error).toBeUndefined();
  });

  it('deve falhar se pain_level for fora do intervalo', () => {
    const invalidData = { ...validData, pain_level: 6 };

    const { error } = PatientQuestionnaireModel.schema.validate(invalidData);
    expect(error).toBeDefined();
    expect(error.details[0].path).toContain('pain_level');
  });

  it('deve falhar se odor não for um dos valores permitidos', () => {
    const invalidData = { ...validData, odor: 'insuportável' }; 

    const { error } = PatientQuestionnaireModel.schema.validate(invalidData);
    expect(error).toBeDefined();
    expect(error.details[0].path).toContain('odor');
  });

  it('deve aceitar campos de texto opcionais vazios', () => {
    const data = {
      ...validData,
      other_symptoms: '',
      treatment_difficulties: ''
    };

    const { error } = PatientQuestionnaireModel.schema.validate(data);
    expect(error).toBeUndefined();
  });

  it('deve falhar se completion_date for inválida', () => {
    const invalidData = { ...validData, completion_date: 'ontem' };

    const { error } = PatientQuestionnaireModel.schema.validate(invalidData);
    expect(error).toBeDefined();
    expect(error.details[0].path).toContain('completion_date');
  });

  it('deve falhar se applied_recommended_dressing for string', () => {
    const invalidData = { ...validData, applied_recommended_dressing: 'sim' };

    const { error } = PatientQuestionnaireModel.schema.validate(invalidData);
    expect(error).toBeDefined();
    expect(error.details[0].path).toContain('applied_recommended_dressing');
  });

  it('deve falhar se patient_id não for inteiro', () => {
    const invalidData = { ...validData, patient_id: 'abc' };

    const { error } = PatientQuestionnaireModel.schema.validate(invalidData);
    expect(error).toBeDefined();
    expect(error.details[0].path).toContain('patient_id');
  });
});
