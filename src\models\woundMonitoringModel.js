const Joi = require("joi");

class WoundMonitoringModel {
  static get schema() {
    return Joi.object({
      evaluation_date: Joi.string().max(255).required(),
      agent_observations: Joi.string().max(255).required(),
      recommended_treatment: Joi.string().max(255).required(),
      monitoring_frequency: Joi.number().min(1).max(5).required(),
      next_evaluation_date: Joi.date().required(),
      wound_id: Joi.number().required(),
      health_agent_id: Joi.number().required()
    });
  }
}

module.exports = WoundMonitoringModel;