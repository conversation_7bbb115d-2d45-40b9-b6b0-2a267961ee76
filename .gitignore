# Node.js
node_modules
package-lock.json
.env

# Arquivos de lock 
# package-lock.json
# yarn.lock
# pnpm-lock.yaml

# Sistema Operacional - arquivos ocultos
**/.DS_Store
**/.AppleDouble
**/.LSOverride
$RECYCLE.BIN/
*.lnk
Thumbs.db
ehthumbs.db
Icon?
Desktop.ini
*~
*.swp
*.swo

# Banco de Dados - apenas dumps e backups
*.backup
*.bak
*.dump

# Arquivos de banco de dados locais
*.db
*.db3
*.sqlite
*.sqlite3

# Variáveis de Ambiente
**/.env
**/.env.*
!.env.example

# Logs
logs/
*.log

# Caches e builds
.cache/
.next/
out/
dist/
build/
tmp/
temp/
.npm/
.pnpm-store/
.parcel-cache/
.rollup.cache/
.webpack/

# Arquivos temporários específicos para processamento de imagens
*.tmp
*.psd.tmp

# Testes
coverage/
*.lcov

# IDEs e Editores
.vscode/
.idea/
*.sublime-workspace
*.sublime-project

# Supabase
.supabase/

# Documentação gerada automaticamente
docs/build/
api-docs/

# Arquivos relacionados ao paciente e sistema de notificação
src/controllers/patientController.js
src/controllers/systemNotificationController.js
src/repositories/patientRepository.js
src/repositories/systemNotificationRepository.js
src/models/systemNotificationModel.js
src/migrations/production/paciente.sql
src/migrations/production/notificacao_sistema.sql
src/migrations/development/notificacao_sistema_seed.sql
src/migrations/production/comorbidade_paciente.sql
src/migrations/production/questionario_paciente.sql
src/migrations/production/emergencia.sql
