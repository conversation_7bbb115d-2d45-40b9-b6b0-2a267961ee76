class HealthAgentController {
  constructor(service) {
    this.service = service;
  }

  async index(req, res) {
    try {
      const data = await this.service.findAll();
      res.json(data);
    } catch (err) {
      console.error(
        "[HealthAgentController] - Erro ao encontrar agente de saúde:",
        err.message
      );
      res.status(400).json({ error: err.message });
    }
  }

  async show(req, res) {
    try {
      const data = await this.service.findById(req.params.id);
      res.json(data);
    } catch (err) {
      console.error(
        "[HealthAgentController] - Erro ao encontrar agente de saúde:",
        err.message
      );
      res.status(400).json({ error: err.message });
    }
  }

  async login(req, res) {
    try {
      console.log("[HealthAgentController] Login attempt", req.body);
      const { identifier, password } = req.body;

      if (!identifier || !password) {
        return res
          .status(400)
          .json({ error: "Email/CPF e senha são obrigatórios" });
      }

      const agent = await this.service.authenticate(identifier, password);

      if (!agent) {
        return res.status(401).json({ error: "Credenciais inválidas" });
      }

      console.log(
        "[HealthAgentController] Login successful for agent:",
        agent.full_name
      );

      res.json({
        message: "Login bem sucedido",
        token: "jwt-token-would-be-here", 
        user: {
          id: agent.id,
          nome: agent.full_name,
          email: agent.email,
          tipo_usuario: "agente",
        },
      });
    } catch (err) {
      console.error(
        "[HealthAgentController] - Erro ao fazer login:",
        err.message
      );
      res.status(500).json({ error: "Erro interno do servidor" });
    }
  }
}

module.exports = HealthAgentController;
