const Joi = require("joi");

class HealthAgentModel {
  static get schema() {
    return Joi.object({
      full_name: Joi.string().max(255).required(),
      type: Joi.string().valid('<PERSON><PERSON><PERSON><PERSON>', '<PERSON>fer<PERSON><PERSON>', 'Técnico de enfermagem', 'Outro').required(),
      agent_registration: Joi.string().max(255).required(),
      phone: Joi.string().max(255).required(),
      email: Joi.string().max(255).required(),
      cpf: Joi.string().length(11).pattern(/^\d+$/).required(),
      password: Joi.string().max(100).required(),
      health_unit_id: Joi.number().integer().required()
    });
  }
}

module.exports = HealthAgentModel;
