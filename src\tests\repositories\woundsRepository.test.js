const WoundsRepository = require('../../repositories/woundsRepository');
const db = require('../../config/db');

jest.mock('../../config/db');

describe('WoundsRepository', () => {
  let repository;

  const woundsMock = [
    {
      id: 1,
      wound_type: 'Corte profundo na perna',
      anatomical_location: 'perna',
      first_occurrence_date: '2025-05-27T08:30:00Z',
      severity_level: 1,
      status: 'ativa',
      registration_date: '2025-05-28T10:00:00Z',
      patient_id: 1,
    },
    {
      id: 2,
      wound_type: 'Corte leve na perna',
      anatomical_location: 'perna',
      first_occurrence_date: '2025-05-26T09:00:00Z',
      severity_level: 3,
      status: 'em cicatrização',
      registration_date: '2025-05-28T10:00:00Z',
      patient_id: 2,
    },
  ];

  beforeEach(() => {
    repository = new WoundsRepository();
    jest.clearAllMocks();
  });

  it('deve retornar todas as feridas (findAll)', async () => {
    db.query.mockResolvedValue({ rows: woundsMock });

    const result = await repository.findAll();

    expect(db.query).toHaveBeenCalledWith(
      'SELECT id, wound_type, anatomical_location, first_occurrence_date, severity_level, status, registration_date, patient_id FROM wound'
        );
    expect(result).toEqual(woundsMock);
  });

  it('deve retornar uma ferida por ID (findById)', async () => {
    db.query.mockResolvedValue({ rows: [woundsMock[0]] });

    const result = await repository.findById(1);

    expect(db.query).toHaveBeenCalledWith(
        'SELECT id, wound_type, anatomical_location, first_occurrence_date, severity_level, status, registration_date, patient_id FROM wound WHERE id = $1',
        [1]
    );
    expect(result).toEqual(woundsMock[0]);
  });

  it('deve lançar erro se a ferida não for encontrada (findById)', async () => {
    db.query.mockResolvedValue({ rows: [] });

    await expect(repository.findById(99)).rejects.toThrow('Ferida não encontrada');

    expect(db.query).toHaveBeenCalledWith(
        'SELECT id, wound_type, anatomical_location, first_occurrence_date, severity_level, status, registration_date, patient_id FROM wound WHERE id = $1',
        [99]
    );
  });

  it('deve criar uma nova ferida (create)', async () => {
    const dataToCreate = { ...woundsMock[0] };
    delete dataToCreate.id;

    db.query.mockResolvedValue({ rows: [dataToCreate] });

    const result = await repository.create(dataToCreate);

    expect(db.query).toHaveBeenCalledWith(
        'INSERT INTO wound (wound_type, anatomical_location, first_occurrence_date, severity_level, status, registration_date, patient_id) VALUES ($1, $2, $3, $4, $5, $6, $7) RETURNING *',
        [dataToCreate.wound_type, dataToCreate.anatomical_location, dataToCreate.first_occurrence_date, dataToCreate.severity_level, dataToCreate.status, dataToCreate.registration_date, dataToCreate.patient_id]
    );
    expect(result).toEqual(dataToCreate);
  });

  it('deve lançar erro se a ferida não for encontrada (create)', async () => {
    db.query.mockResolvedValue({ rows: [] });

    await expect(repository.create(woundsMock[0])).rejects.toThrow('Ferida não encontrada');
  });

  it('deve atualizar uma ferida existente (update)', async () => {
    const updatedData = { ...woundsMock[0], wound_type: 'Corte profundo na perna atualizado' };

    db.query.mockResolvedValue({ rows: [updatedData] });

    const result = await repository.update(1, updatedData);

    expect(db.query).toHaveBeenCalledWith(
        'UPDATE wound SET wound_type = $1, anatomical_location = $2, first_occurrence_date = $3, severity_level = $4, status = $5, registration_date = $6, patient_id = $7 WHERE id = $8 RETURNING *',
        [updatedData.wound_type, updatedData.anatomical_location, updatedData.first_occurrence_date, updatedData.severity_level, updatedData.status, updatedData.registration_date, updatedData.patient_id, 1]
    );
    expect(result).toEqual(updatedData);
  });

  it('deve lançar erro se a ferida não for encontrada (update)', async () => {
    db.query.mockResolvedValue({ rows: [] });

    await expect(repository.update(99, woundsMock[0])).rejects.toThrow('Ferida não encontrada');
  });

  it('deve excluir uma ferida existente (delete)', async () => {
    db.query.mockResolvedValue({ rows: [woundsMock[0]] });

    const result = await repository.delete(1);

    expect(db.query).toHaveBeenCalledWith(
        'DELETE FROM wound WHERE id = $1',
        [1]
        );
    expect(result).toEqual(woundsMock[0]);
  });

  it('deve lançar erro se a ferida não for encontrada (delete)', async () => {
    db.query.mockResolvedValue({ rows: [] });

    await expect(repository.delete(99)).rejects.toThrow('Ferida não encontrada');
  });

  it('deve retornar feridas de um paciente por ID (findByPatient)', async () => {
