const Joi = require("joi");

class PatientModel {
  static get schema() {
    return Joi.object({
      full_name: Joi.string().max(255).required(),
      cpf: Joi.string().length(11).pattern(/^\d+$/).required(),
      email: Joi.string().email().max(100).required(),
      password: Joi.string().max(100).required(),
      birth_date: Joi.date().required(),
      gender: Joi.string().valid("Masculino", "Feminino", "Outro").required(),
      address: Joi.string().allow("").required(),
      phone: Joi.string().pattern(/^\d+$/).required(),
      education_level: Joi.string()
        .valid(
          "Ensino Fundamental Incompleto",
          "Ensino Fundamental Completo",
          "Ensino Médio Incompleto",
          "Ensino Médio Completo",
          "Ensino Superior Incompleto",
          "Ensino Superior Completo",
          "Mestrado",
          "Do<PERSON>rado",
          "<PERSON><PERSON>",
          "Não informado"
        )
        .required(),
      has_mobility_difficulty: Joi.boolean().required(),
      has_visual_impairment: Joi.boolean().required(),
      registration_date: Joi.date().required(),
    });
  }
}

module.exports = PatientModel;
