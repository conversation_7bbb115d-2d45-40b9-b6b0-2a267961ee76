-- Foreign Keys
ALTER TABLE "health_agent" ADD FOREIGN KEY ("health_unit_id") REFERENCES "health_unit" ("id");
ALTER TABLE "wound" ADD FOREIGN KEY ("patient_id") REFERENCES "patient" ("id");
ALTER TABLE "wound_monitoring" ADD FOREIGN KEY ("wound_id") REFERENCES "wound" ("id");
ALTER TABLE "wound_monitoring" ADD FOREIGN KEY ("health_agent_id") REFERENCES "health_agent" ("id");
ALTER TABLE "photo_record" ADD FOREIGN KEY ("wound_id") REFERENCES "wound" ("id");
ALTER TABLE "photo_record" ADD FOREIGN KEY ("health_agent_id") REFERENCES "health_agent" ("id");
ALTER TABLE "patient_questionnaire" ADD FOREIGN KEY ("patient_id") REFERENCES "patient" ("id");
ALTER TABLE "patient_comorbidity" ADD FOREIGN KEY ("patient_id") REFERENCES "patient" ("id");
ALTER TABLE "recommended_material" ADD FOREIGN KEY ("wound_id") REFERENCES "wound" ("id");
ALTER TABLE "recommended_material" ADD FOREIGN KEY ("health_agent_id") REFERENCES "health_agent" ("id");
ALTER TABLE "home_visit" ADD FOREIGN KEY ("patient_id") REFERENCES "patient" ("id");
ALTER TABLE "home_visit" ADD FOREIGN KEY ("health_agent_id") REFERENCES "health_agent" ("id");
ALTER TABLE "system_notification" ADD FOREIGN KEY ("patient_id") REFERENCES "patient" ("id");
ALTER TABLE "emergency" ADD FOREIGN KEY ("patient_id") REFERENCES "patient" ("id");
ALTER TABLE "emergency" ADD FOREIGN KEY ("health_agent_id") REFERENCES "health_agent" ("id");