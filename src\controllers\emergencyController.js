class EmergencyController {
  constructor(service) {
    this.service = service;
  }

  async index(req, res) {
    try {
      
      if (Object.keys(req.query).length > 0) {
        const filters = {};

        
        if (req.query.attended !== undefined) {
          filters.attended = req.query.attended === "true";
        }

        
        if (req.query.patient_id) {
          filters.patient_id = parseInt(req.query.patient_id);
        }

        if (req.query.agent_id) {
          filters.agent_id = parseInt(req.query.agent_id);
        }

        const data = await this.service.findByFilters(filters);
        return res.json(data);
      }

      
      const data = await this.service.findAll();
      res.json(data);
    } catch (err) {
      console.error("[EmergencyController] - Erro na emergência:", err.message);
      res.status(400).json({ error: err.message });
    }
  }

  async show(req, res) {
    try {
      const data = await this.service.findById(req.params.id);
      res.json(data);
    } catch (err) {
      console.error("[EmergencyController] - <PERSON>rro na emergência:", err.message);
      res.status(404).json({ error: err.message });
    }
  }

  async create(req, res) {
    try {
      const data = await this.service.create(req.body);
      res.status(201).json(data);
    } catch (err) {
      console.error(
        "[EmergencyController] - Erro ao criar emergência:",
        err.message
      );
      res.status(400).json({ error: err.message });
    }
  }

  async update(req, res) {
    try {
      const data = await this.service.update(req.params.id, req.body);
      res.json(data);
    } catch (err) {
      console.error(
        "[EmergencyController] - Erro ao atualizar emergência:",
        err.message
      );
      res.status(400).json({ error: err.message });
    }
  }
}

module.exports = EmergencyController;
