const EmergencyService = require('../../services/emergencyService');

describe('EmergencyService', () => {
  const mockRepository = {
    findAll: jest.fn(),
    findById: jest.fn(),
    findByFilters: jest.fn(),
    create: jest.fn(),
    update: jest.fn()
  };

  const service = new EmergencyService(mockRepository);

  const emergenciesMock = [
    {
      id: 1,
      call_datetime: '2025-05-01 14:32:00',
      reason: 'Queda dentro de casa, com dores no braço.',
      attended: false,
      observations: 'Paciente imobilizado no local até chegada da equipe.',
      referral: 'UPA',
      patient_id: 1,
      agent_id: 2
    },
    {
      id: 2,
      call_datetime: '2025-05-03 22:15:00',
      reason: 'Paciente relatando falta de ar súbita.',
      attended: true,
      observations: 'Encaminhado com suporte de oxigênio.',
      referral: 'Hospital Municipal',
      patient_id: 2,
      agent_id: 3
    }
  ];

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('deve retornar todas as emergências', async () => {
    mockRepository.findAll.mockResolvedValue(emergenciesMock);
    const result = await service.findAll();

    expect(mockRepository.findAll).toHaveBeenCalledTimes(1);
    expect(result).toEqual(emergenciesMock);
  });
  

  it('deve retornar uma emergência pelo ID', async () => {
    mockRepository.findById.mockResolvedValue(emergenciesMock[0]);
    const result = await service.findById(1);

    expect(mockRepository.findById).toHaveBeenCalledTimes(1);
    expect(result).toEqual(emergenciesMock[0]);
  });

  it('deve lançar um erro se a emergência não for encontrada', async () => {
    mockRepository.findById.mockResolvedValue(null);

    await expect(service.findById(999)).rejects.toThrow('Emergência não encontrada');
    expect(mockRepository.findById).toHaveBeenCalledTimes(1);
  });

  it('deve retornar emergências filtradas', async () => {
    const filters = { attended: false };
    const filteredEmergencies = [emergenciesMock[0]];

    mockRepository.findByFilters.mockResolvedValue(filteredEmergencies);
    const result = await service.findByFilters(filters);

    expect(mockRepository.findByFilters).toHaveBeenCalledWith(filters);
    expect(mockRepository.findByFilters).toHaveBeenCalledTimes(1);
    expect(result).toEqual(filteredEmergencies);
  });

  it('deve criar uma nova emergência', async () => {
    const newEmergency = {
      call_datetime: '2025-05-05 10:30:00',
      reason: 'Paciente com dores no peito.',
      attended: false,
      observations: 'Aguardando atendimento.',
      referral: 'UPA',
      patient_id: 3,
      agent_id: 1
    };

    const createdEmergency = { id: 3, ...newEmergency };
    mockRepository.create.mockResolvedValue(createdEmergency);

    const result = await service.create(newEmergency);

    expect(mockRepository.create).toHaveBeenCalledWith(newEmergency);
    expect(mockRepository.create).toHaveBeenCalledTimes(1);
    expect(result).toEqual(createdEmergency);
  });

  it('deve atualizar uma emergência existente', async () => {
    const updateData = {
      attended: true,
      observations: 'Paciente atendido e encaminhado.',
      referral: 'Hospital Central'
    };

    const updatedEmergency = { ...emergenciesMock[0], ...updateData };
    mockRepository.update.mockResolvedValue(updatedEmergency);

    const result = await service.update(1, updateData);

    expect(mockRepository.update).toHaveBeenCalledWith(1, updateData);
    expect(mockRepository.update).toHaveBeenCalledTimes(1);
    expect(result).toEqual(updatedEmergency);
  });
});


