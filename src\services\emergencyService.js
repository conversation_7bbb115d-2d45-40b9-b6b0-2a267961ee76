const EmergencyModel = require("../models/emergencyModel");

class EmergencyService {
  constructor(repository) {
    this.repository = repository;
  }

  async findAll() {
    return await this.repository.findAll();
  }

  async findById(id) {
    const data = await this.repository.findById(id);
    if (!data) throw new Error("Emergência não encontrada");
    return data;
  }

  async findByFilters(filters) {
    return await this.repository.findByFilters(filters);
  }

  async create(emergency) {
    return await this.repository.create(emergency);
  }

  async update(id, data) {
    return await this.repository.update(id, data);
  }
}

module.exports = EmergencyService;
