<!DOCTYPE html>
<html lang="pt-BR">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" href="../../../public/css/global.css" />
    <title>Gerenciar Pacientes</title>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&display=swap"
      rel="stylesheet"
    />
    <script src="../../../public/js/auth-check.js"></script>
    <style>
      .patient-list {
        display: flex;
        flex-direction: column;
        gap: 16px;
      }
      
      .patient-card {
        background: white;
        border-radius: 12px;
        padding: 20px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        border-left: 4px solid #013c6d;
      }
      
      .patient-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;
      }
      
      .patient-name {
        font-size: 18px;
        font-weight: 600;
        color: #2d3748;
      }
      
      .patient-status {
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 500;
      }
      
      .status-stable {
        background-color: #c6f6d5;
        color: #38a169;
      }
      
      .status-attention {
        background-color: #fefcbf;
        color: #d69e2e;
      }
      
      .status-critical {
        background-color: #fed7d7;
        color: #e53e3e;
      }
      
      .patient-info {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 12px;
        margin-bottom: 16px;
      }
      
      .info-item {
        display: flex;
        flex-direction: column;
      }
      
      .info-label {
        font-size: 12px;
        color: #718096;
        margin-bottom: 4px;
      }
      
      .info-value {
        font-size: 14px;
        color: #2d3748;
      }
      
      .patient-actions {
        display: flex;
        gap: 8px;
        flex-wrap: wrap;
      }
      
      .action-btn {
        padding: 8px 16px;
        border-radius: 6px;
        border: none;
        font-size: 12px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s;
      }
      
      .btn-primary {
        background-color: #013c6d;
        color: white;
      }
      
      .btn-secondary {
        background-color: #f8fafc;
        color: #4a5568;
        border: 1px solid #e2e8f0;
      }
      
      .btn-danger {
        background-color: #fed7d7;
        color: #e53e3e;
      }
      
      .search-bar {
        margin-bottom: 20px;
      }
      
      .search-input {
        width: 100%;
        padding: 12px 16px;
        border: 1px solid #e2e8f0;
        border-radius: 8px;
        font-size: 14px;
      }
      
      .filters {
        display: flex;
        gap: 12px;
        margin-bottom: 20px;
        flex-wrap: wrap;
      }
      
      .filter-btn {
        padding: 8px 16px;
        border: 1px solid #e2e8f0;
        background: white;
        border-radius: 20px;
        font-size: 12px;
        cursor: pointer;
        transition: all 0.2s;
      }
      
      .filter-btn.active {
        background-color: #013c6d;
        color: white;
        border-color: #013c6d;
      }
      
      /* Estilos para o modal/popup */
      .modal {
        display: none;
        position: fixed;
        z-index: 100;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0,0,0,0.5);
        overflow-y: auto;
      }
      
      .modal-content {
        background-color: white;
        margin: 5% auto;
        width: 80%;
        max-width: 800px;
        border-radius: 12px;
        box-shadow: 0 5px 25px rgba(0,0,0,0.2);
        position: relative;
        animation: modalFadeIn 0.3s ease;
      }
      
      @keyframes modalFadeIn {
        from { opacity: 0; transform: translateY(-20px); }
        to { opacity: 1; transform: translateY(0); }
      }
      
      .modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 20px 24px;
        border-bottom: 1px solid #e2e8f0;
      }
      
      .modal-title {
        font-size: 20px;
        font-weight: 600;
        color: #2d3748;
      }
      
      .modal-close {
        font-size: 24px;
        color: #718096;
        cursor: pointer;
        background: none;
        border: none;
      }
      
      .modal-body {
        padding: 24px;
      }
      
      .modal-footer {
        padding: 16px 24px;
        border-top: 1px solid #e2e8f0;
        display: flex;
        justify-content: flex-end;
        gap: 12px;
      }
      
      .patient-detail-section {
        margin-bottom: 24px;
      }
      
      .section-title {
        font-size: 16px;
        font-weight: 600;
        color: #4a5568;
        margin-bottom: 12px;
        border-bottom: 1px solid #edf2f7;
        padding-bottom: 8px;
      }
      
      .detail-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
        gap: 16px;
      }
      
      .detail-item {
        margin-bottom: 8px;
      }
      
      .detail-label {
        font-size: 12px;
        color: #718096;
        margin-bottom: 4px;
      }
      
      .detail-value {
        font-size: 14px;
        color: #2d3748;
        font-weight: 500;
      }
      
      .patient-info-header {
        display: flex;
        align-items: center;
        gap: 12px;
        margin-bottom: 20px;
      }
      
      .patient-avatar {
        width: 64px;
        height: 64px;
        background-color: #e2e8f0;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        color: #4a5568;
      }
      
      .patient-header-info {
        flex: 1;
      }
      
      .patient-name-large {
        font-size: 24px;
        font-weight: 600;
        color: #2d3748;
        margin-bottom: 4px;
      }
      
      .patient-subtitle {
        font-size: 14px;
        color: #718096;
      }
      
      .gallery-container {
        display: flex;
        gap: 12px;
        overflow-x: auto;
        padding-bottom: 16px;
        margin: 12px 0;
      }
      
      .gallery-image {
        width: 120px;
        height: 120px;
        object-fit: cover;
        border-radius: 8px;
        cursor: pointer;
        border: 2px solid transparent;
        transition: all 0.2s;
      }
      
      .gallery-image:hover {
        border-color: #3182ce;
        transform: scale(1.05);
      }
      
      .action-icon {
        margin-right: 8px;
      }
      
      .edit-form {
        display: none;
        padding: 16px;
        background-color: #f7fafc;
        border-radius: 8px;
        margin-top: 16px;
      }
      
      .form-row {
        display: flex;
        gap: 16px;
        margin-bottom: 16px;
      }
      
      .form-group {
        flex: 1;
      }
      
      .form-label {
        font-size: 14px;
        color: #4a5568;
        margin-bottom: 6px;
        display: block;
      }
      
      .form-input {
        width: 100%;
        padding: 10px 12px;
        border: 1px solid #e2e8f0;
        border-radius: 6px;
        font-size: 14px;
      }

      .cpf-display {
        color: #718096;
        font-size: 13px;
        margin-top: 4px;
      }
      
      .pills {
        display: flex;
        gap: 8px;
      }
      
      .pill {
        font-size: 12px;
        padding: 4px 10px;
        border-radius: 20px;
        background-color: #edf2f7;
        color: #4a5568;
      }
      
      .pill-blue {
        background-color: #ebf8ff;
        color: #3182ce;
      }
      
      .pill-green {
        background-color: #f0fff4;
        color: #38a169;
      }
      
      .tab-buttons {
        display: flex;
        gap: 2px;
        margin-bottom: 16px;
        background-color: #f7fafc;
        border-radius: 8px;
        padding: 4px;
      }
      
      .tab-button {
        padding: 8px 16px;
        font-size: 14px;
        border: none;
        background: none;
        cursor: pointer;
        border-radius: 6px;
        color: #4a5568;
        font-weight: 500;
      }
      
      .tab-button.active {
        background-color: white;
        color: #3182ce;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
      }
      
      .tab-content {
        display: none;
      }
      
      .tab-content.active {
        display: block;
        animation: fadeIn 0.3s ease;
      }
      
      @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
      }
    </style>
  </head>
  <body>
    <div class="desktop-frame">
      <div class="sidebar">
        <div class="sidebar-logo">
          <img
            src="../../../public/images/logo/cicatriza_semnada.png"
            alt="Logo"
            class="logo-small"
          />
          <h3 class="sidebar-title">Cicatriza+</h3>
        </div>
        <ul class="sidebar-menu">
          <a href="menu-principal-agente.html">
            <li class="sidebar-item">
              <span class="sidebar-icon">🏠</span>Início
            </li>
          </a>
          </a>
          <a href="gerenciar-pacientes.html">
            <li class="sidebar-item">
              <span class="sidebar-icon">👥</span>Pacientes
            </li>
          </a>
          <li class="sidebar-item">
            <span class="sidebar-icon">📝</span>Tutorial
          </li>
          <li class="sidebar-item">
            <span class="sidebar-icon">👤</span>Perfil
          </li>
          <a href="notificacoes-agente.html" class="sidebar-link">
            <li class="sidebar-item">
              <span class="sidebar-icon">🔔</span>Notificações
            </li>
          </a>
          <a href="emergencia-agente.html">
            <li class="sidebar-item active">
              <span class="sidebar-icon">⚠️</span>Emergência
            </li>
          </a>
        </ul>
      </div>
      <div class="desktop-content">
        <div class="desktop-header">
          <div class="header-title">Gerenciar Pacientes</div>
        </div>
        <div class="main-area">
          <div class="search-bar">
            <input 
              type="text" 
              class="search-input" 
              placeholder="Buscar paciente por nome, CPF ou ID..."
            />
          </div>
          
          <div class="filters">
            <button class="filter-btn active">Todos</button>
            <button class="filter-btn">Estável</button>
            <button class="filter-btn">Atenção</button>
            <button class="filter-btn">Crítico</button>
            <button class="filter-btn">Novos</button>
          </div>

          <div class="patient-list" id="patient-list">
            <!-- cards serão preenchidos via JS -->
          </div>
          <div id="record-display" style="margin-top:20px;margin-bottom:20px;"></div>
        </div>
        
        <!-- FOOTER -->
        <div class="footer-desktop">
          <div class="footer-left">
            Menu - início - Perfil - Login - Contate - nos
          </div>
          <div class="footer-right">Cicatriza+</div>
        </div>
      </div>
    </div>

    <div class="mobile-navbar">
      <div class="mobile-nav-container">
        <a href="menu-principal-agente.html" class="mobile-nav-item">
          <div class="mobile-nav-icon">🏠</div>
          <div class="mobile-nav-text">Início</div>
        </a>
        <a href="gerenciar-pacientes.html" class="mobile-nav-item">
          <div class="mobile-nav-icon">👥</div>
          <div class="mobile-nav-text">Pacientes</div>
        </a>
        <a href="notificacoes-agente.html" class="mobile-nav-item">
          <div class="mobile-nav-icon">🔔</div>
          <div class="mobile-nav-text">Notificações</div>
        </a>
        <a href="perfil-agente.html" class="mobile-nav-item">
          <div class="mobile-nav-icon">👤</div>
          <div class="mobile-nav-text">Perfil</div>
        </a>
      </div>
    </div>

    <!-- VLibras -->
    <div vw class="enabled">
      <div vw-access-button class="active"></div>
      <div vw-plugin-wrapper>
        <div class="vw-plugin-top-wrapper"></div>
      </div>
    </div>
    <script src="https://vlibras.gov.br/app/vlibras-plugin.js"></script>
    <script>
      new window.VLibras.Widget("https://vlibras.gov.br/app");
    </script>
    <script>
  let allPatients = [];
  let currentPatientId = null;

  // Busca todos os pacientes e exibe cards
  async function loadPatients() {
    try {
      const res = await fetch('/api/patients', {
        credentials: 'include' // Garante que os cookies sejam enviados na requisição
      });
      
      if (!res.ok) {
        if (res.status === 401) {
          alert("Sua sessão expirou. Por favor, faça login novamente.");
          window.location.href = '/login.html';
          return;
        }
        throw new Error(`Erro ao carregar pacientes: ${res.status}`);
      }
      
      allPatients = await res.json();
      renderList(allPatients);
    } catch (error) {
      console.error("Falha ao carregar dados:", error);
      document.getElementById('patient-list').innerHTML = 
        '<div style="color: red; padding: 20px;">Erro ao carregar lista de pacientes. Tente novamente mais tarde.</div>';
    }
  }

  // Renderiza lista de cards
  function renderList(list) {
    const container = document.getElementById('patient-list');
    container.innerHTML = '';
    list.forEach(p => {
      // Formatar CPF para exibição
      const formattedCpf = p.cpf.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, "$1.$2.$3-$4");
      
      const card = document.createElement('div');
      card.className = 'patient-card';
      card.innerHTML = `
        <div class="patient-header">
          <div class="patient-name">
            ${p.full_name}
            <div class="cpf-display">CPF: ${formattedCpf}</div>
          </div>
        </div>
        <div class="patient-actions">
          <button class="action-btn btn-primary" onclick="viewPatientDetails(${p.id})">
            Ver Detalhes
          </button>
        </div>`;
      container.appendChild(card);
    });
  }

  // Filtra conforme texto de busca
  document.querySelector('.search-input').addEventListener('input', e => {
    const term = e.target.value.toLowerCase();
    renderList(allPatients.filter(p =>
      p.full_name.toLowerCase().includes(term) ||
      p.cpf.includes(term) ||
      String(p.id) === term
    ));
  });

  // Abre o modal com detalhes do paciente
  async function viewPatientDetails(id) {
    currentPatientId = id;
    
    // Mostrar o modal
    document.getElementById('patient-details-modal').style.display = 'block';
    document.getElementById('patient-details-content').innerHTML = '<p>Carregando dados...</p>';
    
    try {
      const [rp, rq, rph, rw] = await Promise.all([
        fetch(`/api/patients/${id}`, { credentials: 'include' }),
        fetch(`/api/patient-questionnaire?patient_id=${id}`, { credentials: 'include' }),
        fetch(`/api/photo-records?patient_id=${id}`, { credentials: 'include' }),
        fetch(`/api/wounds?patient_id=${id}`, { credentials: 'include' })
      ]);
      
      if (!rp.ok) {
        if (rp.status === 401) {
          alert("Sua sessão expirou. Por favor, faça login novamente.");
          window.location.href = '/login.html';
          return;
        }
        throw new Error("Erro ao carregar dados do paciente");
      }
      
      const p = await rp.json(),
            qs = rq.ok ? await rq.json() : [],
            ph = rph.ok ? await rph.json() : [],
            ws = rw.ok ? await rw.json() : [];
      
      // Formatar CPF
      const formattedCpf = p.cpf.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, "$1.$2.$3-$4");
      
      // Data de nascimento formatada
      const birthDate = p.birth_date ? new Date(p.birth_date).toLocaleDateString('pt-BR') : 'Não informada';
      
      // Calcular idade
      let age = 'N/A';
      if (p.birth_date) {
        const today = new Date();
        const birthDateObj = new Date(p.birth_date);
        age = today.getFullYear() - birthDateObj.getFullYear();
        if (today.getMonth() < birthDateObj.getMonth() || 
            (today.getMonth() === birthDateObj.getMonth() && today.getDate() < birthDateObj.getDate())) {
          age--;
        }
      }
      
      // Renderizar conteúdo detalhado
      let content = `
        <div class="patient-info-header">
          <div class="patient-avatar">👤</div>
          <div class="patient-header-info">
            <div class="patient-name-large">${p.full_name}</div>
            <div class="patient-subtitle">ID: ${p.id} | CPF: ${formattedCpf}</div>
          </div>
        </div>
        
        <div class="tab-buttons">
          <button class="tab-button active" onclick="switchTab('info')">Informações Pessoais</button>
          <button class="tab-button" onclick="switchTab('medical')">Dados Médicos</button>
          <button class="tab-button" onclick="switchTab('photos')">Registros Fotográficos</button>
        </div>
        
        <div id="tab-info" class="tab-content active">
          <div class="patient-detail-section">
            <div class="section-title">Dados Pessoais</div>
            <div class="detail-grid">
              <div class="detail-item">
                <div class="detail-label">Nome Completo</div>
                <div class="detail-value">${p.full_name}</div>
              </div>
              <div class="detail-item">
                <div class="detail-label">CPF</div>
                <div class="detail-value">${formattedCpf}</div>
              </div>
              <div class="detail-item">
                <div class="detail-label">Data de Nascimento</div>
                <div class="detail-value">${birthDate}</div>
              </div>
              <div class="detail-item">
                <div class="detail-label">Idade</div>
                <div class="detail-value">${age} anos</div>
              </div>
              <div class="detail-item">
                <div class="detail-label">Gênero</div>
                <div class="detail-value">${p.gender || 'Não informado'}</div>
              </div>
              <div class="detail-item">
                <div class="detail-label">E-mail</div>
                <div class="detail-value">${p.email || 'Não informado'}</div>
              </div>
              <div class="detail-item">
                <div class="detail-label">Telefone</div>
                <div class="detail-value">${p.phone || 'Não informado'}</div>
              </div>
              <div class="detail-item">
                <div class="detail-label">Endereço</div>
                <div class="detail-value">${p.address || 'Não informado'}</div>
              </div>
            </div>
          </div>
          
          <div class="patient-detail-section">
            <div class="section-title">Informações Adicionais</div>
            <div class="detail-grid">
              <div class="detail-item">
                <div class="detail-label">Nível de Escolaridade</div>
                <div class="detail-value">${p.education_level || 'Não informado'}</div>
              </div>
              <div class="detail-item">
                <div class="detail-label">Dificuldade de Locomoção</div>
                <div class="detail-value">${p.has_mobility_difficulty ? 'Sim' : 'Não'}</div>
              </div>
              <div class="detail-item">
                <div class="detail-label">Deficiência Visual</div>
                <div class="detail-value">${p.has_visual_impairment ? 'Sim' : 'Não'}</div>
              </div>
              <div class="detail-item">
                <div class="detail-label">Data de Cadastro</div>
                <div class="detail-value">${p.registration_date ? new Date(p.registration_date).toLocaleDateString('pt-BR') : 'Não informada'}</div>
              </div>
            </div>
          </div>
        </div>
        
        <div id="tab-medical" class="tab-content">
          <div class="patient-detail-section">
            <div class="section-title">Feridas</div>
            ${ws.length ? `
              <table style="width:100%; border-collapse: collapse;">
                <tr style="border-bottom: 1px solid #e2e8f0; text-align: left;">
                  <th style="padding: 10px;">Tipo</th>
                  <th style="padding: 10px;">Localização</th>
                  <th style="padding: 10px;">Status</th>
                  <th style="padding: 10px;">Data</th>
                </tr>
                ${ws.map(w => `
                  <tr style="border-bottom: 1px solid #e2e8f0;">
                    <td style="padding: 10px;">${w.wound_type}</td>
                    <td style="padding: 10px;">${w.anatomical_location}</td>
                    <td style="padding: 10px;">
                      <span class="pill ${w.status === 'ativa' ? 'pill-blue' : 'pill-green'}">${w.status}</span>
                    </td>
                    <td style="padding: 10px;">${new Date(w.first_occurrence_date).toLocaleDateString('pt-BR')}</td>
                  </tr>
                `).join('')}
              </table>
            ` : '<p>Nenhuma ferida registrada.</p>'}
          </div>
          
          <div class="patient-detail-section">
            <div class="section-title">Questionários</div>
            ${qs.length ? `
              <table style="width:100%; border-collapse: collapse;">
                <tr style="border-bottom: 1px solid #e2e8f0; text-align: left;">
                  <th style="padding: 10px;">Data</th>
                  <th style="padding: 10px;">Nível de Dor</th>
                  <th style="padding: 10px;">Sintomas</th>
                  <th style="padding: 10px;">Detalhes</th>
                </tr>
                ${qs.map(q => `
                  <tr style="border-bottom: 1px solid #e2e8f0;">
                    <td style="padding: 10px;">${new Date(q.completion_date).toLocaleDateString('pt-BR')}</td>
                    <td style="padding: 10px;">${q.pain_level}/5</td>
                    <td style="padding: 10px;">
                      ${q.has_fever ? '<span class="pill">Febre</span>' : ''}
                      ${q.has_discharge ? '<span class="pill">Secreção</span>' : ''}
                      ${q.surrounding_redness ? '<span class="pill">Vermelhidão</span>' : ''}
                      ${q.swelling ? '<span class="pill">Inchaço</span>' : ''}
                    </td>
                    <td style="padding: 10px;">
                      <button class="action-btn btn-secondary" onclick="showQuestionnaireDetails(${q.id})">Detalhes</button>
                    </td>
                  </tr>
                `).join('')}
              </table>
            ` : '<p>Nenhum questionário preenchido.</p>'}
          </div>
        </div>
        
        <div id="tab-photos" class="tab-content">
          <div class="patient-detail-section">
            <div class="section-title">Galeria de Fotos</div>
            ${ph.length ? `
              <div class="gallery-container">
                ${ph.map(p => `
                  <img src="${p.file_path}" 
                    alt="Foto da ferida" 
                    class="gallery-image" 
                    onclick="showLargePhoto('${p.file_path}', '${new Date(p.photo_date).toLocaleDateString('pt-BR')}')"
                  />
                `).join('')}
              </div>
            ` : '<p>Nenhuma foto registrada.</p>'}
          </div>
        </div>
      `;
      
      document.getElementById('patient-details-content').innerHTML = content;
      
      document.getElementById('edit-patient-btn').onclick = () => editPatient(p);
      document.getElementById('delete-patient-btn').onclick = () => confirmDeletePatient(p.id);
      
    } catch (error) {
      document.getElementById('patient-details-content').innerHTML = `
        <div style="text-align: center; color: #e53e3e; padding: 20px;">
          <p>Erro ao carregar dados do paciente</p>
          <p>${error.message}</p>
        </div>`;
    }
  }
  
  function switchTab(tabId) {
    document.querySelectorAll('.tab-button').forEach(tab => {
      tab.classList.remove('active');
    });
    document.querySelectorAll('.tab-content').forEach(content => {
      content.classList.remove('active');
    });
    
    document.querySelector(`.tab-button[onclick="switchTab('${tabId}')"]`).classList.add('active');
    document.getElementById(`tab-${tabId}`).classList.add('active');
  }
  
  function showLargePhoto(path, date) {
    const modal = document.getElementById('patient-details-modal');
    modal.innerHTML += `
      <div class="photo-overlay" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.8); 
        display: flex; align-items: center; justify-content: center; z-index: 200;">
        <div style="position: relative; max-width: 90%; max-height: 90%;">
          <button onclick="this.parentElement.parentElement.remove()" 
            style="position: absolute; top: -20px; right: -20px; background: white; border: none; 
            width: 40px; height: 40px; border-radius: 50%; cursor: pointer; font-size: 20px;">&times;</button>
          <img src="${path}" style="max-width: 100%; max-height: 80vh; border-radius: 8px;" />
          <div style="color: white; text-align: center; margin-top: 10px;">Data: ${date}</div>
        </div>
      </div>
    `;
  }
  
  function showQuestionnaireDetails(id) {
    alert(`Detalhes do questionário ${id} serão implementados`);
  }
  
  function closeModal() {
    document.getElementById('patient-details-modal').style.display = 'none';
    currentPatientId = null;
  }
  
  function editPatient(patient) {
    const currentTab = document.querySelector('.tab-content.active');
    const editModeExists = document.getElementById('edit-form');
    
    if (editModeExists) {
      editModeExists.remove();
      document.getElementById('edit-patient-btn').innerText = '✏️ Editar Paciente';
      return;
    }
    
    document.getElementById('edit-patient-btn').innerText = '❌ Cancelar Edição';
    
    const editForm = document.createElement('div');
    editForm.id = 'edit-form';
    editForm.className = 'edit-form';
    editForm.style.display = 'block';
    
    editForm.innerHTML = `
      <h3 style="margin-bottom: 16px; color: #2d3748;">Editar Dados do Paciente</h3>
      
      <div class="form-row">
        <div class="form-group">
          <label class="form-label">Nome Completo</label>
          <input type="text" class="form-input" id="edit-name" value="${patient.full_name || ''}">
        </div>
        <div class="form-group">
          <label class="form-label">Email</label>
          <input type="email" class="form-input" id="edit-email" value="${patient.email || ''}">
        </div>
      </div>
      
      <div class="form-row">
        <div class="form-group">
          <label class="form-label">Telefone</label>
          <input type="tel" class="form-input" id="edit-phone" value="${patient.phone || ''}">
        </div>
        <div class="form-group">
          <label class="form-label">Endereço</label>
          <input type="text" class="form-input" id="edit-address" value="${patient.address || ''}">
        </div>
      </div>
      
      <div class="form-row">
        <button type="button" class="action-btn btn-primary" onclick="savePatientChanges(${patient.id})">
          Salvar Alterações
        </button>
      </div>
    `;
    currentTab.appendChild(editForm);
  }
  
  async function savePatientChanges(patientId) {
    try {
      const updatedData = {
        full_name: document.getElementById('edit-name').value,
        email: document.getElementById('edit-email').value,
        phone: document.getElementById('edit-phone').value,
        address: document.getElementById('edit-address').value
      };
      
      const response = await fetch(`/api/patients/${patientId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(updatedData)
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Erro ao atualizar dados');
      }
      
      loadPatients();
      viewPatientDetails(patientId);
      
      alert('Dados atualizados com sucesso!');
      
    } catch (error) {
      alert(`Erro ao atualizar dados: ${error.message}`);
    }
  }

  function confirmDeletePatient(id) {
    const confirmed = confirm(`Tem certeza que deseja excluir este paciente? Esta ação não pode ser desfeita.`);
    
    if (confirmed) {
      deletePatient(id);
    }
  }
  
  async function deletePatient(patientId) {
    try {
      const response = await fetch(`/api/patients/${patientId}`, {
        method: 'DELETE'
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Erro ao excluir paciente');
      }
      
      loadPatients();
      closeModal();
      
      alert('Paciente excluído com sucesso!');
      
    } catch (error) {
      alert(`Erro ao excluir paciente: ${error.message}`);
    }
  }

  loadPatients();
  
  window.onclick = function(event) {
    const modal = document.getElementById('patient-details-modal');
    if (event.target == modal) {
      closeModal();
    }
  };
</script>

<div id="patient-details-modal" class="modal">
  <div class="modal-content">
    <div class="modal-header">
      <div class="modal-title">Detalhes do Paciente</div>
      <button class="modal-close" onclick="closeModal()">&times;</button>
    </div>
    <div class="modal-body" id="patient-details-content">
    </div>
    <div class="modal-footer">
      <button class="action-btn btn-secondary" onclick="closeModal()">Fechar</button>
      <button class="action-btn btn-primary" id="edit-patient-btn">
        <span class="action-icon">✏️</span>Editar Paciente
      </button>
      <button class="action-btn btn-danger" id="delete-patient-btn">
        <span class="action-icon">🗑️</span>Excluir Paciente
      </button>
    </div>
  </div>
</div>
  </body>
</html>