<!DOCTYPE html>
<html lang="pt-BR">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" href="../../../public/css/global.css" />
    <title>Perfil Agente</title>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&display=swap"
      rel="stylesheet"
    />
    <script src="../../../public/js/auth-check.js"></script>
    <style>
      .profile-card {
        background: white;
        border-radius: 12px;
        padding: 20px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
        text-align: center;
      }
      .profile-avatar {
        width: 100px;
        height: 100px;
        border-radius: 50%;
        background-color: #e2e8f0;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 48px;
        color: #718096;
        margin: 0 auto 15px;
      }
      .profile-name {
        font-size: 24px;
        font-weight: 600;
        color: #2d3748;
        margin-bottom: 5px;
      }
      .profile-role {
        font-size: 16px;
        color: #718096;
        margin-bottom: 20px;
      }
      .profile-info-grid {
        display: grid;
        grid-template-columns: 1fr;
        gap: 15px;
        text-align: left;
      }
      .info-item {
        display: flex;
        flex-direction: column;
      }
      .info-label {
        font-size: 12px;
        color: #718096;
        margin-bottom: 4px;
      }
      .info-value {
        font-size: 16px;
        color: #2d3748;
        font-weight: 500;
      }
      .edit-button {
        margin-top: 20px;
      }
      .info-section {
        margin-top: 40px;
        padding-top: 20px;
        border-top: 1px solid #e2e8f0;
      }
      .section-title {
        font-size: 18px;
        font-weight: 500;
        color: #2d3748;
        margin-bottom: 15px;
        display: flex;
        align-items: center;
        gap: 8px;
      }
      .button {
        padding: 10px 20px;
        border-radius: 8px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: background 0.3s, color 0.3s;
      }
      .button.outline {
        background: transparent;
        border: 1px solid #2b6cb0;
        color: #2b6cb0;
      }
      .button.outline:hover {
        background: #2b6cb0;
        color: white;
      }
      .logout-button {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
      }
      .loading {
        text-align: center;
        padding: 20px;
        font-style: italic;
        color: #718096;
      }
      .error-message {
        color: #e53e3e;
        text-align: center;
        padding: 20px;
        border: 1px solid #e53e3e;
        border-radius: 8px;
        background-color: #fff5f5;
        margin-bottom: 20px;
      }
    </style>
  </head>
  <body>
    <div class="desktop-frame">
      <div class="sidebar">
        <div class="sidebar-logo">
          <img
            src="../../../public/images/logo/cicatriza_semnada.png"
            alt="Logo"
            class="logo-small"
          />
          <h3 class="sidebar-title">Cicatriza+</h3>
        </div>
        <ul class="sidebar-menu">
          <a href="menu-principal-agente.html">
            <li class="sidebar-item">
              <span class="sidebar-icon">🏠</span>Início
            </li>
          </a>
          </a>
          <a href="gerenciar-pacientes.html">
            <li class="sidebar-item">
              <span class="sidebar-icon">👥</span>Pacientes
            </li>
          </a>
          <li class="sidebar-item">
            <span class="sidebar-icon">📝</span>Tutorial
          </li>
          <li class="sidebar-item">
            <span class="sidebar-icon">👤</span>Perfil
          </li>
          <a href="notificacoes-agente.html" class="sidebar-link">
            <li class="sidebar-item">
              <span class="sidebar-icon">🔔</span>Notificações
            </li>
          </a>
          <a href="emergencia-agente.html">
            <li class="sidebar-item active">
              <span class="sidebar-icon">⚠️</span>Emergência
            </li>
          </a>
        </ul>
      </div>
      <div class="desktop-content">
        <div class="desktop-header">
          <div class="header-title">Meu Perfil</div>
        </div>
        <div class="main-area">
          <div id="profileContainer">
            <div class="loading">Carregando dados do perfil...</div>
          </div>

          <div class="info-section">
            <div class="section-title">
              🔐 Segurança e Conta
            </div>
            <div style="display: flex; gap: 12px; flex-wrap: wrap;">
              <button class="button outline">Alterar Senha</button>
              <button class="button outline">Configurar Autenticação em Duas Etapas</button>
              <button id="logoutButton" class="button outline logout-button" style="color: #e53e3e; border-color: #e53e3e;">Sair da Conta</button>
            </div>
          </div>
        </div>

        <div class="footer-desktop">
          <div class="footer-left">
            Menu - início - Perfil - Login - Contate - nos
          </div>
          <div class="footer-right">Cicatriza+</div>
        </div>
      </div>
    </div>

    <div class="mobile-navbar">
      <div class="mobile-nav-container">
        <a href="menu-principal-agente.html" class="mobile-nav-item">
          <div class="mobile-nav-icon">🏠</div>
          <div class="mobile-nav-text">Início</div>
        </a>
        <a href="gerenciar-pacientes.html" class="mobile-nav-item">
          <div class="mobile-nav-icon">👥</div>
          <div class="mobile-nav-text">Pacientes</div>
        </a>
        <a href="notificacoes-agente.html" class="mobile-nav-item">
          <div class="mobile-nav-icon">🔔</div>
          <div class="mobile-nav-text">Notificações</div>
        </a>
        <a href="perfil-agente.html" class="mobile-nav-item">
          <div class="mobile-nav-icon">👤</div>
          <div class="mobile-nav-text">Perfil</div>
        </a>
      </div>
    </div>

    <!-- VLibras -->
    <div vw class="enabled">
      <div vw-access-button class="active"></div>
      <div vw-plugin-wrapper>
        <div class="vw-plugin-top-wrapper"></div>
      </div>
    </div>
    <script src="https://vlibras.gov.br/app/vlibras-plugin.js"></script>
    <script>
      new window.VLibras.Widget("https://vlibras.gov.br/app");
    </script>
    <script src="../../../public/js/logout.js"></script>
    
    <script>
      document.addEventListener('DOMContentLoaded', async function() {
        // Get logged in agent info from cookies (not localStorage)
        function getUserFromCookie() {
          const match = document.cookie.match("(^|;) ?user=([^;]*)(;|$)");
          if (!match) return null;
          try {
            return JSON.parse(decodeURIComponent(match[2]));
          } catch (e) {
            console.error("Error parsing user cookie:", e);
            return null;
          }
        }
        
        const user = getUserFromCookie();
        
        if (!user || !user.id) {
          window.location.href = 'login-agente.html';
          return;
        }
        
        const agentId = user.id;
        
        try {
          // Fetch agent data from API
          const response = await fetch(`/api/health-agents/${agentId}`);
          if (!response.ok) {
            throw new Error('Não foi possível carregar os dados do perfil.');
          }
          
          const agent = await response.json();
          console.log('Agent data:', agent); // Debug log
          
          // Determine which field contains the health unit ID
          const healthUnitId = agent.health_unit_id || agent.health_unit;
          console.log('Health Unit ID:', healthUnitId); // Debug the ID
          
          // Get health unit info if we have an ID
          let unitName = 'Não informada';
          if (healthUnitId) {
            try {
              // Add better logging for debugging
              console.log(`Fetching health unit data from: /api/health-units/${healthUnitId}`);
              
              // Make API call with proper error handling
              const unitResponse = await fetch(`/api/health-units/${healthUnitId}`);
              console.log('Unit Response Status:', unitResponse.status);
              
              if (unitResponse.ok) {
                const unitData = await unitResponse.json();
                console.log('Unit Data:', unitData);
                
                if (Array.isArray(unitData)) {
                  console.log('Unit data is an array');
                  if (unitData.length > 0) {
                    unitName = unitData[0].unit_name || unitData[0].name || 'Não informada';
                    console.log('Using unit name from array:', unitName);
                  }
                } else if (unitData && typeof unitData === 'object') {
                  console.log('Unit data is an object');
                  unitName = unitData.unit_name || unitData.name || unitData.nome || 'Não informada';
                  console.log('Using unit name from object:', unitName);
                }
              } else {
                console.error('Failed to fetch health unit data:', await unitResponse.text());
              }
            } catch (error) {
              console.error('Error fetching health unit:', error);
            }
          } else {
            console.log('No health unit ID available');
          }
          
          const profileContainer = document.getElementById('profileContainer');
          
          const profileHtml = `
            <div class="profile-card">
              <div class="profile-avatar">${getAgentIcon(agent.agent_type)}</div>
              <div class="profile-name">${agent.full_name}</div>
              <div class="profile-role">${formatAgentType(agent.agent_type)}</div>
              
              <div class="profile-info-grid">
                <div class="info-item">
                  <div class="info-label">Email</div>
                  <div class="info-value">${agent.email || 'Não informado'}</div>
                </div>
                <div class="info-item">
                  <div class="info-label">CPF</div>
                  <div class="info-value">${agent.cpf || 'Não informado'}</div>
                </div>
                <div class="info-item">
                  <div class="info-label">Telefone</div>
                  <div class="info-value">${agent.phone || 'Não informado'}</div>
                </div>
                <div class="info-item">
                  <div class="info-label">Registro Profissional</div>
                  <div class="info-value">${agent.agent_registration || 'Não informado'}</div>
                </div>
                <div class="info-item">
                  <div class="info-label">Unidade de Saúde</div>
                  <div class="info-value">${unitName}</div>
                </div>
              </div>
              <button class="button edit-button">Editar Perfil</button>
            </div>
          `;
          
          profileContainer.innerHTML = profileHtml;
          
        } catch (error) {
          console.error('Erro ao carregar dados do perfil:', error);
          document.getElementById('profileContainer').innerHTML = `
            <div class="error-message">
              ${error.message || 'Ocorreu um erro ao carregar seus dados. Por favor, tente novamente mais tarde.'}
            </div>
          `;
        }
      });
      
      function getAgentIcon(agentType) {
        if (!agentType) return '👨‍⚕️';
        
        agentType = agentType.toLowerCase();
        
        if (agentType.includes('médico')) return '👨‍⚕️';
        if (agentType.includes('enfermeiro')) return '👩‍⚕️';
        if (agentType.includes('agente')) return '🧑‍⚕️';
        if (agentType.includes('técnico')) return '👨‍💼';
        
        return '👨‍⚕️';
      }
      
      function formatAgentType(agentType) {
        if (!agentType) return 'Profissional de Saúde';
        return agentType.charAt(0).toUpperCase() + agentType.slice(1);
      }
      
      document.getElementById('logoutButton').addEventListener('click', function() {

        document.cookie = "authToken=;path=/;max-age=0";
        document.cookie = "user=;path=/;max-age=0";
        document.cookie = "redirectAfterLogin=;path=/;max-age=0";
        
        window.location.href = 'login-agente.html';
      });
    </script>
  </body>
</html>

