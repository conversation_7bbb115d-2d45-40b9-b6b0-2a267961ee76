CREATE TABLE if not exists "patient" (
  "id" SERIAL PRIMARY KEY,
  "full_name" VARCHAR(255),
  "cpf" VARCHAR(14),
  "email" VARCHAR(100),
  "password" VARCHAR(100),
  "birth_date" DATE,
  "gender" VARCHAR(10),
  "address" TEXT,
  "phone" VARCHAR(20),
  "education_level" VARCHAR(100),
  "has_mobility_difficulty" BOOLEAN,
  "has_visual_impairment" BOOLEAN,
  "registration_date" TIMESTAMP
);

INSERT INTO patient (
  full_name,
  cpf,
  email,
  password,
  birth_date,
  gender,
  address,
  phone,
  education_level,
  has_mobility_difficulty,
  has_visual_impairment,
  registration_date
) VALUES
  (
    '<PERSON>',
    '123.456.789-00',
    '<EMAIL>',
    'senha123',
    '1985-07-12',
    'Ma<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON>, 123 - São Paulo, SP',
    '(11) 91234-5678',
    '<PERSON><PERSON><PERSON>',
    false,
    false,
    NOW()
  ),
  (
    '<PERSON>',
    '987.654.321-00',
    'maria.olive<PERSON>@email.com',
    'senha456',
    '1990-03-25',
    '<PERSON><PERSON><PERSON>',
    'Av. Brasil, 456 - Rio de Janeiro, <PERSON>J',
    '(21) 99876-5432',
    'Ensin<PERSON> Superior Co<PERSON><PERSON>',
    true,
    false,
    NOW()
  ),
  (
    '<PERSON> Souza',
    '456.789.123-00',
    'car<PERSON>.<EMAIL>',
    'senha789',
    '1978-11-02',
    'Mas<PERSON><PERSON>',
    '<PERSON><PERSON> A<PERSON><PERSON> Pena, 789 - Bel<PERSON> <PERSON><PERSON>, <PERSON>',
    '(31) 98888-0000',
    'Ensino Fundamental Incompleto',
    false,
    true,
    NOW()
  ),
  (
    'Ana Paula Mendes',
    '789.123.456-00',
    '<EMAIL>',
    'senha012',
    '2001-05-19',
    'Feminino',
    'Rua dos Lírios, 321 - Curitiba, PR',
    '(41) 98765-4321',
    'Ensino Médio Incompleto',
    false,
    false,
    NOW()
  ),
  (
    'Pedro Henrique Lima',
    '321.654.987-00',
    '<EMAIL>',
    'senha345',
    '1965-09-10',
    'Masculino',
    'Av. das Nações, 1010 - Brasília, DF',
    '(61) 91234-0000',
    'Ensino Superior Incompleto',
    true,
    true,
    NOW()
  );