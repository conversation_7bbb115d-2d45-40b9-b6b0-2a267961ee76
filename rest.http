### Base URL
@baseUrl = http://localhost:3000/api


### ===================== Agentes de Saúde =====================

### Listar todos os agentes de saúde
GET {{baseUrl}}/health-agents
Content-Type: application/json

### Buscar agente por ID
GET {{baseUrl}}/health-agents/98
Content-Type: application/json


### ===================== Materiais Educacionais =====================

### Listar todos os materiais educacionais
GET {{baseUrl}}/materials
Content-Type: application/json

### Buscar material educacional por ID
GET {{baseUrl}}/materials/5
Content-Type: application/json


### ===================== Emergências =====================

### Listar todas as emergências
GET {{baseUrl}}/emergencys
Content-Type: application/json

### Buscar emergência por ID
GET {{baseUrl}}/emergencys/2
Content-Type: application/json


### ===================== UBS =====================

### Listar todas as UBS
GET {{baseUrl}}/health-unit
Content-Type: application/json

### Buscar UBS por ID
GET {{baseUrl}}/health-unit/2
Content-Type: application/json


### ===================== Pacientes =====================

### Listar todos os pacientes
GET {{baseUrl}}/patients
Content-Type: application/json

### Buscar paciente por ID
GET {{baseUrl}}/patients/7
Content-Type: application/json

### Criar um novo paciente
POST {{baseUrl}}/patients
Content-Type: application/json

{
  "nome_completo": "Raiane Leonor De Castro",
  "data_nascimento": "2001-01-01",
  "sexo": "Feminino",
  "endereco": "Rua das Flores, 123",
  "telefone": "11999999999",
  "nivel_escolaridade": "Ensino Médio",
  "tem_dificuldade_locomocao": false,
  "tem_dificuldade_visual": false,
  "data_cadastro": "2025-05-28"
}

### Atualizar paciente existente
PUT {{baseUrl}}/patients/129
Content-Type: application/json

{
  "nome_completo": "João da Silva",
  "data_nascimento": "1990-01-01",
  "sexo": "Masculino",
  "endereco": "Rua das Flores, 123",
  "telefone": "11999999999",
  "nivel_escolaridade": "Ensino Médio",
  "tem_dificuldade_locomocao": false,
  "tem_dificuldade_visual": false,
  "data_cadastro": "2021-01-01"
}

### Deletar paciente existente
DELETE {{baseUrl}}/patients/113
Content-Type: application/json


### ===================== Registro de Fotos =====================

### Listar todos os registros de fotos
GET {{baseUrl}}/photo-records
Content-Type: application/json

### Buscar registro de foto por ID
GET {{baseUrl}}/photo-records/8
Content-Type: application/json

### Criar novo registro de foto
POST {{baseUrl}}/photo-records
Content-Type: application/json

{
  "data_foto": "2025-05-28",
  "caminho_arquivo": "https://www.google.com",
  "tamanho_ferida_mm": 7,
  "enviado_por_paciente": "1",
  "validado_por_agente": "1",
  "ferida_id": "1",
  "agente_validador_id": "1"
}

### Atualizar registro de foto
PUT {{baseUrl}}/photo-records/102
Content-Type: application/json

{
  "data_foto": "2025-05-28",
  "caminho_arquivo": "https://www.google.com",
  "tamanho_ferida_mm": 10,
  "enviado_por_paciente": "1",
  "validado_por_agente": "1",
  "ferida_id": "1",
  "agente_validador_id": "1"
}

### Deletar registro de foto
DELETE {{baseUrl}}/photo-records/4
Content-Type: application/json


### ===================== Feridas =====================

### Listar todas as feridas
GET {{baseUrl}}/wounds
Content-Type: application/json

### Buscar ferida por ID
GET {{baseUrl}}/wounds/97
Content-Type: application/json

### Criar nova ferida
POST {{baseUrl}}/wounds
Content-Type: application/json

{
  "tipo_ferida": "Úlcera de pé diabético",
  "localizacao_anatomica": "Rua das Flores, 123",
  "data_primeira_ocorrencia": "2021-01-01",
  "nivel_gravidade": "1",
  "status": "ativa",
  "paciente_id": "1",
  "data_cadastro": "2021-01-01"
}

### Atualizar ferida existente
PUT {{baseUrl}}/wounds/99
Content-Type: application/json

{
  "tipo_ferida": "Úlcera de mão diabético",
  "localizacao_anatomica": "Rua das Flores, 123",
  "data_primeira_ocorrencia": "2021-01-01",
  "nivel_gravidade": "4",
  "status": "ativa",
  "paciente_id": "1",
  "data_cadastro": "2021-01-01"
}

### Deletar ferida
DELETE {{baseUrl}}/wounds/99
Content-Type: application/json


### ===================== Comorbidades =====================

### Listar todas as comorbidades
GET {{baseUrl}}/comorbidity
Content-Type: application/json

### Buscar comorbidade por ID
GET {{baseUrl}}/comorbidity/2
Content-Type: application/json

### Criar nova comorbidade
POST {{baseUrl}}/comorbidity
Content-Type: application/json

{
  "tipo_comorbidade": "Hipertensão Arterial",
  "data_diagnostico": "2022-04-10",
  "em_tratamento": true,
  "observacoes": "Paciente faz uso de losartana.",
  "paciente_id": 1
}

### Atualizar comorbidade
PUT {{baseUrl}}/comorbidity/5
Content-Type: application/json

{
  "tipo_comorbidade": "Hipertensão Controlada",
  "data_diagnostico": "2022-04-10",
  "em_tratamento": false,
  "observacoes": "Pressão estável sem medicação.",
  "paciente_id": 1
}


### ===================== Questionário dos Pacientes =====================

### Listar todos os questionários
GET {{baseUrl}}/patient-questionnaire
Content-Type: application/json

### Buscar questionário por ID
GET {{baseUrl}}/patient-questionnaire/1
Content-Type: application/json

### Criar questionário
POST {{baseUrl}}/patient-questionnaire
Content-Type: application/json

{
  "data_preenchimento": "2025-05-29T14:30:00",
  "sentindo_dor": 5,
  "presenca_secrecao": true,
  "odor": "forte",
  "febre": false,
  "vermelhidao_ao_redor": true,
  "inchaco": false,
  "outros_sintomas": "Sensação de queimação",
  "fez_curativo_recomendado": true,
  "dificuldades_tratamento": "Falta de materiais em casa",
  "paciente_id": 3
}

### Atualizar questionário
PUT {{baseUrl}}/patient-questionnaire/1
Content-Type: application/json

{
  "data_preenchimento": "2025-05-29T14:30:00",
  "sentindo_dor": 5,
  "presenca_secrecao": false,
  "odor": "leve",
  "febre": false,
  "vermelhidao_ao_redor": false,
  "inchaco": true,
  "outros_sintomas": "Formigamento",
  "fez_curativo_recomendado": false,
  "dificuldades_tratamento": "Esqueceu o horário do curativo",
  "paciente_id": 3
}


### ===================== Acompanhamento de Feridas =====================

### Listar todos os acompanhamentos
GET {{baseUrl}}/wound-monitoring
Content-Type: application/json

### Buscar acompanhamento por ID
GET {{baseUrl}}/wound-monitoring/1
Content-Type: application/json

### Criar novo acompanhamento
POST {{baseUrl}}/wound-monitoring
Content-Type: application/json

{
  "data_avaliacao": "2025-05-29T14:30:00",
  "observacoes_agente": "Acompanhamento de ferida",
  "tratamento_recomendado": "Curativo",
  "frequencia_monitoramento": 1,
  "proxima_avaliacao_data": "2025-06-05",
  "ferida_id": 1,
  "agente_id": 1
}

### Atualizar acompanhamento existente
PUT {{baseUrl}}/wound-monitoring/3
Content-Type: application/json

{
  "data_avaliacao": "2025-05-29T14:30:00",
  "observacoes_agente": "Acompanhamento de ferida",
  "tratamento_recomendado": "Curativo",
  "frequencia_monitoramento": 1,
  "proxima_avaliacao_data": "2025-06-05",
  "ferida_id": 1,
  "agente_id": 1
}

### Deletar acompanhamento
DELETE {{baseUrl}}/wound-monitoring/1
Content-Type: application/json


### ===================== Notificações do Sistema =====================

### Listar todas as notificações
GET {{baseUrl}}/notifications
Content-Type: application/json

### Buscar notificação por ID
GET {{baseUrl}}/notifications/1
Content-Type: application/json

### Criar nova notificação
POST {{baseUrl}}/notifications
Content-Type: application/json

{
  "tipo_notificacao": "Alerta de ferida",
  "data_notificacao": "2025-05-29T14:30:00",
  "status": "pendente",
  "paciente_id": 1
}

### Atualizar notificação existente
PUT {{baseUrl}}/notifications/1
Content-Type: application/json

{
  "tipo_notificacao": "Alerta de ferida",
  "data_notificacao": "2025-05-29T14:30:00",
  "status": "resolvido",
  "paciente_id": 1
}

### Deletar notificação
DELETE {{baseUrl}}/notifications/1
Content-Type: application/json
