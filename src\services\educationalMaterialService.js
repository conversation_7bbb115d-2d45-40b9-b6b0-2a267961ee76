const EducationalMaterialModel = require('../models/educationalMaterialModel');

class EducationalMaterialService {
  constructor(repository) {
        this.repository = repository;
  }

  async findAll() {
    return await this.repository.findAll();
  }

  async findById(id) {
    const data = await this.repository.findById(id);
    if (!data) throw new Error('Material não encontrado'); 
    return data;
  }

}

module.exports = EducationalMaterialService;