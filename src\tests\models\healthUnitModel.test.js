const HealthUnitModel = require('../../models/healthUnitModel'); 

describe('HealthUnitModel Schema', () => {
  const validData = {
    unit_name: 'UBS Vila Nova',
    address: 'Rua das Flores, 123',
    phone: '11987654321',
    coverage_area: 'Zona Leste',
    opening_hours: 'Seg a Sex, das 8h às 17h'
  };

  it('deve validar dados corretos com sucesso', () => {
    const { error } = HealthUnitModel.schema.validate(validData);
    expect(error).toBeUndefined();
  });

  it('deve aceitar address e coverage_area vazios', () => {
    const data = { ...validData, address: '', coverage_area: '' };
    const { error } = HealthUnitModel.schema.validate(data);
    expect(error).toBeUndefined();
  });

  it('deve falhar se unit_name estiver ausente', () => {
    const invalidData = { ...validData };
    delete invalidData.unit_name;

    const { error } = HealthUnitModel.schema.validate(invalidData);
    expect(error).toBeDefined();
    expect(error.details[0].path).toContain('unit_name');
  });

  it('deve falhar se phone não tiver apenas números', () => {
    const invalidData = { ...validData, phone: '11-98765-4321' };

    const { error } = HealthUnitModel.schema.validate(invalidData);
    expect(error).toBeDefined();
    expect(error.details[0].path).toContain('phone');
  });

  it('deve falhar se phone tiver mais de 13 dígitos', () => {
    const invalidData = { ...validData, phone: '12345678901234' };

    const { error } = HealthUnitModel.schema.validate(invalidData);
    expect(error).toBeDefined();
    expect(error.details[0].path).toContain('phone');
  });

  it('deve falhar se opening_hours exceder 100 caracteres', () => {
    const invalidData = { ...validData, opening_hours: 'A'.repeat(101) };

    const { error } = HealthUnitModel.schema.validate(invalidData);
    expect(error).toBeDefined();
    expect(error.details[0].path).toContain('opening_hours');
  });
});