const PatientModel = require('../../models/patientModel');

describe('PatientModel Schema', () => {
  const validData = {
    full_name: '<PERSON><PERSON>',
    cpf: '12345678901',
    email: '<EMAIL>',
    password: 'senhaForte123',
    birth_date: new Date('1990-08-15'),
    gender: '<PERSON><PERSON><PERSON>',
    address: 'Rua Exemplo, 100',
    phone: '11987654321',
    education_level: 'Ensino Médio Completo',
    has_mobility_difficulty: false,
    has_visual_impairment: true,
    registration_date: new Date()
  };

  it('deve validar dados corretos com sucesso', () => {
    const { error } = PatientModel.schema.validate(validData);
    expect(error).toBeUndefined();
  });

  it('deve falhar se cpf tiver menos de 11 dígitos', () => {
    const invalidData = { ...validData, cpf: '12345' };

    const { error } = PatientModel.schema.validate(invalidData);
    expect(error).toBeDefined();
    expect(error.details[0].path).toContain('cpf');
  });

  it('deve falhar se cpf tiver letras', () => {
    const invalidData = { ...validData, cpf: 'abc45678901' };

    const { error } = PatientModel.schema.validate(invalidData);
    expect(error).toBeDefined();
    expect(error.details[0].path).toContain('cpf');
  });

  it('deve falhar se email for inválido', () => {
    const invalidData = { ...validData, email: 'joanaemail.com' };

    const { error } = PatientModel.schema.validate(invalidData);
    expect(error).toBeDefined();
    expect(error.details[0].path).toContain('email');
  });

  it('deve falhar se phone não for numérico', () => {
    const invalidData = { ...validData, phone: '11-98765-4321' };

    const { error } = PatientModel.schema.validate(invalidData);
    expect(error).toBeDefined();
    expect(error.details[0].path).toContain('phone');
  });

  it('deve falhar se gender for inválido', () => {
    const invalidData = { ...validData, gender: 'Não-binário' };

    const { error } = PatientModel.schema.validate(invalidData);
    expect(error).toBeDefined();
    expect(error.details[0].path).toContain('gender');
  });

  it('deve falhar se education_level for inválido', () => {
    const invalidData = { ...validData, education_level: 'Formado' };

    const { error } = PatientModel.schema.validate(invalidData);
    expect(error).toBeDefined();
    expect(error.details[0].path).toContain('education_level');
  });

  it('deve aceitar address vazio', () => {
    const data = { ...validData, address: '' };

    const { error } = PatientModel.schema.validate(data);
    expect(error).toBeUndefined();
  });

  it('deve falhar se has_mobility_difficulty não for booleano', () => {
    const invalidData = { ...validData, has_mobility_difficulty: 'sim' };

    const { error } = PatientModel.schema.validate(invalidData);
    expect(error).toBeDefined();
    expect(error.details[0].path).toContain('has_mobility_difficulty');
  });

  it('deve falhar se registration_date não for uma data', () => {
    const invalidData = { ...validData, registration_date: 'hoje' };

    const { error } = PatientModel.schema.validate(invalidData);
    expect(error).toBeDefined();
    expect(error.details[0].path).toContain('registration_date');
  });
});