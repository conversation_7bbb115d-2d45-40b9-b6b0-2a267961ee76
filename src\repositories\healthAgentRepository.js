const db = require("../config/db");

class HealthAgentRepository {
  async findAll() {
    const result = await db.query(
      "SELECT id, full_name, type as agent_type, agent_registration, phone, email, cpf, health_unit_id FROM health_agent"
    );
    return result.rows;
  }

  async findById(id) {
    const query =
      "SELECT id, full_name, type as agent_type, agent_registration, phone, email, cpf, health_unit_id FROM health_agent WHERE id = $1";
    const result = await db.query(query, [id]);
    if (result.rows.length === 0) {
      throw new Error("Agente de saúde não encontrado");
    }
    return result.rows[0];
  }

  async findByCpf(cpf) {
    try {
      const cleanCpf = cpf.replace(/[^\d]/g, "");
      const query = `
        SELECT id, full_name, type as agent_type, agent_registration, 
               phone, email, health_unit_id, password, cpf
        FROM health_agent 
        WHERE cpf = $1 OR cpf = $2 OR cpf = $3
      `;

      const formattedCpf = `${cleanCpf.slice(0, 3)}.${cleanCpf.slice(
        3,
        6
      )}.${cleanCpf.slice(6, 9)}-${cleanCpf.slice(9)}`;

      const result = await db.query(query, [cpf, cleanCpf, formattedCpf]);
      return result.rows[0];

    } catch (error) {
      console.error(
        "[HealthAgentRepository] Erro ao buscar por CPF:",
        error.message
      );
      throw error;
    }
  }

  async findByEmail(email) {
    try {
      const query = `
        SELECT id, full_name, type as agent_type, agent_registration, 
               phone, email, health_unit_id, password, cpf
        FROM health_agent 
        WHERE email = $1
      `;

      const result = await db.query(query, [email]);
      return result.rows[0];

    } catch (error) {
      console.error(
        "[HealthAgentRepository] Erro ao buscar por email:",
        error.message
      );
      throw error;
    }
  }
}

module.exports = HealthAgentRepository;