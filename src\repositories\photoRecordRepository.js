const db = require("../config/db");

class PhotoRecordRepository {
  async findAll() {
    const result = await db.query(`
      SELECT 
        pr.id, pr.photo_date, pr.file_path, pr.wound_size_mm,
        pr.sent_by_patient, pr.validated_by_agent, pr.wound_id, pr.health_agent_id,
        w.patient_id
      FROM photo_record pr
      LEFT JOIN wound w ON pr.wound_id = w.id
    `);
    return result.rows;
  }

  async findById(id) {
    const result = await db.query(
      `
      SELECT 
        pr.id, pr.photo_date, pr.file_path, pr.wound_size_mm,
        pr.sent_by_patient, pr.validated_by_agent, pr.wound_id, pr.health_agent_id,
        w.patient_id
      FROM photo_record pr
      LEFT JOIN wound w ON pr.wound_id = w.id
      WHERE pr.id = $1
    `,
      [id]
    );
    if (result.rows.length === 0) {
      throw new Error("Registro de foto não encontrado");
    }
    return result.rows[0];
  }

  async create(photoRecord) {
    // checar somente undefined ou null
    if (
      photoRecord.photo_date == null ||
      photoRecord.file_path == null ||
      photoRecord.wound_size_mm == null ||
      photoRecord.sent_by_patient == null ||
      photoRecord.validated_by_agent == null ||
      photoRecord.wound_id == null
    ) {
      throw new Error(
        "Campos obrigatórios não preenchidos: photo_date, file_path, wound_size_mm, sent_by_patient, validated_by_agent e wound_id são obrigatórios"
      );
    }

    const query = `
          INSERT INTO photo_record
            (photo_date, file_path, wound_size_mm, sent_by_patient, validated_by_agent, wound_id, health_agent_id)
          VALUES ($1, $2, $3, $4, $5, $6, $7)
          RETURNING *
        `;
    const result = await db.query(query, [
      photoRecord.photo_date,
      photoRecord.file_path,
      photoRecord.wound_size_mm,
      photoRecord.sent_by_patient,
      photoRecord.validated_by_agent,
      photoRecord.wound_id,
      photoRecord.health_agent_id,
    ]);

    return result.rows[0];
  }

  async update(id, photoRecord) {
    if (
      !photoRecord.photo_date ||
      !photoRecord.file_path ||
      !photoRecord.wound_size_mm ||
      photoRecord.sent_by_patient === undefined ||
      photoRecord.validated_by_agent === undefined ||
      !photoRecord.wound_id
    ) {
      throw new Error(
        "Campos obrigatórios não preenchidos: photo_date, file_path, wound_size_mm, sent_by_patient, validated_by_agent e wound_id são obrigatórios"
      );
    }

    const query = `
          UPDATE photo_record
          SET photo_date=$1, file_path=$2, wound_size_mm=$3, sent_by_patient=$4,
              validated_by_agent=$5, wound_id=$6, health_agent_id=$7
          WHERE id=$8
          RETURNING *
        `;
    const result = await db.query(query, [
      photoRecord.photo_date,
      photoRecord.file_path,
      photoRecord.wound_size_mm,
      photoRecord.sent_by_patient,
      photoRecord.validated_by_agent,
      photoRecord.wound_id,
      photoRecord.health_agent_id,
      id,
    ]);

    if (result.rowCount === 0) {
      throw new Error("Registro de foto não encontrado");
    }
    return result.rows[0];
  }

  async delete(id) {
    const query = "DELETE FROM photo_record WHERE id = $1";
    const result = await db.query(query, [id]);

    if (result.rowCount === 0) {
      throw new Error("Registro de foto não encontrado");
    }
    return result.rows[0];
  }
}

module.exports = PhotoRecordRepository;
