const db = require('../config/db');

class HealthUnitRepository {
    async findAll() {
        const result = await db.query('SELECT id, unit_name, address, phone, coverage_area, opening_hours FROM health_unit');
        return result.rows;
    }
    
    async findById(id) {
        const query = 'SELECT id, unit_name, address, phone, coverage_area, opening_hours FROM health_unit WHERE id = $1';
        const result = await db.query(query, [id]);
        if (result.rows.length === 0) {
            throw new Error('UBS não encontrada');
        }
        return result.rows;
    }

}

module.exports = HealthUnitRepository;