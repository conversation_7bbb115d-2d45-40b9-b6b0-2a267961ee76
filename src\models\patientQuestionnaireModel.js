const Joi = require("joi");

class PatientQuestionnaireModel {
  static get schema() {
    return Joi.object({
      completion_date: Joi.date().required(),
      pain_level: Joi.number().integer().valid(1, 2, 3, 4, 5).required(),
      has_discharge: Joi.boolean().required(),
      odor: Joi.string().valid('nenhum', 'leve', 'forte', 'muito forte').required(),
      has_fever: Joi.boolean().required(),
      surrounding_redness: Joi.boolean().required(),
      swelling: Joi.boolean().required(),
      other_symptoms: Joi.string().allow('').required(),
      applied_recommended_dressing: Joi.boolean().required(),
      treatment_difficulties: Joi.string().allow('').required(),
      patient_id: Joi.number().integer().required()
    });
  }
}

module.exports = PatientQuestionnaireModel;
