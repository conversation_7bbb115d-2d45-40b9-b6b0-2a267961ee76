const bcrypt = require("bcrypt");
const HealthAgentModel = require("../models/healthAgentModel");

class HealthAgentService {
  constructor(repository) {
    this.repository = repository;
  }

  async findAll() {
    return await this.repository.findAll();
  }

  async findById(id) {
    return await this.repository.findById(id);
  }

  async authenticate(identifier, password) {
    try {
      console.log(
        `[HealthAgentService] Autenticando agente com identificador: ${identifier}`
      );

      let agent;

      if (identifier.includes("@")) {
        agent = await this.repository.findByEmail(identifier);
        console.log(`[HealthAgentService] Buscando por email: ${identifier}`);
      } else {
        agent = await this.repository.findByCpf(identifier);
        console.log(`[HealthAgentService] Buscando por CPF: ${identifier}`);
      }
      
      if (!agent) {
        console.log(
          "[HealthAgentService] Agente não encontrado com o identificador fornecido"
        );
        return null;
      }

      if (password === agent.password) {
        console.log("[HealthAgentService] Autenticação bem-sucedida");
        return agent;
      }

      console.log("[HealthAgentService] Senha inválida");
      return null;
    } catch (error) {
      console.error(
        "[HealthAgentService] Erro de autenticação:",
        error.message
      );
      throw error;
    }
  }
}

module.exports = HealthAgentService;
