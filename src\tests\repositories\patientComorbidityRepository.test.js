const PatientComorbidityRepository = require('../../repositories/patientComorbidityRepository');
const db = require('../../config/db');

jest.mock('../../config/db');

describe('PatientComorbidityRepository', () => {
  let repository;

  const patientComorbidityMock = [
    {
      id: 1,
      comorbidity_type: 'Diabetes',
      diagnosis_date: '2015-06-15',
      under_treatment: true,
      observations: 'Paciente controla com medicação oral.',
      patient_id: 1
    },
    {
      id: 2,
      comorbidity_type: 'Hipertensão',
      diagnosis_date: '2018-11-02',
      under_treatment: true,
      observations: 'Controle feito com dieta e remédios.',
      patient_id: 2
    }
  ];

  beforeEach(() => {
    repository = new PatientComorbidityRepository();
    jest.clearAllMocks();
  });

  it('deve retornar todas as comorbidades (findAll)', async () => {
    db.query.mockResolvedValue({ rows: patientComorbidityMock });

    const result = await repository.findAll();

    expect(db.query).toHaveBeenCalledWith(
      'SELECT id, comorbidity_type, diagnosis_date, under_treatment, observations, patient_id FROM patient_comorbidity'
    );
    expect(result).toEqual(patientComorbidityMock);
  });

  it('deve retornar uma comorbidade por ID (findById)', async () => {
    db.query.mockResolvedValue({ rows: [patientComorbidityMock[0]] });

    const result = await repository.findById(1);

    expect(db.query).toHaveBeenCalledWith(
      'SELECT id, comorbidity_type, diagnosis_date, under_treatment, observations, patient_id FROM patient_comorbidity WHERE id = $1',
      [1]
    );
    expect(result).toEqual(patientComorbidityMock[0]);
  });

  it('deve lançar erro se a comorbidade não for encontrada (findById)', async () => {
    db.query.mockResolvedValue({ rows: [] });

    await expect(repository.findById(99)).rejects.toThrow('Comorbidade não encontrada');

    expect(db.query).toHaveBeenCalledWith(
      'SELECT id, comorbidity_type, diagnosis_date, under_treatment, observations, patient_id FROM patient_comorbidity WHERE id = $1',
      [99]
    );
  });

  it('deve criar uma nova comorbidade (create)', async () => {
    const dataToCreate = { ...patientComorbidityMock[0] };
    delete dataToCreate.id;

    db.query.mockResolvedValue({ rows: [dataToCreate] });

    const result = await repository.create(dataToCreate);

    expect(db.query).toHaveBeenCalledWith(
        'INSERT INTO patient_comorbidity (comorbidity_type, diagnosis_date, under_treatment, observations, patient_id) VALUES ($1, $2, $3, $4, $5) RETURNING *',
        [dataToCreate.comorbidity_type, dataToCreate.diagnosis_date, dataToCreate.under_treatment, dataToCreate.observations, dataToCreate.patient_id]
    );
    expect(result).toEqual(dataToCreate);
  });

  it('deve lançar erro ao tentar criar comorbidade com dados inválidos (create)', async () => {
    const invalidData = {
      diagnosis_date: '2022-05-10',
      under_treatment: true,
      observations: 'Sem detalhes',
      patient_id: 1
    };

    await expect(repository.create(invalidData)).rejects.toThrow('Campos obrigatórios não preenchidos');
  });

  it('deve atualizar uma comorbidade existente (update)', async () => {
    const updatedData = { ...patientComorbidityMock[0], comorbidity_type: 'Hipertensão' };

    db.query.mockResolvedValue({ rows: [updatedData] });

    const result = await repository.update(1, updatedData);

    expect(db.query).toHaveBeenCalledWith(
        'UPDATE patient_comorbidity SET comorbidity_type = $1, diagnosis_date = $2, under_treatment = $3, observations = $4, patient_id = $5 WHERE id = $6 RETURNING *',
        [updatedData.comorbidity_type, updatedData.diagnosis_date, updatedData.under_treatment, updatedData.observations, updatedData.patient_id, 1]
    );
    expect(result).toEqual(updatedData);
  });

  it('deve lançar erro se a comorbidade não for encontrada (update)', async () => {
    db.query.mockResolvedValue({ rowCount: 0, rows: [] });

    await expect(repository.update(99, patientComorbidityMock[0])).rejects.toThrow('Comorbidade não encontrada');
  });

  it('deve retornar comorbidades de um paciente (findByPatient)', async () => {
    db.query.mockResolvedValue({ rows: [patientComorbidityMock[0]] });

    const result = await repository.findByPatient(1);

    expect(db.query).toHaveBeenCalledWith(
        'SELECT id, comorbidity_type, diagnosis_date, under_treatment, observations, patient_id FROM patient_comorbidity WHERE patient_id = $1',
        [1]
    );
    expect(result).toEqual([patientComorbidityMock[0]]);
  });

  it('deve retornar comorbidades de um paciente por tipo (findByPatientAndType)', async () => {
    db.query.mockResolvedValue({ rows: [patientComorbidityMock[0]] });

    const result = await repository.findByPatientAndType(1, 'diabetes');

    expect(db.query).toHaveBeenCalledWith(
        'SELECT id, comorbidity_type, diagnosis_date, under_treatment, observations, patient_id FROM patient_comorbidity WHERE patient_id = $1 AND LOWER(comorbidity_type) = LOWER($2)',
        [1, 'diabetes']
    );
    expect(result).toEqual([patientComorbidityMock[0]]);
  });
});