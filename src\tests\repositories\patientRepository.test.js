const PatientRepository = require('../../repositories/patientRepository');
const db = require('../../config/db');

jest.mock('../../config/db');

describe('PatientRepository', () => {
  let repository;

  const patientMock = {
    id: 1,
    full_name: '<PERSON>',
    cpf: '12345678901',
    email: '<EMAIL>',
    password: 'senha123',
    birth_date: '1990-01-01',
    gender: '<PERSON><PERSON><PERSON>',
    address: 'Rua das Flores, 123',
    phone: '11999999999',
    education_level: '<PERSON><PERSON><PERSON>',
    has_mobility_difficulty: false,
    has_visual_impairment: false,
    registration_date: '2025-05-28'
  };

  beforeEach(() => {
    repository = new PatientRepository();
    jest.clearAllMocks();
  });

  it('deve retornar todos os pacientes (findAll)', async () => {
    db.query.mockResolvedValue({ rows: [patientMock] });

    const result = await repository.findAll();

    expect(db.query).toHaveBeenCalledWith(
        'SELECT * FROM patient ORDER BY id'
    );
    expect(result).toEqual([patientMock]);
  });

  it('deve retornar um paciente por ID (findById)', async () => {
    db.query.mockResolvedValue({ rows: [patientMock] });

    const result = await repository.findById(1);

    expect(db.query).toHaveBeenCalledWith(
        'SELECT * FROM patient WHERE id = $1',
        [1]
    );
    expect(result).toEqual(patientMock);
  });

  it('deve lançar erro se o paciente não for encontrado (findById)', async () => {
    db.query.mockResolvedValue({ rows: [] });

    await expect(repository.findById(99)).rejects.toThrow('Paciente não encontrado');

    expect(db.query).toHaveBeenCalledWith(
        'SELECT * FROM patient WHERE id = $1',
        [99]
    );
  });

  it('deve criar um novo paciente (create)', async () => {
    const dataToCreate = { ...patientMock };
    delete dataToCreate.id;

    db.query.mockResolvedValue({ rows: [dataToCreate] });

    const result = await repository.create(dataToCreate);

    expect(db.query).toHaveBeenCalledWith(
        expect.stringContaining('INSERT INTO patient'),
        expect.any(Array)
    );
    expect(result).toEqual(dataToCreate);
  });

  it('deve lançar erro se o paciente não for encontrado (create)', async () => {
    db.query.mockRejectedValue(new Error('Erro ao criar paciente'));

    await expect(repository.create(patientMock)).rejects.toThrow('Erro ao criar paciente');
  });

  it('deve atualizar um paciente existente (update)', async () => {
    const updatedData = { ...patientMock, full_name: 'Maria Silva Atualizada' };

    db.query.mockResolvedValue({ rows: [updatedData] });

    const result = await repository.update(1, updatedData);

    expect(db.query).toHaveBeenCalledWith(
        expect.stringContaining('UPDATE patient SET'),
        expect.any(Array)
    );
    expect(result).toEqual(updatedData);
  });

  it('deve lançar erro se o paciente não for encontrado (update)', async () => {
    db.query.mockResolvedValue({ rows: [] });

    await expect(repository.update(99, patientMock)).rejects.toThrow('Paciente não encontrado');
  });

  it('deve lançar erro quando findAll falha', async () => {
    db.query.mockRejectedValue(new Error('Erro de conexão'));

    await expect(repository.findAll()).rejects.toThrow('Erro ao buscar pacientes');
  });

  it('deve retornar paciente por email', async () => {
    db.query.mockResolvedValue({ rows: [patientMock] });

    const result = await repository.findByEmail('<EMAIL>');

    expect(db.query).toHaveBeenCalledWith(
        'SELECT * FROM patient WHERE email = $1',
        ['<EMAIL>']
    );
    expect(result).toEqual(patientMock);
  });

  it('deve lançar erro quando findByEmail falha', async () => {
    db.query.mockRejectedValue(new Error('Erro de conexão'));

    await expect(repository.findByEmail('<EMAIL>')).rejects.toThrow('Erro ao buscar paciente por email');
  });

  it('deve retornar paciente por CPF', async () => {
    db.query.mockResolvedValue({ rows: [patientMock] });

    const result = await repository.findByCpf('12345678901');

    expect(db.query).toHaveBeenCalledWith(
        expect.stringContaining('SELECT * FROM patient WHERE cpf'),
        expect.any(Array)
    );
    expect(result).toEqual(patientMock);
  });

  it('deve lançar erro quando findByCpf falha', async () => {
    db.query.mockRejectedValue(new Error('Erro de conexão'));

    await expect(repository.findByCpf('12345678901')).rejects.toThrow('Erro ao buscar paciente por CPF');
  });

  it('deve lançar erro específico para violação de constraint única (código 23505)', async () => {
    const duplicateError = new Error('Violação de constraint única');
    duplicateError.code = '23505';

    db.query.mockRejectedValue(duplicateError);

    await expect(repository.create(patientMock)).rejects.toThrow('Um usuário com este email ou CPF já existe');
  });

  it('deve deletar um paciente existente', async () => {
    db.query.mockResolvedValue({ rows: [patientMock] });

    const result = await repository.delete(1);

    expect(db.query).toHaveBeenCalledWith(
        'DELETE FROM patient WHERE id = $1 RETURNING *',
        [1]
    );
    expect(result).toEqual(patientMock);
  });

  it('deve lançar erro se paciente não for encontrado para deletar', async () => {
    db.query.mockResolvedValue({ rows: [] });

    await expect(repository.delete(999)).rejects.toThrow('Paciente não encontrado');
  });

  it('deve lançar erro quando delete falha', async () => {
    db.query.mockRejectedValue(new Error('Erro de conexão'));

    await expect(repository.delete(1)).rejects.toThrow('Erro de conexão');
  });
});