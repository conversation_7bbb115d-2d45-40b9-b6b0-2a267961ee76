const PatientRepository = require('../../repositories/patientRepository');
const db = require('../../config/db');

jest.mock('../../config/db');

describe('PatientRepository', () => {
  let repository;

  const patientMock = {
    id: 1,
    full_name: '<PERSON>',
    cpf: '12345678901',
    email: '<EMAIL>',
    password: 'senha123',
    birth_date: '1990-01-01',
    gender: '<PERSON><PERSON><PERSON>',
    address: 'Rua <PERSON> Flores, 123',
    phone: '11999999999',
    education_level: '<PERSON><PERSON><PERSON>',
    has_mobility_difficulty: false,
    has_visual_impairment: false,
    registration_date: '2025-05-28'
  };

  beforeEach(() => {
    repository = new PatientRepository();
    jest.clearAllMocks();
  });

  it('deve retornar todos os pacientes (findAll)', async () => {
    db.query.mockResolvedValue({ rows: [patientMock] });

    const result = await repository.findAll();

    expect(db.query).toHaveBeenCalledWith(
        'SELECT id, full_name, cpf, email, password, birth_date, gender, address, phone, education_level, has_mobility_difficulty, has_visual_impairment, registration_date FROM patient'
    );
    expect(result).toEqual([patientMock]);
  });

  it('deve retornar um paciente por ID (findById)', async () => {
    db.query.mockResolvedValue({ rows: [patientMock] });

    const result = await repository.findById(1);

    expect(db.query).toHaveBeenCalledWith(
        'SELECT id, full_name, cpf, email, password, birth_date, gender, address, phone, education_level, has_mobility_difficulty, has_visual_impairment, registration_date FROM patient WHERE id = $1',
        [1]
    );
    expect(result).toEqual(patientMock);
  });

  it('deve lançar erro se o paciente não for encontrado (findById)', async () => {
    db.query.mockResolvedValue({ rows: [] });

    await expect(repository.findById(99)).rejects.toThrow('Paciente não encontrado');

    expect(db.query).toHaveBeenCalledWith(
        'SELECT id, full_name, cpf, email, password, birth_date, gender, address, phone, education_level, has_mobility_difficulty, has_visual_impairment, registration_date FROM patient WHERE id = $1',
        [99]
    );
  });

  it('deve criar um novo paciente (create)', async () => {
    const dataToCreate = { ...patientMock };
    delete dataToCreate.id;

    db.query.mockResolvedValue({ rows: [dataToCreate] });

    const result = await repository.create(dataToCreate);

    expect(db.query).toHaveBeenCalledWith(
        'INSERT INTO patient (full_name, cpf, email, password, birth_date, gender, address, phone, education_level, has_mobility_difficulty, has_visual_impairment, registration_date) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)',
        [dataToCreate.full_name, dataToCreate.cpf, dataToCreate.email, dataToCreate.password, dataToCreate.birth_date, dataToCreate.gender, dataToCreate.address, dataToCreate.phone, dataToCreate.education_level, dataToCreate.has_mobility_difficulty, dataToCreate.has_visual_impairment, dataToCreate.registration_date]
    );
    expect(result).toEqual(dataToCreate);
  });

  it('deve lançar erro se o paciente não for encontrado (create)', async () => {
    db.query.mockResolvedValue({ rows: [] });

    await expect(repository.create(patientMock)).rejects.toThrow('Paciente não encontrado');
  });

  it('deve atualizar um paciente existente (update)', async () => {
    const updatedData = { ...patientMock, full_name: 'Maria Silva Atualizada' };

    db.query.mockResolvedValue({ rows: [updatedData] });

    const result = await repository.update(1, updatedData);

    expect(db.query).toHaveBeenCalledWith(
        'UPDATE patient SET full_name = $1, email = $2, phone = $3, address = $4 WHERE id = $5',
        [updatedData.full_name, updatedData.email, updatedData.phone, updatedData.address, 1]
    );
    expect(result).toEqual(updatedData);
  });

  it('deve lançar erro se o paciente não for encontrado (update)', async () => {
    db.query.mockResolvedValue({ rows: [] });

    await expect(repository.update(99, patientMock)).rejects.toThrow('Paciente não encontrado');
  });
});