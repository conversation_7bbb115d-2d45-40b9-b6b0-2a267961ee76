* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: "Roboto", sans-serif;
}

html,
body {
  height: 100%;
  overflow-x: hidden;
  overflow-y: auto;
}

body {
  background-color: #f5f7fa;
  color: #2d3748;
  line-height: 1.5;
  min-height: 100vh;
}

a {
  text-decoration: none;
  color: inherit;
}

/* Layout principal */
.flow-column {
  justify-content: center;
  align-items: center;
  display: flex;
}

.desktop-frame {
  width: 100%;
  height: 100vh;
  display: flex;
  background-color: white;
  overflow: hidden;
}

.desktop-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: #f8fafc;
  overflow: hidden;
}

.main-area {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

/* Conteúdo central */
.content-container {
  max-width: 400px;
  margin: 0 auto;
  text-align: center;
}

.logo {
  width: 80px;
  height: 80px;
}

.logo-small {
  width: 40px;
  height: 40px;
}

.title {
  font-size: 28px;
  margin: 10px 0;
}

.description {
  font-size: 16px;
  color: #718096;
  margin: 0 auto 24px;
  max-width: 350px;
}

.subtitle {
  margin-bottom: 30px;
}

/* Cards container */
.cards-container {
  display: flex;
  gap: 20px;
  justify-content: center;
}

.card {
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.07);
  background-color: white;
  margin-bottom: 20px;
}

.icon-circle {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background-color: #ebf4ff;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
}

.icon {
  font-size: 40px;
}

.card-title {
  font-size: 20px;
  margin-bottom: 10px;
  font-weight: 600;
}

.spacer {
  flex: 1;
}

/* Sidebar */
.sidebar {
  width: 200px;
  background-color: #013c6d;
  color: white;
  padding: 20px 0;
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.sidebar-logo {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20px;
}

.sidebar-title {
  margin-top: 8px;
}

.sidebar-menu {
  list-style: none;
  width: 100%;
  padding: 0;
}

.sidebar-item {
  padding: 12px 24px;
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: background 0.2s;
  color: #e2e8f0;
}

.sidebar-item.active,
.sidebar-item:hover {
  background: #1a4f7e;
  color: #68cfee;
}

.sidebar-icon {
  margin-right: 12px;
  font-size: 18px;
  width: 24px;
  text-align: center;
}

.sidebar-link {
  display: block;
  width: 100%;
}

/* Header */
.desktop-header {
  height: 60px;
  border-bottom: 1px solid #e2e8f0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  background-color: white;
  flex-shrink: 0;
}

.header-title {
  font-size: 18px;
  font-weight: 600;
}

/* Forms */
.form-container {
  width: 100%;
}

.input-group {
  margin-bottom: 16px;
  width: 100%;
}

.input-label {
  display: block;
  margin-bottom: 6px;
  font-size: 14px;
  color: #4a5568;
}

.input-field {
  width: 100%;
  padding: 10px 12px;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
  font-size: 14px;
  background-color: #f8fafc;
  transition: border-color 0.2s;
}

.input-field:focus {
  outline: none;
  border-color: #013c6d;
}

/* Login e registro */
.container {
  width: 100%;
  min-height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f8fafc;
  padding: 20px;
}

.login-card {
  background: #ffffff;
  border-radius: 25px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  overflow: visible;
  width: 100%;
  max-width: 400px;
}

.header {
  background: linear-gradient(135deg, #2c5282 0%, #3182ce 100%);
  padding: 20px;
  text-align: center;
  border-radius: 25px 25px 0 0;
}

.header h1 {
  color: white;
  font-size: 24px;
  font-weight: 600;
  margin: 0;
}

.logo-section {
  padding: 40px 20px 20px;
  text-align: center;
}

.logo-placeholder {
  width: 100px;
  height: 100px;
  border: 2px solid #d1d5db;
  border-radius: 8px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  color: #6b7280;
  font-size: 16px;
  background: #f9fafb;
}

.main-title {
  text-align: center;
  font-size: 32px;
  font-weight: 700;
  color: #374151;
  margin: 20px 0 5px;
}

.login-form {
  padding: 0 30px 30px;
  background: #ffffff;
}

.form-group {
  margin-bottom: 25px;
}

.labelgroup {
  margin-bottom: 8px;
}

.form-group label {
  display: block;
  color: #374151;
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 8px;
}

.form-group input {
  width: 100%;
  padding: 16px 20px;
  border: 2px solid #e5e7eb;
  border-radius: 25px;
  font-size: 16px;
  background: #f9fafb;
  color: #374151;
  transition: all 0.3s ease;
}

.form-group input::placeholder {
  color: #9ca3af;
}

.form-group input:focus {
  outline: none;
  border-color: #3182ce;
  background: #ffffff;
  box-shadow: 0 0 0 3px rgba(49, 130, 206, 0.1);
}

.btn-login {
  width: 100%;
  background: linear-gradient(135deg, #2c5282 0%, #013c6d 100%);
  color: white;
  border: none;
  padding: 18px;
  border-radius: 25px;
  font-size: 18px;
  font-weight: 600;
  cursor: pointer;
  margin-bottom: 20px;
  transition: all 0.3s ease;
}

.btn-login:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(49, 130, 206, 0.3);
}

.btn-login:active {
  transform: translateY(0);
}

.forgot-password {
  display: block;
  text-align: center;
  color: #3182ce;
  text-decoration: none;
  font-size: 16px;
  font-weight: 500;
}

.forgot-password:hover {
  text-decoration: underline;
}

.signup {
  display: block;
  text-align: center;
  width: 100%;
  background: linear-gradient(135deg, #2c5282 0%, #013c6d 100%);
  color: white !important;
  border: none;
  padding: 18px;
  border-radius: 25px;
  font-size: 15px;
  font-weight: 600;
  cursor: pointer;
  margin-bottom: 20px;
  transition: all 0.3s ease;
  text-decoration: none !important;
}

/* Menu buttons */
.menu-buttons {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.menu-link {
  width: 100%;
}

.menu-button {
  width: 100%;
}

.emergency-button {
  margin-top: 16px;
}

.emergency-icon {
  font-size: 18px;
  margin-right: 8px;
}

/* Emergency page */
.emergency-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  flex: 1;
}

.emergency-icon-large {
  font-size: 60px;
  margin-bottom: 20px;
}

.emergency-title {
  margin-bottom: 15px;
}

.emergency-message {
  color: #718096;
  margin-bottom: 30px;
}

/* Como usar page */
.step-item {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.step-number {
  font-size: 24px;
  margin-right: 15px;
}

.step-content {
  flex: 1;
}

.step-title {
  font-weight: 600;
}

.step-description {
  font-size: 14px;
  color: #718096;
}

.video-player {
  width: 100%;
  height: 300px;
  background-color: #2d3748;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  margin-bottom: 0;
  position: relative;
}

/* Video cards */
.video-card {
  margin-bottom: 15px;
}

.video-thumbnail {
  width: 100%;
  height: 300px;
  background-color: #2d3748;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  margin-bottom: 10px;
  position: relative;
}

.play-button {
  width: 50px;
  height: 50px;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
}

.video-title {
  font-weight: 600;
  margin-bottom: 8px;
}

.video-description {
  font-size: 12px;
  text-align: justify;
  margin-bottom: 0;
  color: #718096;
}

/* Notificação */
.notification-section {
  margin-bottom: 20px;
}

.section-title {
  font-size: 16px;
  margin-bottom: 10px;
}

.notification-card {
  margin-bottom: 10px;
}

.notification-content {
  display: flex;
  align-items: flex-start;
}

.notification-icon {
  font-size: 24px;
  margin-right: 10px;
}

.notification-details {
  flex: 1;
}

.notification-title {
  font-weight: 600;
  margin-bottom: 4px;
}

.notification-message {
  font-size: 14px;
  color: #718096;
}

.notification-time {
  font-size: 12px;
  color: #a0aec0;
  margin-top: 8px;
}

/* Termo de consentimento */
.content-center {
  align-items: center;
  text-align: center;
}

.termo-content {
  max-height: 400px;
  overflow-y: auto;
  padding: 30px;
  text-align: justify;
  background-color: white;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  margin-bottom: 20px;
}

/* Cadastro form */
.cadastro-form {
  display: flex;
  flex-direction: column;
  gap: 0;
}

/* Botões */
.button {
  width: 100%;
  margin-top: 15px;
  padding: 10px;
  background-color: #013c6d;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  font-size: 14px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.button:hover {
  background-color: #1a4f7e;
}

.button.outline {
  background-color: transparent;
  color: #013c6d;
  border: 1px solid #013c6d;
}

.button.danger {
  background-color: #b85b5b;
}

/* Footer */
.footer-desktop {
  margin-top: auto;
  display: flex;
  justify-content: space-between;
  padding: 8px 20px;
  background-color: #f7fafc;
  border-top: 1px solid #e2e8f0;
  min-height: 40px;
  flex-shrink: 0;
}

.footer-left {
  color: #4a5568;
  font-size: 12px;
}

.footer-right {
  font-weight: bold;
}

/* Mobile Navigation Bar */
.mobile-navbar {
  display: none;
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  background: white;
  border-top: 1px solid #e2e8f0;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  padding: 8px 0;
}

.mobile-nav-container {
  display: flex;
  justify-content: space-around;
  align-items: center;
  max-width: 100%;
}

.mobile-nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px 12px;
  text-decoration: none;
  color: #718096;
  transition: all 0.2s ease;
  min-width: 60px;
  border-radius: 8px;
}

.mobile-nav-item:hover,
.mobile-nav-item.active {
  color: #013c6d;
  background-color: #f0f8ff;
}

.mobile-nav-icon {
  font-size: 20px;
  margin-bottom: 4px;
}

.mobile-nav-text {
  font-size: 10px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Mobile responsivo */
@media (max-width: 768px) {
  .container {
    padding: 10px;
  }

  .login-card {
    width: 100%;
    max-width: none;
    margin: 0;
  }

  .logo {
    width: 80px;
    height: 80px;
  }

  .main-title {
    font-size: 24px;
  }

  .login-form {
    padding: 0 20px 20px;
  }

  .form-group input {
    padding: 12px 16px;
    font-size: 14px;
  }

  .btn-login {
    padding: 14px;
    font-size: 16px;
  }
}

@media (max-width: 480px) {
  .desktop-frame {
    flex-direction: column;
  }

  .sidebar {
    display: none;
  }

  .main-area {
    padding: 16px;
    padding-bottom: 80px; /* Espaço para a navbar mobile */
  }

  .mobile-navbar {
    display: block;
  }

  .footer-desktop {
    display: none; /* Esconder footer desktop no mobile */
  }

  .login-card {
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  }

  .logo {
    width: 60px;
    height: 60px;
  }
}

/* Tablet */
@media (min-width: 769px) and (max-width: 1024px) {
  .container {
    padding: 20px;
  }

  .login-card {
    max-width: 500px;
  }
}

/* Desktop */
@media (min-width: 1025px) {
  .container {
    padding: 40px;
    min-height: 100vh;
  }

  .login-card {
    max-width: 450px;
  }

  .logo {
    width: 100px;
    height: 100px;
  }

  .main-title {
    font-size: 32px;
  }

  .login-form {
    padding: 0 40px 40px;
  }
}

/* Desktop grande */
@media (min-width: 1441px) {
  .login-card {
    max-width: 500px;
  }

  .form-group {
    width: 100%;
    margin: 0 auto 25px;
  }

  .labelgroup {
    text-align: left;
    margin-left: 0;
  }

  .login-form {
    padding: 0 50px 50px;
  }
}
