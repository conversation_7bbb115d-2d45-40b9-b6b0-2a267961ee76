const PhotoRecordModel = require('../models/photoRecordModel');

class PhotoRecordService {
  constructor(repository) {
        this.repository = repository;
  }

  async findAll() {
    return await this.repository.findAll();
  }

  async findById(id) {
    return await this.repository.findById(id);
  }

  async create(photoRecord) {

    return await this.repository.create(photoRecord);
  }

  async update(id, photoRecord) {

    return await this.repository.update(id, photoRecord);
  }

  async delete(id) {
    return await this.repository.delete(id);
  }

}

module.exports = PhotoRecordService;