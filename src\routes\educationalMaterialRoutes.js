const express = require('express');
const router = express.Router();

const EducationalMaterialRepository = require('../repositories/educationalMaterialRepository');
const educationalMaterialService = require('../services/educationalMaterialService');
const EducationalMaterialController = require('../controllers/educationalMaterialController');

const controller = new EducationalMaterialController(new educationalMaterialService(new EducationalMaterialRepository()));
router.get('/', controller.index.bind(controller));
router.get('/:id', controller.show.bind(controller));

module.exports = router;
