<img src="../assets/logointeli.png">

# WAD - Web Application Document - Módulo 2 - Inteli

## Nome do Grupo

#### Integrantes do grupo

- <a href="https://www.linkedin.com/in/george-ka<PERSON><PERSON>-b54a6a297/"><PERSON></a>
- <a href="https://www.linkedin.com/in/kaian-moura-56b8871b4/"><PERSON><PERSON></a>
- <a href="https://www.linkedin.com/in/leonardo-lameda/"><PERSON></a>
- <a href="https://www.linkedin.com/in/raianearaujobrandao/"><PERSON><PERSON></a>
- <a href="https://www.linkedin.com/in/rapha<PERSON>-fischer-skitne<PERSON>-198b77345/"><PERSON></a>
- <a href="https://www.linkedin.com/in/stefanne-soares-9b31a8256/">Stefanne Victória Andrade Soares</a>
- <a href="https://www.linkedin.com/in/tobias-viana/"><PERSON></a>

## Sum<PERSON>rio

[1. Introdução](#c1)

[2. Visão Geral da Aplicação Web](#c2)

[3. Projeto Técnico da Aplicação Web](#c3)

[4. Desenvolvimento da Aplicação Web](#c4)

[5. Testes da Aplicação Web](#c5)

[6. Estudo de Mercado e Plano de Marketing](#c6)

[7. Conclusões e trabalhos futuros](#c7)

[8. Referências](#c8)

[Anexos](#c9)

<br>

# <a name="c1"></a>1. Introdução

A seção 1 tem como objetivo contextualizar a problemática apresentada pelo parceiro de projeto e apresentar, de forma breve e objetiva, a solução desenvolvida.

Reconhecida pelas contribuições científicas, a Faculdade de Medicina da Universidade de São Paulo (FMUSP) se destaca especialmente em áreas como cirurgia plástica e reconstrutiva no âmbito das feridas e lesões. Estima-se que 85% das amputações em membros inferiores estejam relacionadas a feridas em pacientes com diabetes, evidenciando a importância de investir em ferramentas que apoiem os profissionais no cuidado com a pele, contribuindo para a prevenção e tratamento dos pacientes (Gonçalves, 2025).

A pele, maior órgão do corpo humano, exerce funções vitais para a nossa sobrevivência, contudo, agressões e/ou estresse podem desencadear lesões celulares, que podem ser reversíveis ou irreversíveis. Quando essas lesões comprometem a integridade do tecido cutâneo, dá-se origem às feridas. A presença de comorbidades pode agravar essas condições. Diante de uma ferida, o impacto é físico, psicológico e social, exigindo uma abordagem clínica criteriosa e humanizada, considerando tanto a avaliação da lesão quanto às condições gerais de saúde do paciente.

Todavia, o tratamento necessário muitas vezes não é ofertado devido à alta demanda dos hospitais e Unidades Básicas de Saúde (UBS). Pensando nisso, o presente projeto propõe uma abordagem tecnológica e humanizada para pacientes com feridas, promovendo um acompanhamento mais eficaz, especialmente entre aqueles com dificuldade de locomoção ou baixa adesão.

O projeto, desenvolvido em parceria com a FMUSP, consiste em uma plataforma digital composta por um site responsivo que oferece interface tanto para pacientes quanto para profissionais da saúde. Os pacientes podem cadastrar suas informações, enviar fotos da ferida, acessar conteúdos educativos (como vídeos e textos explicativos sobre curativos, sinais de alerta e higiene) e receber notificações de acompanhamento. Os profissionais, por sua vez, acessam prontuários digitais com evolução das feridas, recebem alertas de emergência e podem responder com orientações personalizadas.O site foi desenvolvido com foco em acessibilidade, usabilidade e empoderamento do paciente.

# <a name="c2"></a>2. Visão Geral da Aplicação Web

A seção 2 tem como objetivo apresentar uma visão ampla do projeto desenvolvido, contextualizando suas funcionalidades principais e a estrutura geral da solução. As subseções seguintes trarão mais detalhes sobre o escopo e os requisitos específicos trabalhados nas diferentes sprints.

## 2.1. Escopo do Projeto (sprints 1 e 4)

Esta subseção (2.1) descreve os limites, objetivos e funcionalidades previstas para a aplicação web.

### 2.1.1. Modelo de 5 Forças de Porter

Esta subseção (2.1.1) realiza uma análise setorial da Faculdade de Medicina da Universidade de São Paulo (FMUSP), mais especificamente do Departamento de Cirurgia Plástica, dentro da subárea de microcirurgia, com foco na unidade de feridas, com base nas 5 Forças de Porter. O objetivo é identificar fatores que influenciam a posição do departamento no mercado atual, contribuindo para a compreensão do ambiente competitivo e reforçando o contexto da indústria no qual a instituição está inserida.

A Faculdade de Medicina da Universidade de São Paulo (FMUSP) é uma das principais referências no ensino superior de Medicina no Brasil, que atrai investimentos públicos e privados e demanda constante inovação acadêmica e científica. Para analisar esse cenário de forma aprofundada, é essencial utilizar um modelo estratégico consolidado, como as 5 Forças de Porter.

A dinâmica do setor educacional exige que as instituições identifiquem desafios estratégicos e desenvolvam planos para se manterem competitivas. A Faculdade de Medicina da Universidade de São Paulo (FMUSP) exemplifica essa postura ao manter seu prestígio nacional, destacando-se pela excelência acadêmica e pela integração com o Hospital das Clínicas.

O modelo das 5 Forças de Porter foi desenvolvido por Michael E. Porter e publicado em seu livro Competitive Strategy (PORTER, 1980). Segundo o autor, a lucratividade de um setor é determinada por cinco forças competitivas:

- **Ameaça de novos entrantes** — Baixa

O mercado de cirurgia plástica para tratamento de queimaduras apresenta barreiras de entrada bastante elevadas. O processo de formação para se tornar um cirurgião plástico especialista em queimaduras é longo e extremamente exigente, incluindo graduação em medicina, residência em cirurgia geral, especialização em cirurgia plástica e, muitas vezes, subespecialização em queimaduras. Além disso, hospitais de referência precisam ter infraestrutura de alta complexidade, como Centros de Tratamento de Queimados (CTQs), que demandam investimentos milionários. Segundo o Ministério da Saúde (2000), para um hospital ser reconhecido como referência em queimaduras, é necessário cumprir exigências rigorosas de estrutura, equipe multidisciplinar e protocolos de atendimento.Essas barreiras fazem com que poucos novos competidores entrem no mercado, mantendo a ameaça de novos entrantes baixa.

- **Ameaça de produtos substitutivos** — Baixa

Não existem substitutos para a cirurgia plástica em queimaduras graves. Tratamentos como curativos especiais, fisioterapia e terapias regenerativas podem auxiliar no processo de recuperação, mas em casos moderados a graves, apenas a cirurgia reconstrutiva pode restaurar a função e a estética da pele. Pesquisas e diretrizes clínicas, como as publicadas pela Revista Brasileira de Cirurgia Plástica (RBCP), indicam que os enxertos de pele e os retalhos cirúrgicos são amplamente considerados as opções mais eficazes para o tratamento de queimaduras profundas. Esses métodos são essenciais para a restauração da função e da estética da pele, especialmente em lesões severas, quando a regeneração natural da pele é insuficiente (RIBEIRO et al., 2013). Com isso, novos produtos não seriam opções muito viáveis para o futuro da cirurgia plástica.

- **Poder de barganha dos fornecedores** — Alto

Hospitais e clínicas de cirurgia plástica para queimaduras dependem de insumos altamente especializados como enxertos biológicos, lasers cirúrgicos e materiais para reabilitação. Poucos fornecedores dominam essas tecnologias, o que aumenta seu poder de barganha. Empresas multinacionais como Smith & Nephew, Integra LifeSciences e 3M controlam grande parte do mercado de produtos médicos para queimaduras. A necessidade crítica desses insumos e a limitação de opções aumentam o poder dos fornecedores sobre preços e condições de compra. De acordo com Markets and Markets (2016), descreve o impacto do domínio dessas empresas multinacionais no controle dos preços e condições de compra de insumos essenciais para o tratamento de queimaduras.

- **Poder de barganha dos clientes** — Médio

Pacientes vítimas de queimaduras graves normalmente dependem dos serviços especializados disponíveis, muitas vezes via sistema público (SUS) ou convênios médicos. Embora exista uma limitação de centros especializados, pacientes com recursos financeiros podem buscar hospitais privados ou cirurgiões renomados, o que dá a eles algum grau de escolha. No entanto, como a oferta de especialistas em queimaduras é restrita, segundo uma pesquisa feita pela Sociedade Brasileira de Queimaduras (2022), o Brasil tem aproximadamente 50 Centros de Tratamento de Queimados habilitados e o poder de barganha dos clientes permanece moderado.

- **Rivalidade entre concorrentes** — Média

O setor de cirurgia plástica para queimaduras é caracterizado por uma rivalidade moderada. Existem poucos centros especializados no país e a maioria deles é regionalizada, o que limita a competição direta. A disputa entre os centros ocorre principalmente pela busca de reconhecimento como referência nacional, pela obtenção de certificações de excelência e pela capacidade de oferecer tratamentos de alta complexidade. Segundo levantamento da Sociedade Brasileira de Queimaduras (2022), a concentração de serviços em algumas regiões, como o Sudeste, faz com que hospitais busquem se diferenciar através de especializações, modernização tecnológica e atendimento humanizado. Apesar disso, como a quantidade de pacientes com queimaduras graves é relativamente constante e os centros são poucos, a competição permanece em um nível moderado e controlado.

<div align="center"> <sub> Figura 1: 5 Forças de Porter do parceiro de projeto </sub>

![5 Forças de Porter](https://res.cloudinary.com/dpks4ergy/image/upload/v1745947693/6db47747-c1af-404d-b11f-c7dc4ee7def4.png)
<sup> Fonte: Autoral, 2025</sup> </div> <br>

### 2.1.2. Análise SWOT da Instituição Parceira

Esta subseção (2.1.2) apresenta a Análise SWOT da unidade de feridas, que integra a subespecialidade de microcirurgia do Departamento de Cirurgia Plástica da Faculdade de Medicina da Universidade de São Paulo (FMUSP), parceiro de projeto. A Análise SWOT é uma ferramenta de planejamento estratégico que auxilia na avaliação de fatores internos e externos que influenciam a organização, como forças (vantagens e diferenciais internos), fraquezas (pontos de melhoria ou limitações internas), oportunidades (fatores externos favoráveis ao crescimento) e ameaças (riscos e desafios externos).

Olhando para o contexto do parceiro, foram identificados os seguintes pontos:

<div align="center"> <sub> Figura 2: Análise SOWT da Faculdade de Medicina da USP (FMUSP) </sub>

![SWOT](https://res.cloudinary.com/dtxiyeitw/image/upload/v1750788236/Navy_Modern_SWOT_Analysis_Graph_e0cegi.png)
<sup> Fonte: Autoral, 2025</sup> </div> <br>

**Forças (Strengths)**

- **Capacidade de realizar reconstruções complexas**  
  A cirurgia plástica moderna possui técnicas avançadas que permitem a reconstrução de estruturas corporais comprometidas por traumas, doenças ou malformações congênitas. Essa capacidade aumenta o valor percebido da especialidade, especialmente em casos de alta complexidade.

- **Tecnologia avançada**  
  O setor conta com equipamentos de última geração, como impressoras 3D para próteses personalizadas, sistemas de imagem de alta precisão e softwares de simulação estética, que elevam a qualidade dos procedimentos e aumentam a segurança do paciente.

**Fraquezas (Weaknesses)**

- **Custos hospitalares elevados**  
  Os procedimentos cirúrgicos, especialmente os de natureza reconstrutiva, exigem ambiente hospitalar especializado, mão de obra qualificada e uso de equipamentos sofisticados, o que eleva significativamente os custos operacionais.

- **Demanda de infraestrutura especializada**  
  Muitos procedimentos exigem centros cirúrgicos modernos, equipes multidisciplinares e leitos de recuperação adequados, o que limita a execução em regiões com menor desenvolvimento tecnológico ou infraestrutura de saúde precária.

**Oportunidades (Opportunities)**

- **Crescimento do mercado de cirurgia plástica**  
  O aumento da busca por procedimentos estéticos e reconstrutivos, impulsionado por fatores como envelhecimento populacional, maior aceitação social e divulgação nas redes sociais, representa uma oportunidade significativa de expansão do setor.

- **Uso de tecnologia para acompanhamento**  
  Ferramentas digitais, como aplicativos de monitoramento pós-operatório, telemedicina e prontuários eletrônicos, otimizam o acompanhamento dos pacientes e aumentam a eficiência no atendimento, abrindo novas possibilidades de inovação no setor.

**Ameaças (Threats)**

- **Burocracia regulatória**  
  O excesso de exigências legais e regulatórias para aprovações de novos procedimentos, equipamentos ou medicamentos pode atrasar a inovação e tornar o processo mais oneroso para clínicas e hospitais.

- **Instabilidade econômica e cortes na saúde pública**  
  Crises econômicas e a redução de investimentos públicos em saúde impactam diretamente a acessibilidade e a disponibilidade de procedimentos reconstrutivos, especialmente os realizados pelo sistema público ou com subsídios.

### 2.1.3. Solução (sprints 1 a 5)

Esta subseção (2.1.3) apresenta uma descrição da solução desenvolvida, abordando os principais elementos que estruturam o projeto.

1. **Problema a ser resolvido**

Pacientes com feridas crônicas, como úlceras venosas, por pressão ou de pé diabético, enfrentam dificuldade de acesso ao acompanhamento adequado e cuidados contínuos devido à baixa mobilidade, desinformação e sobrecarga do sistema público de saúde. Isso agrava o quadro clínico, leva a internações desnecessárias e aumenta os custos públicos.

2. **Dados disponíveis**

Não se aplica.

3. **Solução proposta**

Criação de um site com interfaces para pacientes e profissionais de saúde, permitindo envio periódico de fotos das feridas pelos pacientes e acesso a vídeos educativos sobre a prevenção e tratamento de feridas. Além disso, notificações personalizadas serão enviadas de acordo com o perfil do usuário.

A solução apresentará um botão de emergência na área do paciente, que exigirá uma confirmação adicional para evitar acionamentos acidentais. Caso confirmado, os profissionais de saúde receberão um alerta de emergência com as informações necessárias para avaliação e tomada de decisão.

Os profissionais terão acesso a um prontuário digital no qual poderão acompanhar a evolução das feridas por meio das fotos enviadas, além de registrar e consultar detalhes clínicos relevantes.

4. **Forma de utilização da solução**

Após o cadastro presencial do paciente pelo agente de saúde, o paciente envia fotos das feridas em prazo definido pelo agente, assiste a vídeos educativos sobre tratamento de feridas e recebe lembretes. O profissional de saúde define a frequência dos envios e acompanha a evolução clínica dos pacientes por meio do prontuário digital que apresentará detalhes da ferida e as fotos enviadas por cada paciente.

5. **Benefícios esperados**

Melhora do acompanhamento, detecção precoce de complicações, redução de internações e custos ao sistema público de saúde, além de maior adesão ao tratamento, com consequente melhora no prognóstico.

6. **Critério de sucesso e como será avaliado**

O sucesso será medido pelo envio regular de dados, engajamento do paciente, adesão às orientações e feedback positivo dos profissionais da saúde. Indicadores incluem taxa de adesão, número de alertas acionados e evolução clínica das feridas.

### 2.1.4. Value Proposition Canvas

Esta subseção (2.1.4) apresenta o Canvas de Proposta de Valor, destacando os elementos essenciais que tornam o produto relevante para seu público-alvo. A ferramenta permite mapear tarefas, dores e ganhos dos clientes, auxiliando no alinhamento da solução desenvolvida às necessidades reais do parceiro de projeto, garantindo maior aderência e criação de valor.

<div align="center"> <sub> Figura 3: Canvas Proposta de Valor </sub>

![Canvas](https://res.cloudinary.com/dpks4ergy/image/upload/v1746037749/7b479de4-258a-4bd2-b587-d973066f1bfc_ayinhi.jpg)
<sup> Fonte: Autoral, 2025</sup> </div> <br>

**Clientes**

- **Dores:**

  - **Ignorância sobre cuidado pessoal:** Muitos pacientes não sabem como cuidar adequadamente das próprias feridas por falta de conhecimento técnico e pela ausência de uma educação adequada para esse tipo de autocuidado. Isso pode levar a erros no tratamento em casa, como má higienização ou abandono do tratamento, o que piora a condição da ferida e aumenta o risco de infecções, complicações e necessidade de hospitalização. Estudos apontam que a educação em saúde é fundamental para aumentar a adesão ao tratamento e reduzir complicações (ASSIS et al., 2022).

  - **Ausência de práticas benéficas à saúde e de um acompanhamento profissional:** Muitos pacientes não possuem o hábito de manter práticas cotidianas que favoreçam a cicatrização e a saúde da pele, como alimentação adequada, hidratação e repouso. Além disso, a falta de acompanhamento profissional contínuo dificulta o monitoramento da evolução da ferida e a identificação de possíveis complicações, comprometendo a eficácia do tratamento. Protocolos clínicos recomendam o acompanhamento multiprofissional para garantir a adesão e a segurança do paciente (BRASIL, [s.d.]).

- **Ganhos**

  - **Capacitar os pacientes a identificarem e evitar riscos relacionados à sua saúde física:** A proposta visa promover a autonomia dos pacientes ao oferecer informações claras e acessíveis sobre os cuidados com feridas, prevenções e sinais de alerta. Ao entenderem melhor sua condição e os riscos associados, os pacientes se tornam mais aptos a tomar decisões conscientes no dia a dia, reduzindo complicações e aumentando a eficácia do tratamento. A educação em saúde é reconhecida como estratégia central para o empoderamento do paciente (ASSIS et al., 2022).

  - **Entender de forma significativa sobre o cuidado pessoal:** A proposta busca garantir que os pacientes não apenas recebam instruções, mas compreendam de forma profunda e significativa os princípios do cuidado com suas feridas. Isso inclui entender o porquê de cada etapa do tratamento, a importância da higiene, da troca correta de curativos e dos hábitos saudáveis, incentivando o engajamento ativo no próprio processo de recuperação. O entendimento do paciente sobre sua condição é fundamental para a adesão ao tratamento (BRASIL, [s.d.]).

- **Tarefas Clientes**

  - **Aplicar as orientações práticas, sobre cuidados de feridas crônicas apresentadas no site:** A plataforma oferece instruções claras e baseadas em boas práticas de saúde, permitindo que os pacientes apliquem corretamente os cuidados em casa. Isso inclui passo a passo de higienização, troca de curativos, sinais de alerta e medidas preventivas. O objetivo é transformar o conhecimento adquirido em ações concretas no cotidiano do paciente, melhorando os resultados do tratamento. Materiais educativos e protocolos padronizados são fundamentais para garantir a segurança e eficácia do autocuidado (BRASIL, [s.d.]; ASSIS et al., 2022).

  - **Cumprir os acompanhamentos propostos pelo profissional da saúde:** O site atua como um suporte complementar ao trabalho dos profissionais, reforçando as orientações recebidas durante as consultas e lembrando o paciente da importância de seguir o plano de cuidados estabelecido. Isso contribui para a continuidade do tratamento, reduz o risco de complicações e fortalece a relação entre paciente e equipe de saúde. O acompanhamento contínuo é recomendado em diretrizes nacionais e internacionais para o tratamento de feridas (BRASIL, [s.d.]).

**Solução**

- **Analgésicos**

  - **Reduz a insegurança no tratamento de feridas oferecendo vídeos educativos e suporte direto com profissionais da saúde:** A plataforma combate a sensação de medo, dúvida e solidão que muitos pacientes enfrentam ao tratar feridas em casa. Por meio de vídeos educativos produzidos com linguagem simples e acessível, os usuários aprendem passo a passo como realizar curativos corretamente, identificar sinais de agravamento e adotar práticas de higiene seguras. Esses materiais são especialmente úteis para pessoas com pouca experiência em cuidados médicos ou com limitações físicas. Além disso, o suporte direto com profissionais de saúde - seja por chat, videochamada ou mensagens - permite que o paciente tire dúvidas em tempo real, sinta-se acolhido e receba orientações personalizadas, promovendo maior adesão ao tratamento, evitando complicações e fortalecendo o vínculo entre paciente e cuidado. A educação em saúde e o acompanhamento remoto são estratégias eficazes para aumentar a adesão e reduzir o risco de complicações (ASSIS et al., 2022; BRASIL, [s.d.]).

- **Ganhos**

  - **Tomada de decisão mais consciente:** Ao entender melhor sua condição de saúde e os cuidados necessários, o paciente passa a tomar decisões mais seguras e bem-informadas sobre seu tratamento e rotina. Isso contribui para maior autonomia, engajamento e prevenção de erros que possam comprometer a recuperação. O empoderamento do paciente é uma das principais metas das diretrizes clínicas para o tratamento de feridas (BRASIL, [s.d.]).

  - **Desenvolver uma perspectiva mais responsável sobre a saúde física:** Com o apoio das orientações educativas e do acompanhamento disponível na plataforma, o paciente passa a enxergar sua saúde de forma mais ativa e preventiva. Essa mudança de mentalidade incentiva o autocuidado contínuo e reduz a dependência exclusiva de atendimentos emergenciais, promovendo hábitos mais saudáveis a longo prazo. A educação em saúde é reconhecida como estratégia central para a promoção da saúde e prevenção de doenças (ASSIS et al., 2022; BRASIL, [s.d.]).

- **Produtos e Serviços**

  - **Site que orienta e auxilia pacientes no acompanhamento e tratamento de feridas, promovendo o cuidado com a saúde física:** A plataforma é um ambiente digital desenvolvido para fornecer suporte contínuo a pacientes que lidam com feridas, especialmente aquelas de difícil cicatrização, como as causadas por diabetes ou problemas de locomoção. Entre os principais recursos oferecidos estão: vídeos educativos que demonstram os cuidados necessários de forma prática; tutoriais ilustrados com orientações passo a passo para a limpeza, troca de curativos e monitoramento da ferida; e um canal de comunicação direta com profissionais da saúde, que permite ao paciente esclarecer dúvidas e receber orientações personalizadas. Com isso, o site atua como um guia confiável de autocuidado, promovendo o empoderamento do paciente, reduzindo a dependência de atendimentos presenciais, prevenindo infecções e evitando agravamentos que podem levar à hospitalização ou complicações mais graves. A integração de materiais educativos e suporte remoto é recomendada em protocolos e diretrizes para o tratamento de feridas no SUS (BRASIL, [s.d.]; ASSIS et al., 2022).

### 2.1.5. Matriz de Riscos do Projeto

Esta subseção (2.1.5) apresenta a Matriz de Riscos do Projeto, identificando as principais ameaças e oportunidades que podem impactar o desenvolvimento da solução. A Matriz de Riscos é uma ferramenta que avalia eventos com base em sua probabilidade de ocorrência e no impacto que podem causar, permitindo a antecipação de problemas e o planejamento de ações corretivas. A seguir, é apresentada a matriz elaborada para o projeto.

<div align="center"> <sub> Figura 4: Matriz de Riscos </sub>

![Matriz de Risco](https://res.cloudinary.com/dxutpx5b1/image/upload/v1746211902/matriz-de-risco_f9ernm.png)
<sup> Fonte: Autoral, 2025</sup> </div> <br>

- **Ameaças**

**A. Falta de adesão dos profissionais de saúde**

Apesar da proposta inovadora, alguns profissionais de saúde podem resistir ao uso do sistema por falta de familiaridade com a tecnologia ou por já estarem sobrecarregados com outras atividades. Essa resistência pode comprometer o uso eficaz da plataforma e limitar seus benefícios.

**Plano de ação:** Realizar oficinas de capacitação e treinamentos práticos nas UBSs, mostrando os benefícios diretos da plataforma para a rotina dos profissionais. Estimular o envolvimento desde a fase piloto e incluir canais de suporte técnico e dúvidas frequentes para facilitar o uso.

**B. Internet instável**

Muitos pacientes atendidos pelas UBSs vivem em regiões com infraestrutura de internet precária. Isso pode dificultar o envio de fotos, vídeos ou o preenchimento de questionários, comprometendo o monitoramento remoto e a continuidade do tratamento.

**Plano de ação:** Otimizar a plataforma para funcionar mesmo em conexões lentas, com compressão de imagens e salvamento offline automático para envio posterior. Além disso, explorar parcerias com operadoras ou programas públicos para facilitar o acesso à internet nos territórios mais críticos.

**C. Quebra de privacidade e segurança de dados**

O site lida com informações sensíveis, como imagens de feridas e dados pessoais. Qualquer falha de segurança pode resultar no vazamento dessas informações, afetando a confiança dos usuários e expondo o projeto a riscos legais e éticos.

**Plano de ação:** Adotar protocolos rígidos de segurança, como criptografia de ponta a ponta, autenticação em dois fatores e conformidade com a LGPD. Implementar revisões regulares de segurança e campanhas educativas para os usuários sobre boas práticas de privacidade.

**D. Problemas técnicos e instabilidade do sistema**

Erros de funcionamento, travamentos ou quedas do site podem dificultar ou impedir o uso por parte dos pacientes e profissionais. Esses problemas comprometem a confiabilidade da plataforma e podem atrasar o acompanhamento necessário.

**Plano de ação:** Estabelecer uma rotina de testes e monitoramento constante do sistema (testes automatizados e manuais), além de criar um canal de suporte técnico rápido. Ter planos de contingência para restaurar o serviço rapidamente em caso de falhas.

**E. Problemas de acessibilidade para pacientes com deficiência**

Pacientes com deficiências visuais, motoras ou cognitivas podem ter dificuldade em usar o sistema se ele não seguir boas práticas de acessibilidade. Isso pode excluir parte do público-alvo, indo contra o princípio de inclusão no cuidado com a saúde.

**Plano de ação:** Aplicar as diretrizes do WCAG na construção da interface e realizar testes com usuários reais com deficiência. Manter uma política contínua de melhorias em acessibilidade com base em feedbacks e validações com especialistas da área.

- **Oportunidades**

**F. Aumento da eficiência no acompanhamento das feridas**

Com o envio remoto de fotos, vídeos e respostas a questionários, os profissionais de saúde podem acompanhar mais pacientes simultaneamente, otimizando o tempo e possibilitando intervenções mais rápidas em casos de agravamento das feridas.

**G. Integração com outras soluções de saúde digital**

A plataforma pode ser integrada a outros sistemas de gestão de saúde (como prontuários eletrônicos), ampliando sua funcionalidade e criando um ecossistema mais completo e eficiente para o atendimento e monitoramento dos pacientes.

**H. Personalização do tratamento com base em dados coletados**

Com o armazenamento e a análise dos dados enviados pelos pacientes ao longo do tempo, é possível identificar padrões e adaptar os cuidados de forma mais precisa, promovendo um tratamento individualizado e mais eficaz.  
Por exemplo, se as fotos semanais mostrarem piora em certos períodos, o profissional pode ajustar o tratamento com base em hábitos ou fatores ambientais.

**I. Redução de custos operacionais para as UBS**

Ao permitir o acompanhamento remoto, o site contribui para a diminuição da necessidade de deslocamentos e visitas presenciais, reduzindo os custos com transporte, materiais e tempo de atendimento nas unidades.

**J. Melhoria na usabilidade do site com feedback dos usuários**

A coleta contínua de sugestões e relatos dos pacientes e profissionais permite ajustes e melhorias no design e nas funcionalidades da plataforma, tornando-a mais intuitiva, acessível e eficaz para todos os perfis de usuário.

## 2.2. Personas

Esta subseção (2.2) apresenta os perfis fictícios criados para representar os diferentes tipos de usuários da solução. Personas são ferramentas utilizadas para entender melhor o público-alvo, sintetizando suas características, necessidades, comportamentos e expectativas. Ao construir personas, é possível direcionar o desenvolvimento do produto para atender de maneira mais eficaz às reais demandas dos usuários, aumentando a relevância e a aderência da solução. A seguir, estão descritas as personas desenvolvidas com base no contexto do projeto.

<div align="center"> <sub> Figura 5: Persona 1 </sub>

![Joana](https://res.cloudinary.com/dxutpx5b1/image/upload/v1746222401/joana_amorim_gbzhvd.png)
<sup> Fonte: Autoral, 2025</sup> </div> <br>

Escolhemos a persona Joana Amorim porque ela enfrenta dificuldades de locomoção e precisa de acompanhamento constante para feridas crônicas, agravadas pela diabetes. A possibilidade de enviar fotos pelo celular e receber acompanhamento remoto atende à sua necessidade de reduzir deslocamentos, facilitando o tratamento e melhorando sua qualidade de vida.

<div align="center"> <sub> Figura 6: Persona 2 </sub>

![Juliana](https://res.cloudinary.com/dkn9xjb1r/image/upload/v1746225487/WhatsApp_Image_2025-05-02_at_19.28.39_ulfg4t.jpg)
<sup> Fonte: Autoral, 2025</sup> </div> <br>

Escolhemos a persona Juliana Ribeiro porque ela é uma enfermeira que lida com uma grande demanda de pacientes com feridas crônicas, o que dificulta o acompanhamento contínuo. Sua preocupação com as complicações que podem surgir rapidamente a motiva a buscar soluções que permitam monitorar os pacientes à distância, de forma segura e eficaz, garantindo um cuidado mais eficiente e confiável.

## 2.3. User Stories (sprints 1 a 5)

Esta subseção (2.3) apresenta a lista de User Stories, que são descrições breves e objetivas das necessidades dos usuários, escritas de forma que orientem o desenvolvimento de funcionalidades específicas da solução. Essa abordagem facilita a comunicação entre a equipe e mantém o foco no valor entregue ao usuário. A seguir, são listadas as User Stories elaboradas para o projeto, com destaque para a explicação do critério INVEST aplicado às cinco histórias prioritárias.

**USER STORIES**

User Stories (ou histórias de usuário) são descrições curtas e simples de uma funcionalidade ou necessidade, escritas do ponto de vista do usuário final. Elas são muito utilizadas em metodologias ágeis, como Scrum e XP, para guiar o desenvolvimento de software de forma centrada no valor para o usuário.

Seguem abaixo as 10 User Stories, sendo as 5 primeiras as prioritárias.

| _CÓDIGO_ | _TÍTULO_                                                    | _PERSONA_                    | _HISTÓRIA_                                                                                                                                                                     | _CRITÉRIO DE ACEITE 1_                                                    | _CRITÉRIO DE ACEITE 2_                                                | _TESTE DE ACEITE 1_                                            | _TESTE DE ACEITE 2_                                                |
| -------- | ----------------------------------------------------------- | ---------------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ | ------------------------------------------------------------------------- | --------------------------------------------------------------------- | -------------------------------------------------------------- | ------------------------------------------------------------------ |
| _US01_   | Envio de foto da ferida pelo site                           | Joana Amorim (Paciente)      | Quero poder enviar uma foto da minha ferida pelo site de forma simples, para que eu receba orientações sem precisar sair de casa com frequência.                               | O site permite o envio de fotos de forma intuitiva.                       | A confirmação de envio é exibida.                                     | Usuária idosa consegue enviar a foto sem ajuda.                | A imagem é recebida pela equipe com qualidade.                     |
| _US02_   | Acompanhamento remoto com fotos padronizadas                | Juliana Ribeiro (Enfermeira) | Quero acessar fotos e descrições padronizadas das feridas enviadas pelos pacientes, para acompanhar a evolução remotamente sem depender de visitas presenciais.                | As imagens são organizadas por paciente com data e descrição.             | O sistema permite registrar evolução e conduta.                       | A enfermeira acessa fotos e dados clínicos em poucos cliques.  | Consegue acompanhar a evolução da ferida sem ir até o paciente.    |
| _US03_   | Orientações iniciais com linguagem simples e vídeos básicos | Joana Amorim (Paciente)      | Quero receber orientações iniciais com linguagem simples e vídeos básicos, para compreender os primeiros passos do cuidado com minha ferida.                                   | O conteúdo aborda as primeiras medidas após o diagnóstico.                | Vídeos e textos são acessíveis para diferentes faixas etárias.        | A paciente entende o que fazer logo após a consulta.           | Aplica os primeiros cuidados com confiança.                        |
| _US04_   | Resposta com orientações personalizadas                     | Juliana Ribeiro (Enfermeira) | Quero enviar orientações personalizadas com base nas fotos e histórico do paciente, para que o cuidado remoto seja eficaz e direcionado.                                       | Há campo para responder com texto e arquivos.                             | A mensagem é vinculada ao paciente e à data de envio.                 | Enfermeira envia orientação com base na imagem recebida.       | Paciente visualiza a orientação corretamente.                      |
| _US05_   | Notificação de novo envio do paciente                       | Juliana Ribeiro (Enfermeira) | Quero receber notificações sempre que um paciente enviar nova foto ou informação sobre a ferida, para que eu possa avaliar os casos com agilidade e priorizar os atendimentos. | O sistema envia notificações em tempo real (por e-mail ou na plataforma). | As notificações trazem nome do paciente, data e breve descrição.      | A enfermeira recebe a notificação poucos minutos após o envio. | Consegue identificar rapidamente quais casos precisam de resposta. |
| _US06_   | Acesso contínuo a conteúdos educativos aprofundados         | Joana Amorim (Paciente)      | Quero acessar vídeos e textos educativos aprofundados sobre higiene, alimentação e curativos, para manter os cuidados ao longo do tratamento.                                  | O site apresenta seções organizadas por tema.                             | Os conteúdos são revisados por profissionais de saúde atualizados.    | Paciente navega entre temas com facilidade.                    | Aplica boas práticas de autocuidado regularmente.                  |
| _US07_   | Classificação de risco dos casos recebidos                  | Juliana Ribeiro (Enfermeira) | Quero ver uma classificação de risco nos casos recebidos, para priorizar os pacientes com maior gravidade.                                                                     | O sistema usa critérios clínicos para classificar os casos.               | A lista de pacientes pode ser filtrada por gravidade.                 | Casos graves aparecem no topo da lista da enfermeira.          | A enfermeira consegue agir primeiro nos casos urgentes.            |
| _US08_   | Receber lembretes sobre cuidados diários                    | Joana Amorim (Paciente)      | Quero receber lembretes sobre cuidados diários, para não esquecer nenhuma etapa importante do meu tratamento.                                                                  | O sistema envia lembretes com base nas orientações recebidas.             | O paciente pode escolher o canal de recebimento (e-mail ou WhatsApp). | A paciente recebe lembretes no horário certo.                  | Ela segue a rotina com mais frequência após os lembretes.          |
| _US09_   | Visualizar dados clínicos do paciente                       | Juliana Ribeiro (Enfermeira) | Quero acessar informações clínicas básicas do paciente, para embasar melhor as condutas e orientações.                                                                         | Dados clínicos ficam disponíveis no perfil do paciente.                   | Dados como comorbidades e medicações aparecem de forma clara.         | Enfermeira localiza rapidamente informações do paciente.       | As condutas são baseadas nos dados registrados.                    |
| _US10_   | Confirmar recebimento de orientação                         | Joana Amorim (Paciente)      | Quero confirmar que recebi e entendi a orientação enviada pela enfermeira, para que ela saiba que estou seguindo corretamente.                                                 | Há um botão ou campo para o paciente confirmar o recebimento.             | Há uma mensagem de retorno para a equipe.                             | A paciente clica em "Confirmar" após ler a mensagem.           | A enfermeira vê a confirmação no sistema.                          |

**MÉTODO INVEST**

O método INVEST é um acrônimo usado para definir critérios de qualidade para boas User Stories (histórias de usuário) no desenvolvimento ágil de software. Foi criado por Bill Wake para ajudar equipes a escreverem histórias claras, úteis e gerenciáveis
Seguem abaixo o método invest, sendo as 5 primeiras as prioritárias.

| CÓDIGO | _Independente_                                   | _Negociável_                                                                                                                        | _Valorosa_                                | _Estimável_                                                               | _Small_                             | _Testável_                             |
| ------ | ------------------------------------------------ | ----------------------------------------------------------------------------------------------------------------------------------- | ----------------------------------------- | ------------------------------------------------------------------------- | ----------------------------------- | -------------------------------------- |
| _US01_ | Pode ser desenvolvida sem outras funcionalidades | O tipo de arquivo permitido, tamanho da imagem e formato do botão de envio podem ser discutidos com a equipe de design e front-end. | Permite iniciar o fluxo remoto de cuidado | Sim, envolve tecnologia de upload já conhecida e fácil de estimar.        | Sim, envolve uma ação objetiva      | Sim, com envio e confirmação visual.   |
| _US02_ | Funciona sozinha após o envio estar disponível   | A forma de exibição dos dados, filtros e agrupamentos pode ser definida junto com a equipe e ajustada conforme a usabilidade.       | Permite avaliação remota eficiente        | Sim, com base em componentes já utilizados para exibição e organização.   | Sim, escopo claro                   | Sim, com visualização e registro.      |
| _US03_ | Pode ser exibida mesmo sem resposta direta       | O tipo e o nível de profundidade dos vídeos/textos podem ser definidos com os usuários e especialistas em saúde.                    | Educação melhora adesão ao cuidado        | Sim, pois envolve produção de conteúdo e front-end com escopo previsível. | Sim, componente de conteúdo isolado | Sim, com teste de entendimento.        |
| _US04_ | Pode ser feita depois do envio de fotos          | O canal, o tipo de linguagem e a forma de anexar arquivos podem ser adaptados com base no perfil do paciente.                       | Essencial para tratamento personalizado   | Sim, uso de mensagens com lógica já aplicada em outros contextos.         | Sim, ação direta da enfermeira      | Sim, com envio e visualização.         |
| _US05_ | Pode funcionar com qualquer envio de paciente    | O canal e o intervalo de notificação podem ser definidos conforme a preferência da equipe.                                          | Agiliza triagem e resposta                | Sim, notificações são funcionalidades comuns e estimáveis.                | Sim, tarefa pequena                 | Sim, com envio e recebimento em tempo. |

# <a name="c3"></a>3. Projeto da Aplicação Web (sprints 1 a 4)

A seção 3 apresenta o projeto da aplicação web desenvolvido ao longo das sprints, abrangendo desde a concepção inicial até as definições técnicas e estruturais que orientam o desenvolvimento.

## 3.1. Arquitetura (sprints 3 e 4)

Nesta subseção (3.1) será apresentado o diagrama de arquitetura desenvolvido, que ilustra a estrutura da nossa aplicação web seguindo o padrão MVC (Model-View-Controller). O diagrama de arquitetura é uma representação visual dos componentes do sistema, suas interações e as tecnologias envolvidas, utilizada para planejar e comunicar a organização geral da solução. Ele foi criado com o objetivo de demonstrar as camadas principais (Model, View e Controller), os elementos constituintes de cada uma e as tecnologias propostas para a implementação.

Diagrama de Componentes apresenta a organização dos módulos da aplicação, evidenciando os componentes que a compõem, suas responsabilidades e como interagem entre si. Ele oferece uma visão mais física da arquitetura, útil para compreender a divisão de camadas, integração entre partes do sistema e responsabilidades de cada módulo.

<div align="center"> <sub> Figura 7: Diagrama de Componentes </sub>

![Diagrama de componentes](https://res.cloudinary.com/dwewomj84/image/upload/v1748642123/Imagem_do_WhatsApp_de_2025-05-30_%C3%A0_s_18.23.10_769f83de_yboneh.jpg)
<sup> Fonte: Autoral, 2025</sup> </div> <br>

Diagrama de Classes descreve a estrutura lógica do sistema, destacando as principais classes que compõem a aplicação, seus atributos, métodos e os relacionamentos entre elas. Ele é fundamental para visualizar a modelagem orientada a objetos, facilitar o entendimento do domínio do sistema e orientar o desenvolvimento de funcionalidades.

O diagrama abaixo representa a arquitetura de um sistema de gerenciamento médico-hospitalar, projetado para atender às necessidades de uma instituição de saúde. O sistema foi modelado seguindo os princípios da programação orientada a objetos, com foco na organização eficiente dos dados e processos relacionados ao atendimento de pacientes.

<div align="center"> <sub> Figura 8: Diagrama de Classes </sub>

![Diagrama de Classes](https://res.cloudinary.com/dt8qannja/image/upload/v1748542605/Diagramadeclassescorreto_kquqfc.png)
<sup> Fonte: Autoral, 2025</sup> </div> <br>

[Acesse o Diagrama de classes para melhor visualização](https://www.plantuml.com/plantuml/png/dLPDRnit4BthLn2-j6cTm2cGYmz5b4Hf0-GZbTsWDs94ZYG294SPSXT10l_txfJQShtUZQhUBFDnTjxZcu_dAGFd9lZ5mdX8INq4up1cLDyMIltixBMwMQtVBD_AhqW1jQ5my9ZfMdrQhb_-lbp_-CkpPu_as4867I4P5p0AuDNoTYK72RyCu4-l_a5ZjCXekeFRrL-tyf-C7hSKSH2vGADhqMjCXZomis1n6ArWCWPjtTQPnWj0Up8Ko40N_yM73szNo_TJgDQb1do5z2mCM4YP9RatxrOtjyjt7nTtlKH_lhYPLQVPfB4mPr45Hj26DiYvOQWNziJ0ZlIsYSPHXABmFHLgWikTlE0DDFR_F6Ds1z8W8R0UFhjwfBu5jCxGfSAuHGxekFkx02LS4rsM7Eya2EFEfSoJWRuh00Pm_k7pThfgOV7c_UtgjzMwSdsDueW9ceN75kLu64-8DDuWWpgWiWusc9smElwpnKW9BfN71AGEnEh0c18KNfuCUFSL392629c2C_V3rr8ui1EHG9CXPep6GQc9wkaTGriCtJEJivGXDsaaFcJNTcbXKCOPo5oIkAo_BDnzpxzoTZWMzxIKI_44n7qnrwomvGlGWdTWOFm9sYJa5WnXwj--kfuXb-1QoY5Wj62fdcyPFpSeEkb0qMNYIPyB4QOlBWooq99KLRSyUw5PTONTFFdrqR2lATCiwItbCLy3mSKzQU3FZMkdoWa22E2OKWZNQhrQlYtLP4aP2efnrvJNmzWwiAGFnBf_hNljIGZR7W6xqVa9vlrbdjRWZmPJbkx2ZcRdG7KtOZJxqkj7eYICsKLBsXB_-ntr3PC13-hgyTEhk_AAs24Dw8I6STohIIvNEQK68iPz4wGsMxnKM-ACTuNqXaSoIOFoUsUbJiLk4cm8S5tkO4PVR39Jqid5J07Im9ZuLPk6fGH7rXtS7ioLfAlLIvJJrVUI0l76oVMg_KnNCnKxRcsbeM6wL7jtGCuOatG9sc0IV2SRPlEaTeeElukKf0NMyi5G2LIIe_B-hyJUGKPsu5UsCI0AJjFAZPyRHePYniRIg2Espc97BUENB8EeiSVPqx3JSZQrec0ox0pKLncE2Gs-7JMRYLPTK3tFsd9vt0uVIh2krfchgsglXxsrR2lkwto3w8Fqo56G-GOHZXcUqp8_kUGol98HwBm3dgNK2X16UGGIBzIptajC1ctfAaap7lbMjqrPi0w44avtFtSC0MoT2TMbPr1yJrcgMcIySIbZcDyqOeMEVEenR1WpolWGetha31e3yWvbs5qgGp4rFeCEm3Aw1qLQlL-ephkjFv8HCkySECSi9ofxrLEONnDaRzLGLzP94rHka35QP-5qpy5O9cyVjYfUvuHiwbqVf7Hm6VpT-IRuGQtHWup4r7NJj93D_-BfXNhyM5tyT75lpxwMHymOrA-BldqFeMMRwb07IgbnCwYf0IntogZ5cLjJa-F4XyQbs-4P3MvcFZ5H1oTkL6y8iXYWGowEEbJWrDOfLugK3-73FMqUDjLn9O-Ko3Ym9AaCN-zqFgKJ2OTxzwwUF9dDhMnRenidS-JoQ_RYpElDfpVp959fA3eycCzdwlWSess2Nop-1W00)

## 3.2. Wireframes

Nesta subseção (3.2) serão apresentados os wireframes desenvolvidos, que ilustram a proposta inicial da interface da nossa aplicação. Wireframes são representações visuais simplificadas da interface de um sistema, utilizadas para planejar e validar a estrutura, a disposição dos elementos e a navegação entre as telas antes do desenvolvimento final. Eles foram criados com o objetivo de representar as funcionalidades principais e a organização visual da solução.

A seguir, é possível verificar algumas das telas do wireframe, assim como a descrição de cada tela desenvolvida na versão desktop e mobile.

Para ver todas as telas, acesse: [Wireframes](https://miro.com/app/board/uXjVI2yb0FQ=/).

**Tela incial:** Na primeira parte, temos o painel inicial do aplicativo, onde há duas áreas para a pessoa escolher. Se ela for paciente, entra na área do paciente, caso contrário, entra na do agente de saúde (figura 9).

<div align="center"> <sub> Figura 9: Wireframe da tela inicial versão mobile</sub>

![Tela inicial](https://res.cloudinary.com/dpks4ergy/image/upload/v1747160918/WhatsApp_Image_2025-05-13_at_15.22.00_sfhzon.jpg)
<sup> Fonte: Autoral, 2025</sup> </div> <br>

**Área login paciente:** Se você entrar na área do paciente, terá que inserir seu CPF ou e-mail já cadastrado, junto com sua senha. Caso ainda não possua cadastro, haverá uma opção de se cadastrar ao final da tela. Isso pode ser observado na imagem abaixo.

<div align="center"> <sub> Figura 10: Wireframe da tela de login versão mobile</sub>

![Tela de login](https://res.cloudinary.com/dpks4ergy/image/upload/v1747160957/WhatsApp_Image_2025-05-13_at_15.22.14_vpjrsv.jpg)
<sup> Fonte: Autoral, 2025</sup> </div> <br>

**Área login agente de saúde:** Se a pessoa for um agente de saúde, ela deverá inserir sua identificação profissional, junto com sua senha de segurança. Caso esqueça a senha, haverá a opção de redefini-la logo abaixo.

**Termo de consentimento:** Quando você inserir todos os dados corretamente, uma aba com um termo de consentimento aparecerá para o usuário ler. Após finalizar, ele deverá clicar na opção "Aceitar" para seguir para a próxima etapa.

**Instrução de uso:** Logo após o termo de consentimento, aparecerá uma tela contendo uma demonstração básica de como usar o aplicativo e quais são suas funcionalidades. Isso ajudará as pessoas que ainda não conhecem o app a entenderem como ele funciona.

**Menu:** O menu conterá várias áreas para o usuário explorar, com opções como vídeos educativos, resultados de exames, botão de tutorial, envio de foto de nova ferida, e também um botão de emergência para situações graves.

**Área de vídeos:** Na área de vídeos educativos, haverá diversos vídeos que abordam assuntos relacionados a feridas, ajudando o paciente a se cuidar com orientações sobre prevenção e tratamento.

- Essa tela atende à User Story US03, que busca oferecer explicações acessíveis com vídeos ilustrativos para melhorar o entendimento do paciente.

<div align="center"> <sub> Figura 11: Tela de vídeos versão desktop </sub>

![Tela de vídeos](https://res.cloudinary.com/dpks4ergy/image/upload/v1747246341/WhatsApp_Image_2025-05-14_at_15.02.47_yooj5a.jpg)
<sup> Fonte: Autoral, 2025</sup> </div> <br>

**Avaliação paciente:** Na área de avaliação do paciente, serão exibidas informações como o tipo de ferida, o odor identificado e a localização da ferida, para que os agentes de saúde possam analisar.

**Mandar avaliação da ferida:** Na área de envio de foto, o paciente poderá tirar uma foto ou selecionar uma imagem da galeria do celular. Também será necessário inserir uma breve descrição da ferida antes de enviar.

- Essa funcionalidade está alinhada com a User Story US01, permitindo que o paciente envie imagens da ferida de forma simples e remota.

**Cadastrar no app:** Quando o usuário estiver instalando o aplicativo pela primeira vez, será necessário se cadastrar com informações pessoais como nome completo, CPF, data de nascimento, CEP e telefone para identificação.

**Paciente dos agentes de saúde:** Para o agente de saúde, haverá uma área onde ele poderá visualizar todos os pacientes sob sua responsabilidade. Nela, poderá ver o nome, idade, tipo de ferida, foto da ferida e acessar detalhes completos do paciente, além de agendar visitas ou solicitar novas fotos para análise. Também será exibido o status do caso: em análise, em andamento ou finalizado.

- Esse recurso responde à User Story US02, facilitando o acompanhamento remoto por meio da visualização de fotos e dados clínicos organizados.

**Botão de emergência:** Caso o paciente clique no botão de emergência, aparecerão dois botões: um para confirmar que realmente precisa de ajuda e outro para cancelar, caso tenha sido um toque acidental, especialmente considerando que a maioria dos usuários são idosos.

**Notificações do paciente:** Na área do paciente, haverá uma seção de notificações que o avisará sobre atualizações importantes, como a necessidade de tirar uma nova foto da ferida, data de consulta agendada e mensagens do agente de saúde.

- Essa funcionalidade também está ligada à User Story US05, ao notificar o agente de saúde sobre novas informações enviadas pelo paciente.

**Notificações agentes de saúde:** A mesma área estará disponível para o agente de saúde, com notificações sobre seus pacientes, como fotos recebidas, marcação de consultas e mensagens enviadas pelos pacientes.

**Dados dos pacientes:** Cada paciente possui uma ficha técnica com todos os dados da ferida, incluindo fotos e observações desde o início até a evolução do quadro, com detalhes para análise.

- Essa tela está associada à User Story US04, pois permite que o agente de saúde envie orientações personalizadas com base no histórico do paciente.

As telas em desktop seguem a mesma temática e estrutura, com a diferença de possuirem uma barra lateral de menu no canto esquerdo da tela todo o tempo.

## 3.3 Guia de Estilos

Esta subseção (3.3) apresentará o **Guia de Estilos** que é um documento essencial que estabelece as diretrizes de design para uma empresa, projeto ou marca. Ele funciona como referência central para garantir **consistência e coesão visual** em todos os materiais e interfaces.

Este guia apresenta os padrões de **cores, tipografia, iconografia, componentes e demais elementos visuais** aplicados nesta aplicação web.

Caso queira ver as imagens que serão apresentadas nesta seção completa, acesse o [design no Figma](https://www.figma.com/design/caqNCT21xBUL9gMFQAXX2s/Sem-t%C3%ADtulo?node-id=0-1&t=EJipqXBhQ1y3POIV-1).

### 3.3.1 Cores

Esta subseção (3.3.1) apresentará as cores, que são elementos essenciais para a construção da identidade visual da aplicação. A escolha cuidadosa da paleta garante não apenas uma estética agradável, mas também assegura contraste, acessibilidade e usabilidade, contribuindo para uma experiência do usuário clara, consistente e confiável.

As cores principais são compostas por tons de **azul e branco**, que garantem uma identidade visual coerente e agradável. **Cores auxiliares**, como **verde, vermelho e laranja**, são utilizadas para **status, alertas e feedbacks visuais**.

<div align="center"> <sub> Figura 12: Cores</sub>
  
![Paleta de Cores](https://res.cloudinary.com/dkn9xjb1r/image/upload/fl_preserve_transparency/v1747923690/Group_6_e3qrdq.jpg)
<sup> Fonte: Autoral, 2025</sup> </div> <br>

**Cores Principais**

| Cor               | Hex       | Aplicação                                                                                |
| ----------------- | --------- | ---------------------------------------------------------------------------------------- |
| Azul Escuro       | `#013C6D` | Cor principal da identidade. Usada em botões, links, cabeçalhos e elementos de destaque. |
| Background Branco | `#F8FAFC` | Fundo geral da aplicação, promovendo leveza e contraste.                                 |
| Branco Puro       | `#FFFFFF` | Fundos de seções, cards e textos sobre botões escuros para garantir legibilidade.        |

**Cores para Textos**

| Cor          | Hex       | Aplicação                                                        |
| ------------ | --------- | ---------------------------------------------------------------- |
| Cinza Claro  | `#718096` | Textos secundários e informações menos destacadas.               |
| Cinza Escuro | `#2D3748` | Títulos e textos principais, oferecendo contraste e boa leitura. |
| Azul Escuro  | `#013C6D` | Links e textos interativos.                                      |
| Branco       | `#FFFFFF` | Texto sobre botões escuros.                                      |
| Azul Médio   | `#19547B` | Texto dentro de botões claros, mantendo harmonia e contraste.    |

**Cores para Botões**

| Cor         | Hex       | Aplicação                                                            |
| ----------- | --------- | -------------------------------------------------------------------- |
| Branco      | `#FFFFFF` | Botões neutros ou de ações secundárias.                              |
| Azul Escuro | `#013C6D` | Botões de ações principais, reforçando a identidade visual.          |
| Vermelho    | `#B85B5B` | Botões de emergência, ações críticas ou que exigem atenção imediata. |

**Cores de Status do Paciente**

| Status   | Cor      | Hex       | Aplicação                                                                   |
| -------- | -------- | --------- | --------------------------------------------------------------------------- |
| Atrasado | Vermelho | `#E53E3E` | Indica urgência, pendências críticas ou atrasos.                            |
| Em Dia   | Verde    | `#38A169` | Representa normalidade, acompanhamento adequado e situação estável.         |
| Pendente | Laranja  | `#DD6B20` | Alerta para situações que requerem atenção, mas que ainda não são críticas. |

A definição criteriosa da paleta de cores não só reforça a **identidade visual da aplicação**, como também melhora a **experiência do usuário**, garantindo **clareza, acessibilidade e coerência visual** em todas as interações.

**Elementos da Página**

<div align="center"> <sub> Figura 13: Elementos da página</sub>
  
![Imagem dos Elementos](https://res.cloudinary.com/dkn9xjb1r/image/upload/fl_preserve_transparency/v1747924111/Group_7_vpyaln.jpg)
<sup> Fonte: Autoral, 2025</sup> </div> <br>

**Botões**

Os botões da aplicação seguem um padrão visual que visa garantir **acessibilidade, clareza e hierarquia nas ações**.  
O botão principal utiliza a cor **Azul Escuro (`#013C6D`)** com texto branco, sendo aplicado principalmente em ações prioritárias.

Existem duas variações principais:

- **Botão grande:** 414px de largura, texto na fonte Roboto, tamanho 36px, utilizado para ações mais relevantes.
- **Botão menor:** 311px de largura, texto em 24px, aplicado em contextos de menor prioridade.

Para situações de alerta ou ações críticas, é utilizado o **botão de emergência**, na cor **Vermelho (`#B85B5B`)**, mantendo as mesmas dimensões do botão menor (**311px x 92px**) e tipografia padrão (**Roboto, 24px, branco**).

Além dos botões preenchidos, há versões com **fundo branco e borda azul escuro (`#013C6D`)**, que funcionam como alternativas para **ações secundárias**, mantendo coerência visual. O texto desses botões é invertido — **Azul Escuro sobre fundo branco** — seguindo os mesmos tamanhos e tipografia das versões preenchidas.

**Responsividade**

Todos os botões possuem **altura fixa de 92px**, porém, a **largura é adaptável**, dependendo do tamanho da tela ou do dispositivo utilizado. Assim, o botão pode aumentar ou diminuir proporcionalmente, garantindo uma boa experiência tanto em dispositivos móveis quanto em desktops, sem comprometer a **legibilidade e a hierarquia visual**.

**Títulos e Componentes**

Os títulos seguem o padrão de **background branco suave (`#F8FAFC`) com containers brancos (`#FFFFFF`)**.  
A tipografia utilizada é **Roboto, em negrito (Bold), tamanho 32px**, garantindo **legibilidade, clareza e hierarquia** nas seções da interface.

**Especificações de Layout**

- Altura fixa dos botões: **92px**.
- Largura dos botões: **414px (grande)** e **311px (menor)**, podendo ser ajustada proporcionalmente conforme o tamanho da tela.
- Texto dos botões: **36px (botão grande)** e **24px (botão menor)**.
- Tipografia: **Roboto** em todos os botões e títulos.
- Cores:
  - **Azul Escuro (`#013C6D`)** para ações principais.
  - **Vermelho (`#B85B5B`)** para emergências.
  - **Branco (`#FFFFFF`)** para botões secundários.

**Logo**

Logo é o símbolo visual que representa uma marca ou empresa. Ela é essencial para criar uma identidade única, facilitar o reconhecimento e transmitir confiança ao público. Uma boa logo ajuda a comunicar os valores da marca de forma rápida e mantém a consistência visual em todos os canais. A seguir é possível visualizar a logo do Cicatriza+:

<div align="center"> <sub> Figura 14: Logo e suas cores</sub>
  
![Logo](https://res.cloudinary.com/dkn9xjb1r/image/upload/fl_preserve_transparency/v1747924375/Group_8_stn27r.jpg)
<sup> Fonte: Autoral, 2025</sup> </div> <br>

**Cores e Significado**

A paleta de cores utilizada na logo foi cuidadosamente selecionada para transmitir sensações alinhadas com os valores e propósitos da área da saúde, como **acolhimento, cuidado e confiança**.

- **Verde Água – `#55BBA5`**  
  Utilizado no fundo da logo, transmite **equilíbrio, serenidade e bem-estar**. É uma cor associada à saúde, à cura e à tranquilidade, ajudando a criar uma conexão de **segurança com os usuários**.

- **Bege Suave – `#F9E6D1`**  
  Aplicado nos curativos da logo, reforça sensações de **acolhimento, leveza e cuidado**. O tom neutro traz **conforto visual**, remetendo à **empatia e ao toque humano**, essenciais no contexto da saúde.

- **Verde Escuro – `#3B8788`**  
  Usado no contorno e nos detalhes da logo, representa **estabilidade, segurança e confiança**. Esse tom mais fechado reforça a **seriedade e a credibilidade do serviço**, sem perder a suavidade do design.

Essa combinação de cores foi pensada para gerar **proximidade e confiança**, criando uma identidade visual que **acolhe e transmite cuidado**, refletindo o propósito de um **serviço de saúde digital humanizado**.

### 3.3.2 Tipografia

Esta subseção (3.3.2) apresentará a tipografia que vai muito além da simples escolha de fontes. Ela é responsável pela organização, hierarquia e apresentação do conteúdo, impactando diretamente na **legibilidade, acessibilidade e na experiência do usuário** dentro da nossa aplicação.

No contexto digital, uma tipografia bem definida contribui para uma comunicação mais clara, intuitiva e eficiente.

Optamos pela família tipográfica **Roboto** por sua **alta legibilidade** em diferentes tamanhos e dispositivos, além de ser uma fonte versátil, limpa e moderna. Sua simplicidade, somada a proporções bem balanceadas, permite que os usuários naveguem pela plataforma com **facilidade, sem esforço visual**.

**Justificativa da Escolha**

A fonte **Roboto** combina uma **estética contemporânea** com traços humanistas. Seu desenho oferece um **ritmo de leitura confortável**, tornando-a ideal tanto para **blocos de texto quanto para títulos, botões e elementos de interface**. Sua popularidade no design digital também reforça a **consistência e familiaridade**, fundamentais para uma aplicação acessível e intuitiva.

<div align="center"> <sub> Figura 15: Tipografia e tamanhos</sub>

![Tipografia](https://res.cloudinary.com/dkn9xjb1r/image/upload/fl_preserve_transparency/v1747924591/Group_9_elafrh.jpg)
<sup> Fonte: Autoral, 2025</sup> </div> <br>

**Aplicações da Tipografia**

**Headlines (Títulos)**

Utiliza-se peso **Bold** para garantir contraste e hierarquia. Os tamanhos seguem a hierarquia definida abaixo:

| Headline   | Tamanho | Line-height |
| ---------- | ------- | ----------- |
| Headline 1 | 56px    | 110%        |
| Headline 2 | 48px    | 120%        |
| Headline 3 | 32px    | 120%        |
| Headline 4 | 24px    | 120%        |
| Headline 5 | 18px    | 120%        |

**Body (Texto Corrido)**

Utiliza-se peso **Regular** para garantir leitura fluida:

| Body   | Tamanho | Line-height |
| ------ | ------- | ----------- |
| Body 1 | 18px    | 140%        |
| Body 2 | 16px    | 140%        |

**Botões e Links**

| Variante | Tamanho | Line-height |
| -------- | ------- | ----------- |
| X/Large  | 36px    | 40px        |
| Large    | 20px    | 24px        |
| Normal   | 20px    | 24px        |

**Campos e Inputs (Fields)**

| Elemento        | Tamanho | Line-height |
| --------------- | ------- | ----------- |
| Placeholder     | 16px    | 24px        |
| Título do Campo | 12px    | 16px        |

**Espaçamento Tipográfico**

**Desktop**

- **Entre Headline e parágrafo (texto corrido):**

  - Headline 1 → Body: 48px
  - Headline 2 → Body: 40px
  - Headline 3 → Body: 32px
  - Headline 4 → Body: 24px
  - Headline 5 → Body: 20px

- **Entre parágrafos de texto:** 20px
- **Entre blocos/seções:** 64px a 80px

**Mobile**

- **Entre Headline e parágrafo (texto corrido):**

  - Headline 1 → Body: 32px
  - Headline 2 → Body: 28px
  - Headline 3 → Body: 24px
  - Headline 4 → Body: 20px
  - Headline 5 → Body: 16px

- **Entre parágrafos de texto:** 16px
- **Entre blocos/seções:** 48px a 64px

A escolha da tipografia **Roboto** reforça os princípios de **acessibilidade, clareza e foco na experiência do usuário**. Ela sustenta a identidade visual da aplicação, equilibrando **elegância, funcionalidade e usabilidade**.

A definição precisa de **tamanhos, pesos, line-heights e espaçamentos** garante uma experiência **consistente, agradável e alinhada aos padrões atuais de design digital**.

### 3.3.3 Iconografia e imagens

Esta subseção (3.3.3) apresentará a iconografia que é utilizada no sistema e segue um estilo flat, amigável, com traços simples, cores suaves e bordas arredondadas, promovendo uma comunicação visual intuitiva, acessível e consistente. Além de reforçar a identidade visual da aplicação, os ícones facilitam a navegação e compreensão das funcionalidades, reduzindo a dependência de texto e tornando a interface mais acolhedora e didática.

Os ícones são majoritariamente representados por emojis, o que traz uma linguagem mais próxima, leve e humanizada, gerando empatia especialmente em públicos com menor letramento digital. Além disso, seu uso reforça a interpretação rápida de ações e estados de saúde.

**Tamanhos**

Os ícones são responsivos, se adaptando conforme a resolução e o contexto de uso, com variações nos tamanhos: 16, 24, 32, 64 e 128 pixels.

<div align="center"> <sub> Figura 16: Logo e suas cores</sub>
  
![Imagem de referência para iconografia](https://res.cloudinary.com/dkn9xjb1r/image/upload/fl_preserve_transparency/v1747927508/Design_sem_nome_gz6wgs.jpg)  
<sup> Fonte: Autoral, 2025</sup> </div> <br>

**Aplicação dos ícones**

| Ícone                  | Função / Local de uso                                          |
| ---------------------- | -------------------------------------------------------------- |
| 📊 Gráfico             | Visualizar estado geral de saúde do paciente.                  |
| 👥 Duas pessoas        | Exclusivo da interface do profissional – visualizar pacientes. |
| 📅 Calendário          | Seleção da data de nascimento no cadastro.                     |
| 📚 Livros              | Acesso ao material educativo.                                  |
| 🎓 Chapéu de formatura | Área de capacitação e treinamentos.                            |
| 📋 Prancheta           | Indicativo de informações gerais ou complementares.            |
| 📄 Papel               | Informações ou registros específicos.                          |
| 📤 Seta para cima      | Função de compartilhamento de informações.                     |
| 👵 Senhora             | Representação de exemplo de paciente.                          |
| ⚠️ Triângulo de alerta | Emergências ou alertas importantes.                            |
| 📷 Câmera              | Tirar ou enviar foto da ferida.                                |
| 🛏️ Cama                | Indicar condição atual do paciente.                            |
| 🔔 Sino                | Notificações e lembretes.                                      |
| 🦶 Pé                  | Marcar a localização da ferida no corpo.                       |
| 👃 Nariz               | Indicar presença de odor na ferida.                            |
| 😀🙂😐☹️😫 Carinhas    | Escala subjetiva para expressar o nível de dor ou bem-estar.   |
| ✏️ Lápis               | Edição de informações.                                         |
| 🔍 Lupa                | Função de pesquisa dentro do sistema.                          |
| 👨‍⚕️ Doutor              | Perfil do profissional de saúde (seleção rápida).              |
| 👤 Silhueta            | Perfil do usuário/paciente.                                    |
| 🏠 Casa                | Acesso à página inicial (Home).                                |
| 🗑️ Lixeira             | Excluir registros, fotos ou informações.                       |

O uso de emojis como base da iconografia aproxima a comunicação do cotidiano dos usuários, tornando-a mais empática e acessível. Além disso, reforça a clareza no uso das funcionalidades, especialmente para pacientes que podem ter dificuldade com interfaces tradicionais.

## 3.4 Protótipo de alta fidelidade

Esta subseção (3.4) apresenta o protótipo de alta fidelidade desenvolvido na Sprint 3, que reflete a interface final do sistema com alto nível de detalhamento visual e funcional. O protótipo foi construído com base nas validações obtidas nas etapas anteriores, considerando os feedbacks dos usuários, requisitos funcionais e não funcionais, além dos princípios de usabilidade e acessibilidade.

Segue abaixo algumas imagens de exemplo:

<div align="center"> <sub> Figura 17: Imagem do protótipo</sub>
  
![Imagem de referência para o protótipo](https://res.cloudinary.com/dkn9xjb1r/image/upload/fl_preserve_transparency/v1748557847/1_zptktx.jpg)  
<sup> Fonte: Autoral, 2025</sup> </div> <br>

<div align="center"> <sub> Figura 18: Imagem do protótipo</sub>
  
![Imagem de referência para o protótipo](https://res.cloudinary.com/dkn9xjb1r/image/upload/v1748558014/2_kn8vap.jpg)  
<sup> Fonte: Autoral, 2025</sup> </div> <br>

Caso deseje visualizar o protótipo na íntegra, acesse os links abaixo, de acordo com o dispositivo desejado. Recomendamos, caso necessário, utilizar a função de zoom do navegador para uma melhor visualização, especialmente em telas menores ou maiores que a escala padrão do protótipo.

- **Versão Mobile:** [Acessar Protótipo Mobile](https://rococo-pegasus-0b61c1.netlify.app/)
- **Versão Desktop:** [Acessar Protótipo Desktop](https://kaian-moura.github.io/desktopteste/newmockup.html)

O protótipo de alta fidelidade é uma etapa essencial no desenvolvimento de um produto digital, pois permite validar, de forma visual e interativa, como será a experiência final do usuário. Ele possibilita que os stakeholders, usuários e o time de desenvolvimento tenham uma visão clara de como o sistema irá se comportar, antecipando melhorias, ajustes de interface e usabilidade antes da implementação definitiva.

Além disso, o protótipo reduz retrabalho, melhora a comunicação entre as equipes e assegura que o produto final esteja alinhado às expectativas e necessidades dos usuários. Portanto, sua construção é um passo fundamental para garantir maior eficiência, qualidade e aderência às soluções propostas no projeto.

## 3.5. Modelagem do banco de dados (sprints 2 e 4)

Esta subseção (3.5) apresenta a modelagem do banco de dados, que tem como objetivo estruturar e organizar as informações que serão manipuladas pela aplicação, garantindo integridade, consistência e desempenho no armazenamento e na recuperação dos dados.

### 3.5.1. Modelo relacional (sprints 2 e 4)

Esta subseção (3.5.1) apresenta o modelo relacional do banco de dados, que descreve a estrutura lógica do sistema, detalhando os esquemas das tabelas, seus atributos e os relacionamentos entre elas. Além disso, também é apresentado o Diagrama Entidade-Relacionamento (DER), responsável por representar visualmente as entidades do sistema, seus atributos e as associações entre elas. Ademais, o modelo físico também é apresentado.

Essas representações são essenciais para garantir a integridade e a consistência dos dados, além de orientar a implementação da persistência das informações na solução.

**DIAGRAMA RELACIONAL**

O diagrama relacional contempla todas as entidades relevantes ao contexto da aplicação, como patient, health_agent, wound, home_visit, educational_material, emergency, entre outras. Cada tabela possui um identificador único (id) e colunas que representam atributos específicos daquela entidade.

As relações entre as tabelas são representadas por chaves estrangeiras, como por exemplo:

- A entidade wound possui uma chave estrangeira patient_id, indicando a qual paciente a ferida pertence;

- A tabela wound_monitoring está relacionada tanto com a entidade wound quanto com health_agent, representando o monitoramento feito por determinado agente;

- A entidade photo_record relaciona-se com a ferida registrada, incluindo metadados como o tamanho da ferida em milímetros e se a foto foi validada por um agente.

Esse modelo relacional visa suportar as operações da aplicação de forma robusta e organizada, facilitando consultas complexas e mantendo a rastreabilidade dos dados clínicos e operacionais.

A seguir, apresenta-se o diagrama relacional completo:

<div align="center"> <sub> Figura 19: Modelo Relacional </sub>

![Modelo Relacional - Tabelas](https://res.cloudinary.com/dt8qannja/image/upload/v1749662771/Untitled_4__page-0001_l7npn9.jpg)
<sup> Fonte: Autoral, 2025</sup> </div> <br>

A seguir, apresenta-se as conexões do diagrama relacional:

<div align="center"> <sub> Figura 20: Conexões do modelo relacional </sub>

![Modelo Relacional - Tabelas](https://res.cloudinary.com/dt8qannja/image/upload/v1749662772/Untitled_4__page-0002_r7zpti.jpg)
<sup> Fonte: Autoral, 2025</sup> </div> <br>

**DIAGRAMA ENTIDADE-RELACIONAMENTO**

O Diagrama Entidade-Relacionamento (DER), apresentado abaixo, é uma representação gráfica que descreve as entidades envolvidas no sistema de telemonitoramento de feridas, bem como os relacionamentos entre elas.

Esse diagrama tem como objetivo facilitar a compreensão da estrutura do banco de dados, evidenciando como os dados estão organizados e interligados. Ele serve como base conceitual para a implementação do modelo relacional, promovendo uma visão clara da lógica de negócios e garantindo que todos os requisitos de persistência e integridade das informações sejam atendidos.

Entidades como `photo_record`, `recommended_material` e `home_visit` **não estão representadas no DER**, pois são **tabelas associativas** (ou tabelas de conexão) que modelam relacionamentos muitos-para-muitos e auxiliam no detalhamento das interações. Elas fazem parte do **modelo relacional**, voltado para a implementação do sistema.

<div align="center"> <sub> Figura 21: Diagrama Entidade-Relacionamento (DER) </sub>

![Modelo Relacional Tabelas](https://res.cloudinary.com/dt8qannja/image/upload/v1748956017/N1_1_cq2bqj.png)
<sup> Fonte: Autoral, 2025</sup> </div> <br>

**Resumo das Cardinalidades (Notações 1:N, N:1, N:N)**

| Relacionamento                      | Cardinalidade | Significado                                                                                        |
| ----------------------------------- | ------------- | -------------------------------------------------------------------------------------------------- |
| patient — wound                     | 1:N           | Um paciente pode ter várias feridas                                                                |
| patient — patient_comorbidity       | 1:N           | Um paciente pode ter várias comorbidades                                                           |
| patient — questionnaire             | 1:N           | Um paciente pode responder vários questionários                                                    |
| patient — emergency                 | 1:N           | Um paciente pode ter vários registros de emergência                                                |
| patient — system_notification       | 1:N           | Um paciente pode receber várias notificações do sistema                                            |
| health_agent — health_unit          | N:1           | Cada agente de saúde pertence a uma UBS                                                            |
| health_agent — emergency            | 1:N           | Um agente pode atender várias emergências                                                          |
| health_agent — wound                | N:N           | Um agente pode acompanhar várias feridas e uma ferida pode ser acompanhada por vários agentes      |
| health_agent — educational_material | N:N           | Um agente pode recomendar vários materiais e cada material pode ser recomendado por vários agentes |
| patient — health_agent_visit        | N:N           | Um paciente pode ser visitado por vários agentes e um agente pode visitar vários pacientes         |
| wound — educational_material        | N:N           | Uma ferida pode ter vários materiais educativos associados e vice-versa                            |

**MODELO FÍSICO**

Para a construção do Modelo Físico, o modelo lógico foi traduzido para comandos SQL.

O modelo físico do banco de dados corresponde à implementação concreta da estrutura lógica em um Sistema de Gerenciamento de Banco de Dados (SGBD). Ele especifica, por meio de instruções SQL, a criação das tabelas, os tipos de dados de cada atributo, as chaves primárias e estrangeiras, bem como as restrições de integridade necessárias. Essa etapa é fundamental para garantir que o banco de dados, em ambiente de produção, reflita com precisão as regras de negócio definidas na modelagem lógica.

O arquivo com o Modelo Físico do Banco de Dados pode ser acessado em: <a href="https://github.com/Inteli-College/2025-1B-T19-IN02-G04/tree/main/src/migrations/production">Modelo Físico</a>.

### 3.5.2. Consultas SQL e lógica proposicional

Nesta subseção (3.5.2), são apresentadas as principais consultas SQL compostas desenvolvidas e implementadas no back-end da aplicação web. Cada consulta é acompanhada de sua respectiva formulação em lógica proposicional, utilizando o template proposto para facilitar a compreensão da relação entre a linguagem de consulta e os conceitos lógicos subjacentes.

| #1                                 | ---                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                |
| ---------------------------------- | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **Expressão SQL**                  | SELECT \* FROM paciente WHERE (tem_dificuldade_locomocao = TRUE AND sexo = 'Feminino') OR (tem_dificuldade_visual = TRUE AND sexo = 'Feminino');                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   |
| **Proposições lógicas**            | $A$: tem_dificuldade_locomocao = TRUE <br> $B$: sexo = 'Feminino'<br> $C$: tem_dificuldade_visual = TRUE                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           |
| **Expressão lógica proposicional** | $(A \land B) \lor (C \land B)$                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     |
| **Tabela Verdade**                 | <table> <thead> <tr> <th>$A$</th> <th>$B$</th> <th>$C$</th> <th>$(A \land B)$</th> <th>$(C \land B)$</th> <th>$(A \land B) \lor (C \land B)$</th> </tr> </thead> <tbody> <tr> <td>F</td> <td>F</td> <td>F</td> <td>F</td> <td>F</td> <td>F</td> </tr> <tr> <td>F</td> <td>F</td> <td>V</td> <td>F</td> <td>F</td> <td>F</td> </tr> <tr> <td>F</td> <td>V</td> <td>F</td> <td>F</td> <td>F</td> <td>F</td> </tr> <tr> <td>F</td> <td>V</td> <td>V</td> <td>F</td> <td>V</td> <td>V</td> </tr> <tr> <td>V</td> <td>F</td> <td>F</td> <td>F</td> <td>F</td> <td>F</td> </tr> <tr> <td>V</td> <td>F</td> <td>V</td> <td>F</td> <td>F</td> <td>F</td> </tr> <tr> <td>V</td> <td>V</td> <td>F</td> <td>V</td> <td>F</td> <td>V</td> </tr> <tr> <td>V</td> <td>V</td> <td>V</td> <td>V</td> <td>V</td> <td>V</td> </tr> </tbody> </table> |

| #2                                 | ---                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         |
| ---------------------------------- | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **Expressão SQL**                  | UPDATE FROM ferida SET status = 'Grave' WHERE nivel_gravidade >= 4 OR (status = 'Sem melhora' AND data_cadastro BETWEEN CURRENT_DATE - INTERVAL '30 dias' AND CURRENT_DATE - INTERVAL '15 dias');                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           |
| **Proposições lógicas**            | $A$: nivel_gravidade ≥ 4 <br> $B$: status = 'Sem melhora' <br> $C$: data_cadastro entre 30 e 15 dias atrás                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  |
| **Expressão lógica proposicional** | $A \lor (B \land C)$                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        |
| **Tabela Verdade**                 | <table> <thead> <tr> <th>$A$</th> <th>$B$</th> <th>$C$</th> <th>$(B \land C)$</th> <th>$(A \lor (B \land C))$</th> </tr> </thead> <tbody> <tr> <td>F</td> <td>F</td> <td>F</td> <td>F</td> <td>F</td> </tr> <tr> <td>F</td> <td>F</td> <td>V</td> <td>F</td> <td>F</td> </tr> <tr> <td>F</td> <td>V</td> <td>F</td> <td>F</td> <td>F</td> </tr> <tr> <td>F</td> <td>V</td> <td>V</td> <td>V</td> <td>V</td> </tr> <tr> <td>V</td> <td>F</td> <td>F</td> <td>F</td> <td>V</td> </tr> <tr> <td>V</td> <td>F</td> <td>V</td> <td>F</td> <td>V</td> </tr> <tr> <td>V</td> <td>V</td> <td>F</td> <td>F</td> <td>V</td> </tr> <tr> <td>V</td> <td>V</td> <td>V</td> <td>V</td> <td>V</td> </tr> </tbody> </table> |

| #3                                 | ---                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        |
| ---------------------------------- | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **Expressão SQL**                  | DELETE FROM registro_foto WHERE enviado_por_paciente = TRUE AND validado_por_agente = FALSE AND data_foto BETWEEN CURRENT_DATE - INTERVAL '15 dias' AND CURRENT_DATE;                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      |
| **Proposições lógicas**            | $A$: enviado_por_paciente = TRUE <br> $B$: validado_por_agente = FALSE <br> $C$: data_foto entre CURRENT_DATE - 15 dias e CURRENT_DATE                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     |
| **Expressão lógica proposicional** | $A \land B \land C$                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        |
| **Tabela Verdade**                 | <table> <thead> <tr> <th>$A$</th> <th>$B$</th> <th>$C$</th> <th>$(A \land B)$</th> <th>$(A \land B \land C)$</th> </tr> </thead> <tbody> <tr> <td>F</td> <td>F</td> <td>F</td> <td>F</td> <td>F</td> </tr> <tr> <td>F</td> <td>F</td> <td>V</td> <td>F</td> <td>F</td> </tr> <tr> <td>F</td> <td>V</td> <td>F</td> <td>F</td> <td>F</td> </tr> <tr> <td>F</td> <td>V</td> <td>V</td> <td>F</td> <td>F</td> </tr> <tr> <td>V</td> <td>F</td> <td>F</td> <td>F</td> <td>F</td> </tr> <tr> <td>V</td> <td>F</td> <td>V</td> <td>F</td> <td>F</td> </tr> <tr> <td>V</td> <td>V</td> <td>F</td> <td>V</td> <td>F</td> </tr> <tr> <td>V</td> <td>V</td> <td>V</td> <td>V</td> <td>V</td> </tr> </tbody> </table> |

| #4                                 | ---                                                                                                                                                                                                                                                                                           |
| ---------------------------------- | --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **Expressão SQL**                  | SELECT \* FROM agente_saude WHERE tipo_agente <> 'visitador' OR telefone LIKE '11%';                                                                                                                                                                                                          |
| **Proposições lógicas**            | $A$: tipo_agente ≠ 'visitador' <br> $B$: telefone LIKE '11%'                                                                                                                                                                                                                                  |
| **Expressão lógica proposicional** | $A \lor B$                                                                                                                                                                                                                                                                                    |
| **Tabela Verdade**                 | <table> <thead> <tr> <th>$A$</th> <th>$B$</th> <th>$(A \lor B)$</th> </tr> </thead> <tbody> <tr> <td>F</td> <td>F</td> <td>F</td> </tr> <tr> <td>F</td> <td>V</td> <td>V</td> </tr> <tr> <td>V</td> <td>F</td> <td>V</td> </tr> <tr> <td>V</td> <td>V</td> <td>V</td> </tr> </tbody> </table> |

| #5                                 | ---                                                                                                                                                                                                                                                                                            |
| ---------------------------------- | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **Expressão SQL**                  | SELECT \* FROM emergencia WHERE atendida = FALSE AND motivo NOT IN ('queda', 'ferida grave', 'desmaio', 'acionamento acidental');                                                                                                                                                              |
| **Proposições lógicas**            | $A$: atendida = FALSE <br> $B$: motivo ∉ ('queda', 'ferida grave', 'desmaio', 'acionamento acidental')                                                                                                                                                                                         |
| **Expressão lógica proposicional** | $A \land B$                                                                                                                                                                                                                                                                                    |
| **Tabela Verdade**                 | <table> <thead> <tr> <th>$A$</th> <th>$B$</th> <th>$(A \land B)$</th> </tr> </thead> <tbody> <tr> <td>F</td> <td>F</td> <td>F</td> </tr> <tr> <td>F</td> <td>V</td> <td>F</td> </tr> <tr> <td>V</td> <td>F</td> <td>F</td> </tr> <tr> <td>V</td> <td>V</td> <td>V</td> </tr> </tbody> </table> |

## 3.6. WebAPI e endpoints

Esta subseção (3.6) apresenta os endpoints desenvolvidos para o sistema, descrevendo as funcionalidades disponíveis por meio da WebAPI. Cada endpoint está associado a uma funcionalidade específica do sistema e é acessado por meio de métodos HTTP (GET, POST, PUT, DELETE), contendo as devidas informações de rota, cabeçalhos (headers), corpo da requisição (body), e formatos esperados de resposta (response).

Além disso, é disponibilizado um link para o arquivo rest.http, que centraliza todas as requisições implementadas, facilitando o acesso e testes por parte da equipe técnica ou avaliadora.

**Agentes de Saúde**

- **GET** `/health-agents` – Lista todos os agentes de saúde
- **GET** `/health-agents/:id` – Obtém um agente específico pelo ID

**Materiais Educacionais**

- **GET** `/materials` – Lista todos os materiais educacionais
- **GET** `/materials/:id` – Obtém um material educacional específico pelo ID

**Emergências**

- **GET** `/emergencys` – Lista todas as emergências
- **GET** `/emergencys/:id` – Obtém uma emergência específica pelo ID

**UBSs**

- **GET** `/ubs` – Lista todas as UBSs
- **GET** `/ubs/:id` – Obtém uma UBS específica pelo ID

**Pacientes**

- **GET** `/patients` – Lista todos os pacientes
- **GET** `/patients/:id` – Obtém um paciente específico pelo ID
- **POST** `/patients` – Cria um novo paciente
- **PUT** `/patients/:id` – Atualiza um paciente existente
- **DELETE** `/patients/:id` – Remove um paciente

**Registro de Fotos**

- **GET** `/photo-records` – Lista todos os registros de fotos
- **GET** `/photo-records/:id` – Obtém um registro de foto específico pelo ID
- **POST** `/photo-records` – Cria um novo registro de foto
- **PUT** `/photo-records/:id` – Atualiza um registro de foto existente
- **DELETE** `/photo-records/:id` – Remove um registro de foto

**Feridas**

- **GET** `/wounds` – Lista todas as feridas
- **GET** `/wounds/:id` – Obtém uma ferida específica pelo ID
- **POST** `/wounds` – Cria uma nova ferida
- **PUT** `/wounds/:id` – Atualiza uma ferida existente
- **DELETE** `/wounds/:id` – Remove uma ferida

**Comorbidades dos Pacientes**

- **GET** `/comorbidities` – Lista todas as comorbidades
- **GET** `/comorbidities/:id` – Obtém uma comorbidade específica pelo ID
- **POST** `/comorbidities` – Cria uma nova comorbidade para um paciente
- **PUT** `/comorbidities/:id` – Atualiza uma comorbidade existente

**Questionários dos Pacientes**

- **GET** `/patient-questionnaire` – Lista todos os questionários
- **GET** `/patient-questionnaire/:id` – Obtém um questionário específico pelo ID
- **POST** `/patient-questionnaire` – Cria um novo questionário
- **PUT** `/patient-questionnaire/:id` – Atualiza um questionário existente

**Acompanhamento de Feridas**

- **GET** `/wound-monitoring` – Lista todos os acompanhamentos de feridas
- **GET** `/wound-monitoring/:id` – Obtém um acompanhamento específico pelo ID
- **POST** `/wound-monitoring` – Cria um novo acompanhamento de ferida
- **PUT** `/wound-monitoring/:id` – Atualiza um acompanhamento existente
- **DELETE** `/wound-monitoring/:id` – Remove um acompanhamento

**Notificações do Sistema**

- **GET** `/notifications` – Lista todas as notificações
- **GET** `/notifications/:id` – Obtém uma notificação específica pelo ID
- **POST** `/notifications` – Cria uma nova notificação
- **PUT** `/notifications/:id` – Atualiza uma notificação existente
- **DELETE** `/notifications/:id` – Remove uma notificação

Para visualizar e testar todas as requisições da API, acesse o link abaixo e abra o arquivo `rest.http`:

[Requisições completas de cada endpoint](rest.http)

Também é possível acessar e realizar as requisições pelo link: <http://localhost:3000/>.Entretanto, é necessário conectar ao banco de dados e inicializar o servidor primeiro.

# <a name="c4"></a>4. Desenvolvimento da Aplicação Web

A seção 4 descreve o processo de desenvolvimento da aplicação web, detalhando as principais decisões técnicas, ferramentas utilizadas e funcionalidades implementadas ao longo do projeto.

## 4.1. Primeira versão da aplicação web

Esta subseção (4.1) apresentará a primeira versão funcional da aplicação web desenvolvida.

Durante esta sprint, desenvolvemos a primeira versão funcional do sistema web com foco na implementação completa do padrão MVC (Model-View-Controller) para todas as tabelas do projeto.

Foram criados os controllers, services, models e rotas para as seguintes entidades: `paciente`, `ubs`, `registro_foto`, `agente_saude`, `questionario_paciente`, `ferida`, `comorbidade_paciente`, `notificacao_sistema`, `emergencia`, `material_educativo` e `acompanhamento_ferida`. Cada uma dessas entidades possui rotas RESTful padronizadas para as operações de CRUD: criação, leitura, atualização e remoção de registros.

**Requisições com REST Client**

Além da estrutura MVC, também elaboramos um arquivo `rest.http` que concentra as requisições de teste para cada uma das entidades do sistema. Esse arquivo foi essencial para testar rapidamente os endpoints da API durante o desenvolvimento.

Por exemplo, para cadastrar um novo paciente utilizamos o seguinte trecho:

```
POST http://localhost:3000/api/patients
Content-Type: application/json

{
  "nome_completo": "Raiane Leonor De Castro",
  "data_nascimento": "2001-01-01",
  "sexo": "Feminino",
  "endereco": "Rua das Flores, 123",
  "telefone": "11999999999",
  "nivel_escolaridade": "Ensino Médio",
  "tem_dificuldade_locomocao": false,
  "tem_dificuldade_visual": false,
  "data_cadastro": "2025-05-28"
}
```

Esse tipo de requisição nos permite validar diretamente se as rotas estão funcionando corretamente, retornando os dados esperados ou mensagens de erro adequadas.

Isso foi desenvolvido para todas as rotas, que são:

- **GET** `/health-agents` – Lista todos os agentes de saúde
- **GET** `/health-agents/:id` – Obtém um agente específico pelo ID
- **GET** `/materials` – Lista todos os materiais educacionais
- **GET** `/materials/:id` – Obtém um material educacional específico pelo ID
- **GET** `/emergencys` – Lista todas as emergências
- **GET** `/emergencys/:id` – Obtém uma emergência específica pelo ID
- **GET** `/ubs` – Lista todas as UBSs
- **GET** `/ubs/:id` – Obtém uma UBS específica pelo ID
- **GET** `/patients` – Lista todos os pacientes
- **GET** `/patients/:id` – Obtém um paciente específico pelo ID
- **POST** `/patients` – Cria um novo paciente
- **PUT** `/patients/:id` – Atualiza um paciente existente
- **DELETE** `/patients/:id` – Remove um paciente
- **GET** `/photo-records` – Lista todos os registros de fotos
- **GET** `/photo-records/:id` – Obtém um registro de foto específico pelo ID
- **POST** `/photo-records` – Cria um novo registro de foto
- **PUT** `/photo-records/:id` – Atualiza um registro de foto existente
- **DELETE** `/photo-records/:id` – Remove um registro de foto
- **GET** `/wounds` – Lista todas as feridas
- **GET** `/wounds/:id` – Obtém uma ferida específica pelo ID
- **POST** `/wounds` – Cria uma nova ferida
- **PUT** `/wounds/:id` – Atualiza uma ferida existente
- **DELETE** `/wounds/:id` – Remove uma ferida
- **GET** `/comorbidities` – Lista todas as comorbidades
- **GET** `/comorbidities/:id` – Obtém uma comorbidade específica pelo ID
- **POST** `/comorbidities` – Cria uma nova comorbidade para um paciente
- **PUT** `/comorbidities/:id` – Atualiza uma comorbidade existente
- **GET** `/patient-questionnaire` – Lista todos os questionários
- **GET** `/patient-questionnaire/:id` – Obtém um questionário específico pelo ID
- **POST** `/patient-questionnaire` – Cria um novo questionário
- **PUT** `/patient-questionnaire/:id` – Atualiza um questionário existente
- **GET** `/wound-monitoring` – Lista todos os acompanhamentos de feridas
- **GET** `/wound-monitoring/:id` – Obtém um acompanhamento específico pelo ID
- **POST** `/wound-monitoring` – Cria um novo acompanhamento de ferida
- **PUT** `/wound-monitoring/:id` – Atualiza um acompanhamento existente
- **DELETE** `/wound-monitoring/:id` – Remove um acompanhamento
- **GET** `/notifications` – Lista todas as notificações
- **GET** `/notifications/:id` – Obtém uma notificação específica pelo ID
- **POST** `/notifications` – Cria uma nova notificação
- **PUT** `/notifications/:id` – Atualiza uma notificação existente
- **DELETE** `/notifications/:id` – Remove uma notificação

Abaixo é possível visualizar requisição de cadastrar novos pacientes por meio do front-end desenvolvido, que será melhor explicado após a imagem.

<div align="center"> <sub> Figura 21: Cadastrar novos pacientes</sub>

![Front-end](https://res.cloudinary.com/dwewomj84/image/upload/v1748654194/Captura_de_tela_2025-05-30_213519_im4wcz.png)
<sup> Fonte: Autoral, 2025</sup> </div> <br>

**Interface Web para validação das rotas**

Para facilitar os testes visuais e garantir que os dados estavam sendo exibidos corretamente, desenvolvemos um protótipo de front-end simples. Essa interface nos permitiu fazer chamadas à API e verificar as respostas de forma mais amigável do que apenas no terminal ou em arquivos REST.

<div align="center"> <sub> Figura 22: Interface do front-end </sub>

![Front-end](https://res.cloudinary.com/dwewomj84/image/upload/v1748653498/Captura_de_tela_2025-05-30_213403_vrz1ic.png)
<sup> Fonte: Autoral, 2025</sup> </div> <br>

O front possui funções que fazem requisições `GET` para listar dados, como agentes de saúde e pacientes. Veja um exemplo da função usada para buscar agentes de saúde:

```javascript
async function fetchHealthAgents() {
  showLoading("loading-agents");
  try {
    const response = await fetch("/api/health-agents");
    const data = await response.json();

    const container = document.getElementById("health-agents-container");
    container.innerHTML = "";

    data.forEach((agent) => {
      const card = document.createElement("div");
      card.className = "card";
      card.innerHTML = `<h3>${agent.nome_completo || "Nome não disponível"}</h3>
                <p>ID: ${agent.id}</p>
                <p>Tipo: ${agent.tipo_agente || "Não especificada"}</p>
                <p>UBS: ${agent.ubs_id || "Não associada"}</p>`;
      container.appendChild(card);
    });
  } catch (error) {
    console.error("Erro ao buscar agentes:", error);
    document.getElementById("health-agents-container").innerHTML =
      "<p>Erro ao carregar os dados. Verifique o console para mais detalhes.</p>";
  } finally {
    hideLoading("loading-agents");
  }
}
```

Essa função usa `fetch` para consultar a API e cria dinamicamente cards com as informações recebidas. Também exibe mensagens de erro e indicadores de carregamento, contribuindo para a usabilidade do protótipo.

Abaixo é possível visualizar a tela após a requisção de buscar agentes de saúde ser realizada, com os dados correspondentes:

<div align="center"> <sub> Figura 23: Requisição dos agentes de saúde</sub>

![Front-end](https://res.cloudinary.com/dwewomj84/image/upload/v1748653923/Captura_de_tela_2025-05-30_213438_qdscdw.png)
<sup> Fonte: Autoral, 2025</sup> </div> <br>

**Desafios:**

Durante o desenvolvimento dessa funcionalidade, enfrentamos alguns desafios técnicos, como:

- Garantir que os dados fossem validados corretamente antes de serem inseridos no banco.
- Testar todos os endpoints repetidamente para garantir sua estabilidade.
- Estruturar o MVC com validações e corretamente.

**Sucessos:**

- Todas as rotas de CRUD funcionaram corretamente com os testes manuais e automatizados.
- A estrutura em MVC trouxe organização e facilitou a manutenção do código.
- O protótipo de front-end ajudou na validação visual das funcionalidades.

**Próximos passos:**

- Validar melhor os dados recebidos para garantir integridade e segurança.
- Finalizar e integrar todos os módulos no sistema completo.

## 4.2. Segunda versão da aplicação web

Esta subseção (4.2) apresentará a segunda versão funcional da aplicação web desenvolvida.

Durante esta sprint, o foco principal foi o desenvolvimento robusto e completo do frontend da aplicação web, visando atender a dois perfis de usuário distintos: **Agentes de Saúde (Profissionais)** e **Pacientes**. A arquitetura foi concebida para ser responsiva, garantindo uma experiência de usuário otimizada tanto em desktops quanto em dispositivos móveis, com uma abordagem "desktop-first" para o design.

#### Frontend para Agentes de Saúde

Para os Agentes de Saúde, foram desenvolvidas as seguintes páginas HTML, todas interligadas através de uma barra lateral de navegação (sidebar) e uma barra de navegação móvel na parte inferior, garantindo funcionalidade e consistência visual:

- **Login (login-agente.html):** Interface para autenticação do agente.
- **Cadastro (cadastro-agente.html):** Formulário para registro de novos agentes.
- **Menu Principal (menu-principal-agente.html):** Dashboard inicial com acesso rápido às principais funcionalidades.
- **Gerenciar Pacientes (gerenciar-pacientes.html):** Uma interface centralizada para que o agente possa visualizar, buscar e gerenciar a lista de pacientes. Esta página é projetada para interagir com o backend para buscar e exibir dados de pacientes, permitindo filtros e ordenação.
- **Análise de Dados de Saúde (analise-dados-saude.html):** Um dashboard visual para que o agente possa analisar estatísticas e tendências de saúde dos pacientes, como progresso de cicatrização e incidência de comorbidades. Esta página simula a exibição de dados agregados provenientes do backend.
- **Consultas (consultas-agente.html):** Gerenciamento de agendamentos e histórico de consultas dos pacientes.
- **Tutorial (tutorial-agente.html):** Guias e instruções de uso da plataforma para o agente.
- **Enviar Foto (enviar-foto-agente.html):** Interface para o agente fazer upload de imagens relacionadas aos pacientes (ex: evolução de feridas), com campos para metadados. Esta funcionalidade prevê a integração com um serviço de armazenamento de arquivos no backend.
- **Vídeos (videos-agente.html):** Biblioteca de vídeos educativos ou de treinamento para agentes.
- **Perfil (perfil-agente.html):** Visualização e edição das informações do perfil do agente.
- **Notificações (notificacoes-agente.html):** Central de alertas e mensagens importantes para o agente, como novas emergências ou atualizações de pacientes. Esta página buscaria notificações em tempo real do backend.
- **Emergência (emergencia-agente.html):** Painel de atendimento a situações de emergência, exibindo alertas críticos de pacientes e permitindo ações rápidas (ex: acionar SAMU, contatar paciente). Esta página demonstra a capacidade de exibir dados urgentes do backend.

­
­­­­<div align="center"> <sub> Figura 24: Páginas criadas em HTML para o front dos Agentes de saúde </sub>

![Codigos - Agente](https://res.cloudinary.com/dxutpx5b1/image/upload/v1749847613/image_xr4swo.png)

<sup> Fonte: Autoral, 2025</sup> </div> <br>

Todas as páginas foram desenvolvidas reutilizando componentes e o `global.css` para manter a identidade visual, incluindo a cor principal (`#013c6d`), tipografia e elementos como cards e botões. A integração com o backend é simulada através de chamadas `fetch` (conforme exemplificado na seção 4.1), onde o frontend envia requisições e exibe as respostas JSON recebidas.

<div align="center"> <sub> Figura 25: Tela do Frontend dos Agentes de saúde (Menu Principal) </sub>

![Front-end Agente - Menu Principal](https://res.cloudinary.com/dxutpx5b1/image/upload/v1749847404/image_zcngki.png)

<sup> Fonte: Autoral, 2025</sup> </div> <br>

<div align="center"> <sub> Figura 26: Tela do Frontend do Agente de saúde (Gerenciar Pacientes) </sub>

![Front-end Agente - Gerenciar Pacientes](https://res.cloudinary.com/dxutpx5b1/image/upload/v1749847468/image_ajc9yj.png)

<sup> Fonte: Autoral, 2025</sup> </div> <br>

#### Frontend para Pacientes

Seguindo o mesmo padrão visual e de navegação dos agentes, o frontend para pacientes foi adaptado para oferecer uma experiência focada no autocuidado e acompanhamento pessoal. As páginas desenvolvidas incluem:

- **Login (login-paciente.html):** Interface para autenticação do paciente.
- **Cadastro (cadastro-paciente.html):** Formulário para registro de novos pacientes.
- **Menu Principal (menu-principal.html):** Dashboard inicial com informações relevantes para o paciente.
- **Saúde (saude-paciente.html):** Dashboard pessoal com progresso de cicatrização, histórico de saúde e recomendações personalizadas. Esta página exibiria dados específicos do paciente obtidos do backend.
- **Consultas (consultas-paciente.html):** Gerenciamento de agendamentos e histórico de consultas pessoais do paciente.
- **Tutorial (tutorial-paciente.html):** Guias e instruções de uso da plataforma para o paciente.
- **Enviar Foto (enviar-foto-paciente.html):** Interface para o paciente fazer upload de fotos de sua ferida, com formulário detalhado para acompanhamento. Esta funcionalidade também se integraria a um serviço de backend para armazenamento de imagens.
- **Vídeos (videos-paciente.html):** Biblioteca de vídeos educativos sobre cuidados com a saúde e feridas.
- **Perfil (perfil-paciente.html):** Visualização e edição das informações do perfil do paciente.
- **Notificações (notificacoes-paciente.html):** Central de alertas e mensagens importantes para o paciente, como lembretes de consulta ou atualizações sobre seu caso.
- **Emergência (emergencia.html):** Página para o paciente reportar uma situação de emergência, com opções para contato rápido. Esta página enviaria dados de emergência para o backend.
- **Como Usar (como-usar.html):** Um guia passo a passo sobre como utilizar a aplicação, desde o cadastro até o envio de fotos e acompanhamento.

<div align="center"> <sub> Figura 27: Tela do Frontend do Paciente (Gerenciar Pacientes) </sub>

![Codigos - Paciente](https://res.cloudinary.com/dxutpx5b1/image/upload/v1749848794/image_ma8imy.png)

<sup> Fonte: Autoral, 2025</sup> </div> <br>

<div align="center"> <sub> Figura 28: Tela do Frontend do Paciente (Menu Principal) </sub>

![Front-end Paciente - Menu Principal](https://res.cloudinary.com/dxutpx5b1/image/upload/v1749846808/image_n4nwqi.png)

<sup> Fonte: Autoral, 2025</sup> </div> <br>

 <div align="center"> <sub> Figura 29: Tela do Frontend do Paciente (Saúde) </sub>

![Front-end Paciente - Saúde](https://res.cloudinary.com/dxutpx5b1/image/upload/v1749846894/image_dckepu.png)

<sup> Fonte: Autoral, 2025</sup> </div> <br>

#### Acessando o Sistema

Para testar o sistema, você pode criar uma nova conta de paciente ou usar as credenciais pré-configuradas abaixo:

#### Credenciais de Acesso

| Tipo de Usuário           | Login          | Senha    |
| ------------------------- | -------------- | -------- |
| **Profissional da Saúde** | 45678912300    | senha789 |
| **Paciente**              | 123.456.789-00 | senha123 |

> **Nota:** Recomendamos criar uma nova conta de paciente para explorar a experiência completa de cadastro.

#### Desafios e Soluções

Durante o desenvolvimento do frontend, enfrentamos alguns desafios, principalmente relacionados à manutenção da consistência da navegação e à correção de erros de digitação e estrutura HTML. Problemas como links que não funcionavam ou itens de menu que desapareciam foram identificados e corrigidos iterativamente. A principal solução envolveu a revisão minuciosa do código HTML de cada página, garantindo que todos os elementos de navegação (`<a>` e `<li>`) estivessem corretamente aninhados e apontando para os arquivos HTML corretos. A padronização da estrutura da sidebar e da barra de navegação móvel em todas as páginas foi crucial para resolver esses problemas.

#### Próximos Passos

Com o frontend agora completo e funcional para ambos os perfis de usuário, os próximos passos incluem aprofundar a integração com o backend para que as funcionalidades de exibição e manipulação de dados sejam totalmente operacionais. Isso envolverá a implementação de APIs RESTful completas para todas as operações de CRUD em cada entidade, bem como a validação de dados e o tratamento de erros em tempo real no frontend.

## 4.3. Versão final da aplicação web (sprint 5)

_Descreva e ilustre aqui o desenvolvimento da última versão do sistema web, explicando brevemente o que foi entregue em termos de código e sistema. Utilize prints de tela para ilustrar. Indique as eventuais dificuldades e próximos passos._

# <a name="c5"></a>5. Testes

## 5.1. Relatório de testes de integração de endpoints automatizados

#### Visão Geral dos Testes

Com base nos resultados dos testes automatizados executados, nossa aplicação possui uma suíte abrangente de testes unitários para os serviços de negócio, com **cobertura de 100%** em todas as métricas (statements, branches, functions e lines).

#### Resultados da Execução dos Testes

```
Test Suites: 11 passed, 11 total
Tests:       50 passed, 50 total
Snapshots:   0 total
Time:        2.572 s
```

#### Cobertura de Código

| File                           | % Stmts | % Branch | % Funcs | % Lines | Uncovered Line #s |
| ------------------------------ | ------- | -------- | ------- | ------- | ----------------- |
| **All files**                  | **100** | **100**  | **100** | **100** |                   |
| educationalMaterialService.js  | 100     | 100      | 100     | 100     |                   |
| emergencyService.js            | 100     | 100      | 100     | 100     |                   |
| healthAgentService.js          | 100     | 100      | 100     | 100     |                   |
| healthUnitService.js           | 100     | 100      | 100     | 100     |                   |
| patientComorbidityService.js   | 100     | 100      | 100     | 100     |                   |
| patientQuestionnaireService.js | 100     | 100      | 100     | 100     |                   |
| patientService.js              | 100     | 100      | 100     | 100     |                   |
| photoRecordService.js          | 100     | 100      | 100     | 100     |                   |
| systemNotificationService.js   | 100     | 100      | 100     | 100     |                   |
| woundMonitoringService.js      | 100     | 100      | 100     | 100     |                   |
| woundsService.js               | 100     | 100      | 100     | 100     |                   |

## Detalhamento dos Testes por Serviço

### 1. PatientQuestionnaireService (7 testes)

**Funcionalidades testadas:**

- Retorno de todos os questionários
- Busca de questionário por ID
- Tratamento de erro quando questionário não é encontrado
- Criação de novo questionário
- Validação de dados inválidos na criação
- Atualização de questionário existente
- Validação de dados inválidos na atualização

**Validações específicas:**

- Nível de dor deve estar entre 0-5
- Campos obrigatórios são validados
- Tratamento de registros não encontrados

### 2. PatientComorbidityService (7 testes)

**Funcionalidades testadas:**

- Retorno de todas as comorbidades
- Busca de comorbidade por ID
- Criação de nova comorbidade
- Validação de dados inválidos na criação
- Atualização de comorbidade existente
- Validação de dados inválidos na atualização
- Tratamento de erro quando comorbidade não é encontrada

**Validações específicas:**

- Tipo de comorbidade deve ser string
- Data de diagnóstico é obrigatória
- Validação de tipos de dados

### 3. PhotoRecordService (5 testes)

**Funcionalidades testadas:**

- Retorno de lista de registros fotográficos
- Busca de registro fotográfico por ID
- Criação de novo registro fotográfico
- Atualização de registro fotográfico existente
- Exclusão de registro fotográfico existente

**Campos validados:**

- Data da foto
- Caminho do arquivo
- Tamanho da ferida em mm
- Status de validação pelo agente

### 4. WoundsService (7 testes)

**Funcionalidades testadas:**

- Retorno de lista de ferimentos
- Busca de ferimento por ID
- Criação de novo ferimento
- Validação de dados inválidos na criação
- Atualização de ferimento existente
- Validação de dados inválidos na atualização
- Exclusão de ferimento existente

**Validações específicas:**

- Tipo de ferimento é obrigatório
- Localização anatômica deve ser informada
- Nível de severidade (1-3)
- Status da ferida (ativa, em cicatrização, cicatrizada)

### 5. SystemNotificationService (3 testes)

**Funcionalidades testadas:**

- Retorno de lista de notificações
- Busca de notificação por ID
- Criação de nova notificação

**Tipos de notificação:**

- Alerta
- Informativo
- Status de visualização

### 6. PatientService (5 testes)

**Funcionalidades testadas:**

- Retorno de lista de pacientes
- Busca de paciente por ID
- Criação de novo paciente
- Atualização de paciente existente
- Exclusão de paciente existente

**Dados validados:**

- Nome completo
- CPF
- Email
- Data de nascimento
- Gênero
- Endereço e telefone

### 7. HealthUnitService (3 testes)

**Funcionalidades testadas:**

- Retorno de todas as unidades de saúde
- Busca de unidade de saúde por ID
- Tratamento de erro quando unidade não é encontrada

**Validação de erro:**

- Mensagem: "UBS não encontrada."

### 8. EmergencyService (3 testes)

**Funcionalidades testadas:**

- Retorno de todas as emergências
- Busca de emergência por ID
- Tratamento de erro quando emergência não é encontrada

**Dados de emergência:**

- Data/hora da chamada
- Motivo da emergência
- Status de atendimento
- Observações e encaminhamentos

### 9. HealthAgentService (2 testes)

**Funcionalidades testadas:**

- Retorno de todos os agentes de saúde
- Busca de agente de saúde por ID

**Tipos de agente:**

- Agente Comunitário de Saúde
- Enfermeira
- Outros profissionais de saúde

### 10. WoundMonitoringService (5 testes)

**Funcionalidades testadas:**

- Retorno de lista de monitoramentos de ferimentos
- Busca de monitoramento por ID
- Criação de novo monitoramento
- Atualização de monitoramento existente
- Exclusão de monitoramento existente

**Dados de monitoramento:**

- Data de avaliação
- Observações do agente
- Tratamento recomendado
- Frequência de monitoramento
- Próxima data de avaliação

### 11. EducationalMaterialService (3 testes)

**Funcionalidades testadas:**

- Retorno de todos os materiais educacionais
- Busca de material educacional por ID
- Tratamento de erro quando material não é encontrado

**Tipos de conteúdo:**

- Vídeo
- Texto
- Material direcionado por tipo de ferida
- Classificação por nível de severidade

## Estratégias de Teste Implementadas

### 1. **Mocking de Repositórios**

Todos os testes utilizam mocks dos repositórios de dados, isolando a lógica de negócio das operações de persistência:

```javascript
const mockRepository = {
  findAll: jest.fn(),
  findById: jest.fn(),
  create: jest.fn(),
  update: jest.fn(),
  delete: jest.fn(),
};
```

### 2. **Testes de Cenários Positivos**

- Operações CRUD básicas (Create, Read, Update, Delete)
- Retorno de dados válidos
- Comportamento esperado em condições normais

### 3. **Testes de Cenários Negativos**

- Validação de dados inválidos
- Tratamento de registros não encontrados
- Lançamento de exceções apropriadas

### 4. **Validação de Dados**

- Testes específicos para validação de entrada
- Verificação de tipos de dados
- Validação de campos obrigatórios

## Padrões de Teste Utilizados

### Estrutura AAA (Arrange, Act, Assert)

```javascript
it("deve retornar todos os questionários", async () => {
  mockRepository.findAll.mockResolvedValue(patientQuestionnaireMock);

  const result = await service.findAll();

  expect(mockRepository.findAll).toHaveBeenCalledTimes(1);
  expect(result).toEqual(patientQuestionnaireMock);
});
```

### Testes de Validação de Erro

```javascript
it("deve lançar um erro se o questionário não for encontrado", async () => {
  mockRepository.findById.mockResolvedValue(null);

  await expect(service.findById(999)).rejects.toThrow(
    "Questionário não encontrado."
  );
});
```

## Ferramentas Utilizadas

- **Jest**: Framework principal de testes
- **Mocking**: Para isolamento de dependências
- **Coverage Report**: Para análise de cobertura de código
- **Async/Await**: Para testes de operações assíncronas

## Configuração de Testes

### Script de Execução

```json
{
  "scripts": {
    "test": "npx jest --coverage --runInBand"
  }
}
```

### Configuração Jest

- Coverage habilitado
- Execução sequencial (--runInBand)
- Relatório de cobertura completo

## Métricas de Qualidade

### Tempo de Execução

- **Total**: 2.572 segundos
- **Média por teste**: ~0.05 segundos
- **Performance**: Excelente para suíte de 50 testes

### Cobertura de Código

- **Statements**: 100%
- **Branches**: 100%
- **Functions**: 100%
- **Lines**: 100%

## Benefícios Alcançados

1. **Confiabilidade**: Validação completa da lógica de negócio
2. **Manutenibilidade**: Detecção precoce de regressões
3. **Documentação**: Testes servem como documentação viva
4. **Qualidade**: Garantia de funcionamento correto dos serviços

## Conclusão

A suíte de testes atual demonstra uma excelente cobertura da camada de serviços da aplicação, com **100% de cobertura** em todas as métricas importantes. Os testes cobrem tanto cenários de sucesso quanto de falha, garantindo a robustez e confiabilidade dos serviços implementados.

### Próximos Passos Recomendados

1. **Testes de Integração**: Implementar testes que validem a integração entre camadas
2. **Testes End-to-End**: Implementar testes que validem fluxos completos da aplicação
3. **Testes de Performance**: Avaliar comportamento sob carga
4. **Testes de Segurança**: Validar aspectos de autenticação e autorização
5. **Testes de Mutação**: Verificar a qualidade dos testes existentes
6. **Testes de Contrato**: Validar APIs com consumidores externos

### Indicadores de Sucesso

- **50 testes** executados com sucesso
- **0 falhas** na execução
- **100% cobertura** em todas as métricas
- **11 serviços** completamente testados
- **Tempo de execução** otimizado (< 3 segundos)

## 5.2. Testes de usabilidade (sprint 5)

# 5.2 Testes de Usabilidade

## Introdução

Para avaliar a usabilidade do sistema, foram realizados testes com 13 usuários reais representando o público-alvo da aplicação (pacientes e profissionais de saúde). Os participantes realizaram tarefas previamente definidas, com acompanhamento e coleta de feedback. A seguir, são apresentados os relatórios com os enunciados das tarefas, etapas de execução, resultados e principais melhorias detectadas, seguidas de sua priorização.

---

## Tarefa 1 – Cadastro de Paciente

**Enunciado:**  
"Suponha que você quer utilizar o site. Utilize o sistema para se cadastrar."

**Etapas:**

1. Acessar a área de cadastro
2. Preencher os dados do paciente
3. Salvar o cadastro

**Resultados:**  
Todos os usuários completaram com sucesso. Um usuário relatou leve dificuldade ao preencher os campos.

---

## Tarefa 2 – Visualizar Dados do Perfil

**Enunciado:**  
"Suponha que você é um paciente e quer ver os dados de seu perfil."

**Etapas:**

1. Acessar o perfil
2. Obter dados do perfil

**Resultados:**  
Todos os participantes realizaram a tarefa com sucesso, sem dificuldades.

---

## Tarefa 3 – Enviar Foto

**Enunciado:**  
"Suponha que você é um paciente e precisa enviar uma foto."

**Etapas:**

1. Acessar parte de envio de foto
2. Carregar a foto
3. Enviar para análise

**Resultados:**  
Um usuário teve dificuldade ao carregar a foto. Além disso, foram levantadas melhorias relacionadas à usabilidade desta funcionalidade (ver seção de melhorias abaixo).

---

## Tarefa 4 – Ver Consultas

**Enunciado:**  
"Suponha que você gostaria de visualizar as suas consultas. Utilize o sistema para isso."

**Etapas:**

1. Acessar a parte de consultas
2. Visualizar as consultas marcadas

**Resultados:**  
Todos os participantes concluíram com sucesso.

---

## Tarefa 5 – Ativar Acessibilidade de Libras

**Enunciado:**  
"Suponha que você está utilizando o site e precisa de acessibilidade. Ative a acessibilidade de Libras."

**Etapas:**

1. Clicar no botão de Libras
2. Acessar o tutorial
3. Verificar se o conteúdo está acessível

**Resultados:**  
Dois usuários relataram dificuldades para iniciar o tutorial ou compreender como ativar a acessibilidade.

---

## Tarefa 6 – Entrar no Site

**Enunciado:**  
"Suponha que você é um paciente já cadastrado. Faça login no site."

**Etapas:**

1. Inserir dados de login
2. Entrar no sistema

**Resultados:**  
Todos os participantes conseguiram realizar a tarefa sem dificuldades.

---

## Melhorias Detectadas e Priorização

Durante os testes, foram levantadas as seguintes **melhorias de usabilidade**, organizadas por **nível de prioridade**:

| Prioridade | Problema Detectado                                  | Proposta de Solução                                                               |
| ---------- | --------------------------------------------------- | --------------------------------------------------------------------------------- |
| Alta       | Aba de fotos não aparece no mobile                  | Corrigir o layout responsivo para garantir exibição da aba em dispositivos móveis |
| Alta       | Tutorial incompleto (não indica onde enviar foto)   | Incluir instruções claras com indicação visual do botão de envio de foto          |
| Alta       | "Data de início" no envio de foto é ambígua         | Substituir por "Data da lesão" para melhorar a compreensão                        |
| Média      | "Como você está se sentindo hoje" é confuso         | Reformular a pergunta para algo como: "Descreva seu estado de saúde atual"        |
| Média      | Instruções da foto não falam da régua               | Acrescentar no tutorial que a régua deve estar visível na imagem                  |
| Média      | Botão de voltar é muito parecido com o de cadastrar | Alterar cor e reposicionar para evitar cliques equivocados                        |
| Baixa      | Falta de popup de confirmação após envio            | Adicionar mensagem de sucesso com redirecionamento para tela inicial              |

# <a name="c6"></a>6. Estudo de Mercado e Plano de Marketing (sprint 4)

## 6.1 Resumo Executivo

Nosso projeto tem como objetivo desenvolver uma aplicação web voltada para o acompanhamento de pacientes com feridas, oferecendo uma solução tecnológica diante da alta demanda enfrentada por hospitais e UBS.A proposta surgiu em parceria com a Faculdade de Medicina da Universidade de São Paulo (FMUSP), reconhecida por sua excelência em cirurgia plástica. A sobrecarga no atendimento de pacientes com feridas, levou a uma oportunidade de ampliar o acesso à orientação médica de qualidade por meio de uma plataforma digital acessível, segura e eficiente.Nossa plataforma permite que os pacientes registram a evolução de suas feridas, enviem imagens e recebam orientações personalizadas de profissionais da saúde, tendo um cuidado diário e remoto. Com isso, busca reduzir filas de atendimento, otimizar o tempo de resposta dos profissionais de saúde e melhorar a qualidade de vida dos pacientes.Nossos diferenciais são: a interface intuitiva e inclusiva e a integração com profissionais de saúde e pacientes.Os objetivos estratégicos do projeto incluem: promover a digitalização do acompanhamento clínico, reduzir os custos operacionais das unidades de atendimento e fortalecer o vínculo entre paciente e profissional por meio da tecnologia.

## 6.2 Análise de Mercado

O mercado de soluções digitais voltadas ao tratamento de feridas tem se expandido nos últimos anos, impulsionado pelo aumento da população idosa, do número de pacientes com doenças crônicas como diabetes e pela demanda de tecnologias que facilitem o acompanhamento remoto. Estima-se que cerca de 2% da população brasileira sofra com feridas crônicas, como úlceras venosas e lesões diabéticas (CREMERJ, 2023). Isso representa milhões de pessoas que dependem de acompanhamento diário.Por mais que tenha esses problemas, o Brasil ainda precisa de plataformas digitais acessíveis para monitorar lesões de forma eficiente, especialmente dentro do SUS. Nesse contexto, diversas empresas internacionais se destacam, como o Tissue Analytics, que utiliza inteligência artificial para medir e acompanhar a evolução de feridas por meio de imagens (NET HEALTH, 2023), e o eKare inSight, que emprega câmeras 3D e algoritmos de visão computacional para fornecer dados precisos a hospitais e clínicas. Outro exemplo é o Pixalere, sistema usado por enfermeiros no Canadá para documentar e tratar lesões (PIXALERE, 2023).O problema é que essas plataformas possuem um custo muito alto, são voltadas para grandes instituições privadas e exigem equipamentos sofisticados, o que dificulta a sua adoção em grande escala no Brasil.Nosso projeto se diferencia por ser gratuito, acessível e de fácil usabilidade, sendo voltado para pacientes e profissionais de saúde atuantes nas Unidades Básicas de Saúde (UBS). A proposta permite que o próprio paciente registre a evolução da ferida, envie fotos com segurança e receba orientações de forma remota, promovendo um cuidado mais humanizado. Além disso, profissionais podem utilizar a plataforma como ferramenta de triagem e acompanhamento, otimizando o tempo e recursos disponíveis no sistema público.Considerando o crescimento da medicina digital, a regulamentação favorável à saúde digital no Brasil e a ausência de soluções nacionais com foco no cuidado de feridas, nosso projeto se posiciona com grande potencial de impacto social, apresentando uma proposta de inovação tecnológica, simplicidade e alinhamento com as necessidades do SUS. Em um mercado ainda pouco explorado no país, onde os concorrentes atuam de forma limitada ou distante da saúde pública, o projeto representa uma solução estratégica, escalável e com forte diferencial competitivo.

### Visão Geral do Setor

O setor de tratamento de feridas e cirurgia plástica no Brasil está em expansão, impulsionado por avanços tecnológicos e mudanças regulatórias. Estima-se que o mercado de estética nacional atinja um faturamento de US$ 41,6 bilhões até 2028, posicionando o Brasil como o terceiro maior mercado global, atrás apenas dos Estados Unidos e da China (PODER360, 2023).Na área de tecnologia, inovações como curativos inteligentes, impressão 3D e plataformas de telemedicina têm transformado o cuidado com feridas. Empresas como a NatércIA Health utilizam inteligência artificial para monitorar e acompanhar feridas crônicas, oferecendo acesso a cuidados especializados, especialmente em regiões com infraestrutura de saúde limitada (NATÉRCIA HEALTH, 2023).Regulamentações recentes também têm contribuído para a digitalização da saúde. O programa “Meu SUS Digital”, instituído pela Portaria nº 1.434 do Ministério da Saúde, visa à integração dos dados de saúde dos cidadãos, facilitando a implementação de soluções digitais no sistema público (BRASIL, 2023).Apesar dos avanços, ainda existem desafios. A necessidade de infraestrutura adequada, a disponibilidade de profissionais capacitados e a burocracia regulatória dificultam a adoção de novas tecnologias, especialmente em pequenas cidades. Além disso, a descentralização do sistema de saúde brasileiro exige uma coordenação eficaz entre os entes federativos para garantir a implementação uniforme das inovações.Em resumo, o setor apresenta grandes oportunidades para soluções tecnológicas que proporcionem cuidados mais eficientes e acessíveis para pacientes com feridas e em tratamentos de cirurgia plástica.

### Tamanho e Crescimento do Mercado

O setor de saúde representa cerca de 10% do PIB brasileiro e emprega aproximadamente 20 milhões de pessoas (AGÊNCIA GOV, 2024). Dentro desse cenário, os hospitais universitários, apesar de serem menos de 3% do total, concentram 9% dos leitos e 24% dos recursos do SUS para internações. O Brasil é líder mundial em cirurgia plástica, com mais de 2 milhões de procedimentos estéticos realizados em 2023, segundo a ISAPS (CBN, 2024; ISAPS, 2024). Os procedimentos mais comuns incluem lipoaspiração, aumento das glândulas mamárias e reconstruções faciais. Com o envelhecimento da população, acaba tendo uma maior aceitação desses procedimentos, fazendo o setor continuar crescer. A FMUSP, por meio do Hospital das Clínicas, é referência nacional em cirurgias plásticas de alta complexidade, especialmente em casos como queimaduras e reconstruções (FMUSP, 2024). Sua participação no número total de cirurgias é muito relevante, onde também é reconhecida tanto na formação médica quanto na execução desses procedimentos complexos. Em resumo desse cenário, é um mercado em expansão, com potencial de impacto social e tecnológico, especialmente quando integrar 100% ferramentas digitais no acompanhamento clínico de pacientes com feridas e lesões complexas.

### Tendências de Mercado

O setor de cirurgia plástica e tratamento de feridas no Brasil está passando por várias transformações que são ajudadas por avanços tecnológicos, mudanças comportamentais e novas dinâmicas do mercado. Na área de avanços tecnológicos, a integração de tecnologia como IA, impressão 3D e simulações em 3D está mudando o planejamento e a execução de procedimentos cirúrgicos (PROPLÁSTICA, 2025). A IA permite análises mais precisas, previsão de resultados e ajuda na tomada de decisões. A impressão 3D possibilita a criação de modelos personalizados, ajudando na visualização e planejamento cirúrgico. Essas inovações resultam em procedimentos mais eficientes, com menor tempo de recuperação e custos reduzidos. Na área de mudanças comportamentais, têm uma crescente demanda por procedimentos não invasivos e resultados mais naturais. Os pacientes que buscam intervenções que ajudam nas suas características, promovem uma estética sem exageros (EM, 2025). Na área de dinâmicas mercadológicas, o mercado brasileiro de saúde tem visto um grande aumento no número de startups de para soluções médicas, com 536 atuando no país (FOLHA DO AMAPÁ, 2025). Essas empresas desenvolvem tecnologias que otimizem o atendimento, reduzam custos e ampliem o acesso a tratamentos especializados. Em resumo, essas tendências no setor da cirurgia plástica e no tratamento de feridas, mostram uma medicina mais personalizada, tecnológica e acessível para todos, com capacidade de atender um número maior de pessoas com eficiência e menor custo.

## 6.3 Análise da Concorrência

No setor de cirurgia plástica para tratamento de queimaduras e feridas, a análise da concorrência mostra um cenário com várias barreiras que impedem novos entrantes, o que mantém a competição estável. A formação complexa dos profissionais de saúde é aliada às necessidades de infraestrutura e certificações rigorosas, dificultando a entrada de novos concorrentes no mercado. Além disso, a inexistência de produtos substitutos para a cirurgia de reconstrução em acidentes graves de queimaduras reforça ainda mais a posição dos centros especializados (RBCP, 2020).O poder de barganha dos fornecedores é muito alto, pois os hospitais dependem deles, já que são especializados e controlados por poucas empresas multinacionais que dominam o mercado de materiais e equipamentos essenciais para o tratamento (MARKETS AND MARKETS, 2025). Por outro lado, o poder de barganha dos clientes é moderado, já que os pacientes com queimaduras graves têm acesso limitado a centros especializados, mas mesmo assim conseguem escolher entre o sistema público e hospitais privados, dependendo dos custos financeiros que querem gastar.A rivalidade entre os concorrentes é de intensidade média. Ela é caracterizada pela disputa entre poucos centros especializados que tentam se destacar pela técnica boa, certificações e inovações tecnológicas (SBQUEIMADURAS, 2025). Essa competição focada em reconhecimento e qualidade mantém o setor competitivo, mas controlado, por isso a demanda constante e a concentração desses serviços em lugares determinados.

### A) Principais Concorrentes

No setor de cirurgia plástica voltada ao tratamento de queimaduras graves, os principais concorrentes diretos da FMUSP são outros centros de referência. Entre eles, destacam-se cerca de 50 Centros de Tratamento de Queimados, conforme levantamento da Sociedade Brasileira de Queimaduras em 2022 (SOCIEDADE BRASILEIRA DE QUEIMADURAS, 2022). Esses centros disputam o reconhecimento como referências nacionais, buscando diferenciação por meio de certificados, modernização e excelência. A rivalidade entre concorrentes é moderada, já que a maioria desses centros é regionalizada, com uma demanda constante. A diferença está principalmente na qualidade da infraestrutura, qualificação das equipes e investimentos em pesquisas. Já os concorrentes indiretos incluem clínicas privadas especializadas em estética e microcirurgia, que atendem pacientes com maior poder aquisitivo. No entanto, como a maioria dos atendimentos de queimaduras graves ocorre via SUS ou convênios, o impacto desses concorrentes indiretos é limitado. Além disso, não há substitutos eficazes para as técnicas cirúrgicas aplicadas em queimaduras profundas, o que reforça a posição dessas instituições especializadas (BRASIL, 2000). Nesse cenário, a FMUSP se destaca pela sua tradição acadêmica, pela integração com o Hospital das Clínicas e pela capacidade de oferecer atendimento de excelência em casos complexos (REVISTA BRASILEIRA DE CIRURGIA PLÁSTICA, 2025).

### B) Vantagens Competitivas da Aplicação Web

A principal vantagem competitiva da aplicação web está na digitalização de um processo que hoje só é feito de forma 100% presencial, que é o acompanhamento clínico de feridas. Com a transformação desse processo em uma plataforma digital, a solução traz mais praticidade, reduzindo o tempo gasto por profissionais da saúde e facilitando o acesso remoto às informações (SCIELO, 2025a). Além disso, essa aplicação proporciona muita agilidade na tomada de decisão médica, o que impacta diretamente no tempo de recuperação dos pacientes. O monitoramento das feridas, com registros fotográficos, histórico de evolução e alertas clínicos, ajuda para um retorno mais rápido dos pacientes às suas atividades diárias (SCIELO, 2025a). Outro diferencial importante é que a plataforma foi pensada com foco na realidade do setor público de saúde, considerando limitações de recursos e conectividade. Isso torna a solução acessível para hospitais como o da FMUSP (SCIELO, 2025b). Em resumo, essa aplicação reduz a necessidade de deslocamento diário de pacientes, ajudando com a eficiência do sistema de saúde e promovendo um cuidado mais humanizado e contínuo, mesmo fora do hospital.

## 6.4 Público-Alvo

O público-alvo do nosso projeto é composto por profissionais de saúde, estudantes de medicina e gestores de hospitais, com foco nas instituições públicas e no Hospital das Clínicas. O sistema foi feito para ajudar em áreas de feridas e microcirurgia, onde há uma demanda por registros e acompanhamento de pacientes. Segundo Kotler e Keller (2012), a definição do público-alvo é essencial para o sucesso de qualquer produto, porque ajuda a alinhar as funcionalidades e a comunicação da solução. No nosso projeto, essa aplicação tem como objetivo atender dois públicos principais:
Profissionais da saúde
Incluem médicos, enfermeiros e atuantes na área de cirurgia plástica, feridas e queimaduras. Esses profissionais precisam de uma plataforma que facilite o registro clínico, permita o acompanhamento da evolução das feridas com base em imagens e dados estruturados, e otimize o tempo de consulta. Essa plataforma ajuda nessas necessidades ao digitalizar processos que ainda são feitos manualmente, reduzindo o trabalho e aumentando a precisão das decisões clínicas.
Usuários do aplicativo
São formados por pacientes do SUS, onde se incluem as vítimas de queimaduras e feridas crônicas, sendo que muitos desses pacientes moram longe de centros de referência. Nosso projeto vai oferecer para essas pessoas uma maneira mais prática, rápida e fácil de manter o contato com a equipe médica, enviando fotos, atualizando o que sente e recebendo orientações direto pela plataforma.
De acordo com o Ministério da Saúde (2018), o uso da telessaúde no SUS tem ampliado o acesso, reduzido deslocamentos desnecessários e melhorado a qualidade. Isso reforça a aderência do produto às necessidades reais do mercado público de saúde (MINISTÉRIO DA SAÚDE, 2015).

### A) Segmentação de Mercado

O projeto recebido da FMUSP é voltado para o tratamento de feridas na área de microcirurgia plástica. A segmentação considera aspectos demográficos, geográficos e comportamentais, seguindo princípios de marketing que facilitam a adaptação da aplicação às necessidades reais dos usuários.Na parte demográfica, o foco são adultos e idosos, pois o envelhecimento aumenta o número de feridas crônicas e queimaduras que precisam de tratamento especializado. Conforme o Instituto Brasileiro de Geografia e Estatística (IBGE, 2025), a população brasileira com 60 anos ou mais era de aproximadamente 32,9 milhões em 2022, representando cerca de 15,5% da população total.Na parte geográfica, a segmentação prioriza regiões com menor acesso a centros especializados, como o Norte e Nordeste, onde a concentração de serviços de microcirurgia e tratamento de feridas é menor. A telessaúde é solução para ampliar o acesso. De acordo com o Programa Nacional Telessaúde Brasil Redes (MINISTÉRIO DA SAÚDE, 2015), do Ministério da Saúde, ela desempenha papel importante na redução das desigualdades regionais no SUS.Na parte comportamental, o público inclui pacientes que buscam agilidade e comodidade no acompanhamento, além de profissionais da saúde que precisam de suporte remoto para o diagnóstico e tratamento. O SUS atende mais ou menos 75% da população brasileira (IPEA, 2025), reforçando a importância do acompanhamento digital para ajudar a otimizar os serviços públicos de saúde.A segmentação do mercado está alinhada com o contexto da FMUSP e do sistema de saúde, atendendo às demandas por meio da tecnologia e digitalização.

### B) Perfil do Público-Alvo

O público-alvo do nosso projeto, desenvolvido em parceria com a FMUSP, é formado por pacientes adultos e idosos com feridas complexas como queimaduras, úlceras ou lesões crônicas atendidos pelo Sistema Único de Saúde (SUS) em diferentes regiões do Brasil.Demograficamente, esse grupo é composto majoritariamente por pessoas com 50 anos ou mais, faixa etária mais propensa a doenças crônicas e dificuldades de cicatrização. Segundo o IBGE (GOV.BR – MINISTÉRIO DA SAÚDE, 2023), a população com 60 anos ou mais já ultrapassa 32,9 milhões no país, o que reforça a urgência de políticas públicas voltadas para esse segmento e sua alta demanda por serviços de atenção especializada.Psicograficamente, esses pacientes valorizam qualidade de vida, acolhimento e soluções que respeitem suas limitações físicas. Muitos vivem em regiões periféricas ou rurais, com menor acesso a centros especializados. Por isso, preferem recursos que evitem deslocamentos frequentes, sem comprometer o acompanhamento médico.Comportamentalmente, esse público busca agilidade no atendimento, orientação clara e continuidade no tratamento. Suas principais necessidades incluem retorno médico eficiente, facilidade de comunicação com a equipe de saúde e suporte durante a recuperação.A aplicação proposta atende a esses pontos ao permitir o agendamento remoto, envio de imagens das feridas, histórico clínico e acesso direto a profissionais da FMUSP via telessaúde, promovendo cuidado contínuo e humanizado.

## 6.5 Posicionamento

Nosso projeto foi desenvolvido em parceria com a FMUSP e se posiciona como uma solução moderna, acessível e humanizada para o acompanhamento de pacientes com feridas. Nosso principal diferencial está na digitalização de um serviço que só é feito presencial, tendo um cuidado maior.Nosso objetivo é ser lembrado como a forma mais prática e eficiente de continuar um tratamento de feridas mesmo estando longe dos hospitais, tendo segurança, acompanhamento contínuo dos profissionais da saúde e que tenha um retorno rápido dos médicos. O público vai ver nosso projeto como uma ferramenta que facilita o dia a dia, tanto dos pacientes quanto dos profissionais da saúde. Para os pacientes, vão ter uma forma mais simples de receber tratamento, sem precisar ir toda hora para o hospital conferir a ferida. Para os profissionais da saúde, ele vai otimizar tempo, organizar as informações e ajudar na tomada de decisões mais rápidas com a ajuda das imagens e dos dados que os pacientes enviaram. Além disso, nosso posicionamento valoriza todas as pessoas. A plataforma é fácil de usar, interativa e também foi pensada para ter um acolhimento com as pessoas com dificuldades, tendo mensagens fáceis de entender, vídeos de suporte de dúvidas e um botão de acessibilidade de libras. Nessa área de saúde digital, queremos ser vistos como um projeto de inovação que tenha um impacto social, que vai ampliar o alcance do SUS para mais pessoas e ajudar a reduzir problemas por falta de acompanhamento. Com isso, nossa aplicação é como uma ferramenta que vai ajudar na extensão do cuidado presencial, que vai ligar o hospital e ao paciente.

### A) Proposta de Valor Única

Oferecemos uma plataforma digital para registrar feridas, acompanhar e orientar casos de feridas complexas para profissionais da saúde e pacientes, que querem acompanhar a evolução das feridas sendo rápido, fácil e seguro, sem precisar ter atendimentos presenciais no hospital ou anotações no papel com instruções. Nosso diferencial está em digitalizar uma ação que só é feita presencialmente, permitindo que médicos, enfermeiros e pacientes melhorem a comunicação entre si e também otimizem o tempo de resposta das situações que exigem cuidado contínuo. Nosso projeto oferece recursos como registro da ferida, histórico de evolução e alertas de novas fotos e agendamentos. Para os pacientes, o valor está na agilidade do atendimento, com economia de tempo e redução da necessidade de locomoção até os hospitais, essencial para pessoas com dificuldades de mobilidade ou que vivem longe dos centros de referência. Para os profissionais da saúde, a plataforma contribui na organização e acompanhamento dos casos clínicos. Com isso, a aplicação se posiciona como uma solução inovadora e alinhada às necessidades do Sistema Único de Saúde (SUS) e da FMUSP, promovendo uma saúde pública mais moderna e centrada no cuidado (INTELI, 2025).

### B) Estratégia de Diferenciação

Nosso projeto tem como diferencial no mercado a digitalização de um processo que só é feito de forma presencial. O nosso diferencial é ter tecnologia com necessidades reais do sistema de saúde, oferecendo uma solução para o acompanhamento dessas feridas. Enquanto outros projetos se concentram só nas clínicas privadas ou eles precisam de equipamentos avançados e caros, e a nossa plataforma foi pensada para funcionar com recursos mínimos e também em lugares com conexão ruim. Isso vai possibilitar o uso da plataforma em lugares remotos e escassos, alinhando com o programa telessaúde Brasil redes, que incentiva a ampliação desse acesso a saúde com tecnologias digitais (BRASIL, 2025).Também, a interface simples ajuda a entender e usar melhor a plataforma, o que torna o uso intuitivo tanto para profissionais da saúde quanto para pacientes. Isso representa uma vantagem em relação às outras aplicações.

## 6.6 Estratégia de Marketing

A estratégia de marketing do nosso projeto foi desenvolvida com foco em alcançar dois públicos principais, os profissionais da saúde e pacientes atendidos nas instituições públicas, como o Hospital das Clínicas da FMUSP. Nosso plano segue os pilares do mix de marketing dos 4Ps, os Produtos Preço, Praça e Promoção, no contexto de uma solução digital.

#### Produto

Nosso produto é uma plataforma digital especializada em registrar, acompanhar e ter avaliação clínica de feridas em pacientes atendidos. Diferente das outras soluções ou voltadas ao setor privado, esta ferramenta é adaptada as necessidades do SUS e instituições de ensino como a FMUSP. Seus principais diferenciais são:

-Interface simples e acessível

-Registro padronizado com fotos

-Evolução da ferida com avaliação dos profissionais da saúde

-Acesso remoto, permitindo monitoramento.

#### Preço (Price)

Neste momento, nosso projeto ainda não possui uma estratégia de precificação definida, porque o projeto está em fase de desenvolvimento como uma iniciativa vinculada a FMUSP. Ele é um projeto colaborativo, construído em parceria com profissionais da saúde do Hospital das Clínicas. O foco é entregar uma solução funcional, validada em ambiente real, que possa no futuro ser usada no sistema de saúde, especialmente do SUS.

#### Praça

A distribuição do projeto é feita através de plataformas digitais

-Site com login seguro

-Versão web responsiva acessível por computadores e celulares

A escolha por plataformas digitais é para facilitar o acesso remoto, especialmente para equipes de saúde que atuam em locais afastados ou que têm dificuldade de locomoção.

#### Promoção

A divulgação da plataforma vai acontecer de forma prática e próxima do dia a dia das pessoas. Quem vai ajudar nisso são os agentes de saúde que já visitam as casas dos pacientes.Durante essas visitas as casas, eles vão apresentar o aplicativo e explicar como ele pode facilitar o acompanhamento das feridas. Essa abordagem é importante principalmente para quem tem dificuldade de locomoção ou mora longe dos centros de saúde. A ideia é fazer com que o paciente perceba que usar o aplicativo vai economizar tempo e tornar o atendimento mais rápido e fácil.Tudo isso para garantir que mais gente conheça a plataforma e use ela no dia a dia.

### A) Produto/Serviço

Nossa aplicação é uma plataforma digital para registro e acompanhamento de feridas, desenvolvida especialmente para o SUS e instituições como a FMUSP. Ela permite o cadastro de pacientes, registro com fotos, anotações clínicas e avaliação da evolução da ferida ao longo do tempo.Entre os benefícios estão a agilidade no atendimento, a redução de deslocamentos dos pacientes e a organização dos casos clínicos. Seus diferenciais incluem uma interface simples, acesso remoto e adaptação às rotinas do sistema público de saúde, facilitando o uso tanto por profissionais quanto pelos próprios pacientes.

### 6.2 Preço

Atualmente, o projeto não possui uma estratégia de precificação definida, pois está sendo desenvolvido como parte de uma iniciativa colaborativa com a FMUSP e o Hospital das Clínicas. O principal objetivo neste momento é validar a plataforma em um ambiente real de saúde pública, garantindo que ela funcione bem dentro do SUS. Como ainda estamos em fase de testes e melhorias, o foco está na utilidade e eficiência da ferramenta. No futuro, quando for o momento de expansão, o modelo de preço será pensado com base na realidade das instituições públicas, buscando manter o acesso fácil e de baixo custo.

### 6.3 Praça (Distribuição)

A distribuição da aplicação será feita por canais totalmente digitais. A principal forma de acesso será por meio de um site com login seguro, garantindo a proteção dos dados dos pacientes. A plataforma é responsiva, ou seja, funciona tanto em computadores quanto em celulares, o que facilita o uso em diferentes contextos, como hospitais, ambulatórios ou mesmo nas visitas domiciliares feitas por agentes de saúde.A escolha pelos canais digitais foi pensada para atender melhor às equipes médicas que atuam em regiões afastadas ou que enfrentam dificuldades de locomoção. Dessa forma, o profissional consegue registrar e acompanhar os casos de forma prática e remota, sem depender de estrutura física específica. Isso amplia o alcance da solução e garante maior agilidade no atendimento e na troca de informações clínicas.

### 6.4 Promoção

A divulgação da plataforma será feita de forma prática, próxima da rotina dos pacientes. Os próprios agentes de saúde, que já visitam as casas dos pacientes, serão os principais incentivadores do uso do aplicativo. Durante as visitas domiciliares, eles vão apresentar o app, mostrando como ele facilita o registro e o acompanhamento das feridas.Essa estratégia é especialmente importante para pacientes com dificuldade de locomoção ou que moram longe dos centros de saúde. O objetivo é que o paciente entenda que usar o aplicativo vai economizar tempo e tornar o atendimento mais ágil e eficiente. Além disso, essa aproximação direta ajuda a criar confiança e engajamento com a plataforma.Assim, a promoção acontece de forma natural e com impacto real, garantindo que a ferramenta seja conhecida e utilizada no dia a dia, melhorando a experiência dos usuários e a qualidade do acompanhamento clínico.

# <a name="c7"></a>7. Conclusões e trabalhos futuros (sprint 5)

_Escreva de que formas a solução da aplicação web atingiu os objetivos descritos na seção 2 deste documento. Indique pontos fortes e pontos a melhorar de maneira geral._

_Relacione os pontos de melhorias evidenciados nos testes com planos de ações para serem implementadas. O grupo não precisa implementá-las, pode deixar registrado aqui o plano para ações futuras_

_Relacione também quaisquer outras ideias que o grupo tenha para melhorias futuras_

# <a name="c8"></a>8. Referências

AGÊNCIA GOV. Governo destina R$ 4,2 bilhões para alavancar indústria da saúde e atender necessidades do SUS. 2024. Disponível em: https://agenciagov.ebc.com.br/noticias/202410/governo-destina-r-4-2-bilhoes-para-alavancar-industria-da-saude-e-atender-necessidades-do-sus-1. Acesso em: 12 jun. 2025.
ASSIS, J. M. et al. Práticas e perspectivas no manejo de feridas na Atenção Primária à Saúde. Research, Society and Development, [s.l.], 2022. Disponível em: https://rsdjournal.org/index.php/rsd/article/download/47977/37787/493539. Acesso em: 01 maio 2025.
BRASIL. Ministério da Saúde. Meu SUS Digital. 2023. Disponível em: https://www.gov.br/saude/pt-br/composicao/seidigi/meususdigital. Acesso em: 12 jun. 2025.
BRASIL. Ministério da Saúde. Portaria nº 1.273, de 21 de novembro de 2000. Organiza e implanta Redes Estaduais de Assistência a Queimados. Diário Oficial da União: seção 1, Brasília, DF, 23 nov. 2000, p. 51. Disponível em: https://bvsms.saude.gov.br/bvs/saudelegis/gm/2000/prt1273_21_11_2000.html. Acesso em: 29 abr. 2025.
BRASIL. Ministério da Saúde. Portaria nº 1.273, de 26 de julho de 2000. Institui a Política Nacional de Atenção Integral às Pessoas Queimadas. Disponível em: https://bvsms.saude.gov.br/bvs/saudelegis/gm/2000/prt1273_26_07_2000.html. Acesso em: 12 jun. 2025.
BRASIL. Ministério da Saúde. Programa Nacional Telessaúde Brasil Redes. 2015. Disponível em: https://bvsms.saude.gov.br/bvs/folder/programa_nacional_telessaude_bbrasil_redes_2015.pdf. Acesso em: 12 jun. 2025.
BRASIL. Ministério da Saúde. Protocolos Clínicos e Diretrizes Terapêuticas - PCDT. Portal Gov.br, [s.d.]. Disponível em: https://www.gov.br/saude/pt-br/assuntos/pcdt. Acesso em: 01 maio 2025.
BRASIL. Programa Nacional Telessaúde Brasil Redes. Disponível em: https://aps.bvs.br/programa-nacional-telessaude-brasil-redes/. Acesso em: 12 jun. 2025.
CBN. Brasil lidera ranking de cirurgias plásticas em todo o mundo em 2023. 2024. Disponível em: https://cbn.globo.com/brasil/noticia/2024/06/15/brasil-lidera-ranking-de-cirurgias-plasticas-em-todo-o-mundo-em-2023.ghtml. Acesso em: 12 jun. 2025.
CREMERJ. Estima-se que 2% da população brasileira sofra com feridas crônicas. Conselho Regional de Medicina do Estado do Rio de Janeiro, 2023. Disponível em: https://www.cremerj.org.br/informes/exibe/3315. Acesso em: 12 jun. 2025.
EM. Especialista comenta as tendências da cirurgia plástica em 2025. Estado de Minas, 2025. Disponível em: https://www.em.com.br/mundo-corporativo/2025/03/7097986-especialista-comenta-as-tendencias-da-cirurgia-plastica-em-2025.html. Acesso em: 12 jun. 2025.
FMUSP. Projeto de extensão da FMUSP leva mutirão de cirurgias ao interior do Mato Grosso. 2024. Disponível em: https://www.fm.usp.br/fmusp/noticias/projeto-de-extensao-da-fmusp-leva-mutirao-de-cirurgias-ao-interior-do-mato-grosso. Acesso em: 12 jun. 2025.
FOLHA DO AMAPÁ. Especialista comenta as tendências da cirurgia plástica em 2025. 2025. Disponível em: https://www.folhadoamapa.com/noticia/65538/especialista-comenta-as-tendencias-da-cirurgia-plastica-em-2025. Acesso em: 12 jun. 2025.
GONÇALVES, Edilaine da Silva. O podólogo integrado a equipe de saúde na prevenção e no tratamento do pé diabético. Disponivel em: https://repositorio.ucs.br/xmlui/handle/11338/12522. Acesso em: 22 jun. 2025.
GOV.BR – Ministério da Saúde. Pesquisa aponta que quase 37% dos brasileiros acima de 50 anos têm dores crônicas. Disponível em: https://www.gov.br/saude/pt-br/assuntos/noticias/2023/dezembro/pesquisa-aponta-que-quase-37-dos-brasileiros-acima-de-50-anos-tem-dores-cronicas. Acesso em: 12 jun. 2025.
GOV.BR – Ministério dos Direitos Humanos. Crescimento da população idosa brasileira expõe urgência de políticas públicas. Disponível em: https://www.gov.br/mdh/pt-br/assuntos/noticias/2024/junho/crescimento-da-populacao-idosa-brasileira-expoe-urgencia-de-politicas-publicas-para-combater-violacoes-e-desigualdades. Acesso em: 12 jun. 2025.
IBGE. Censo 2022: número de pessoas com 65 anos ou mais cresceu 57,4% em 12 anos. Disponível em: https://agenciadenoticias.ibge.gov.br/agencia-noticias/2012-agencia-de-noticias/noticias/38186-censo-2022-numero-de-pessoas-com-65-anos-ou-mais-de-idade-cresceu-57-4-em-12-anos. Acesso em: 12 jun. 2025.
IBGE. Projeção da População. Disponível em: https://www.ibge.gov.br/estatisticas/sociais/populacao/9109-projecao-da-populacao.html. Acesso em: 12 jun. 2025.
INTELI. Materiais de aula – Plano de Marketing e Branding. 2025. Acesso em: 12 jun. 2025.
IPEA. Importância do SUS. Disponível em: https://www.ipea.gov.br/portal/beneficiometro/beneficiometro-artigos/saude/importancia-do-sus. Acesso em: 12 jun. 2025.
ISAPS. International Survey on Aesthetic/Cosmetic Procedures Performed in 2023. 2024. Disponível em: https://www.isaps.org/media/iuuloooz/4184727-1-14-portuguese-latam.pdf. Acesso em: 12 jun. 2025.
KOTLER, P.; KELLER, K. L. Administração de Marketing. 14. ed. São Paulo: Pearson, 2012. Disponível em: https://biblioteca.unisced.edu.mz/bitstream/123456789/2100/1/Administra%C3%A7%C3%A3o%20de%20Marketing%2C%20Kotler%20e%20Keller%2C%2014ed%2C%202012.pdf. Acesso em: 12 jun. 2025.
MARKETS AND MARKETS. Medical Burn Care Products Market. 2025. Disponível em: https://www.marketsandmarkets.com/report-search-page.asp?rpt=medical-burn-care-products-market. Acesso em: 12 jun. 2025.
MARKETSANDMARKETS. Burn Care Market – Report Search Page, set. 2016. Disponível em: https://www.marketsandmarkets.com/report-search-page.asp?rpt=burn-care-market. Acesso em: 29 abr. 2025.
MINISTÉRIO DA SAÚDE. 71% dos brasileiros têm os serviços públicos de saúde como referência. Disponível em: https://bvsms.saude.gov.br/71-dos-brasileiros-tem-os-servicos-publicos-de-saude-como-referencia/. Acesso em: 12 jun. 2025.
MINISTÉRIO DA SAÚDE. Manual Telessaúde na Atenção Básica. 2018. Disponível em: https://bvsms.saude.gov.br/bvs/publicacoes/manual_telessaude_atencao_basica.pdf. Acesso em: 12 jun. 2025.
NATÉRCIA HEALTH. Como nasceu a Natércia. 2023. Disponível em: https://www.naterciahealth.com/post/como-nasceu-a-nat%C3%A9rcia. Acesso em: 12 jun. 2025.
NET HEALTH. Tissue Analytics. 2023. Disponível em: https://www.nethealth.com/tissue-analytics/. Acesso em: 12 jun. 2025.
PIXALERE. Administrators – Wound Care Software. 2023. Disponível em: https://pixalere.com/administrators-woundcare-software/. Acesso em: 12 jun. 2025.
PODER360. Setor de estética projeta faturamento de US$ 416 bi até 2028. 2023. Disponível em: https://www.poder360.com.br/poder-saude/setor-de-estetica-projeta-faturamento-de-us-416-bi-ate-2028/. Acesso em: 12 jun. 2025.
PORTER, Michael E. Competitive strategy. New York: Free Press, 1980.
PROPLÁSTICA. Inovações tecnológicas em cirurgia plástica. 2025. Disponível em: https://proplastica.com/inovacoes-tecnologicas-em-cirurgia-plastica/. Acesso em: 12 jun. 2025.
RBCP – Revista Brasileira de Cirurgia Plástica. Enxertos e retalhos na cirurgia plástica. 2020. Disponível em: https://www.rbcp.org.br/details/1604/artigo-tecnico-enxertos-e-retalhos-na-cirurgia-plasticas. Acesso em: 12 jun. 2025.
REVISTA BRASILEIRA DE CIRURGIA PLÁSTICA. Diversos artigos sobre cirurgia plástica. Disponível em: https://www.rbcp.org.br/. Acesso em: 12 jun. 2025.
RIBEIRO, Luiz Mário Bonfatti et al. Sindactilia pós-queimadura da mão. Revista Brasileira de Cirurgia Plástica, v. 28, n. 1, p. 123–129, mar. 2013. Disponível em: https://www.scielo.br/j/rbcp/a/CCfC9z3fpGdL7wGgqzTmmCz/. Acesso em: 29 abr. 2025.
SBQUEIMADURAS. Serviços especializados no atendimento do paciente queimado no Brasil. 2025. Disponível em: https://sbqueimaduras.org.br/noticia/servicos-especializados-no-atendimento-do-paciente-queimado-no-brasil. Acesso em: 12 jun. 2025.
SCIELO. Acompanhamento de feridas: práticas e desafios. Disponível em: https://www.scielo.br/j/reben/a/GYjY436Cqm5RFhw8T3MBjYH/?lang=en. Acesso em: 12 jun. 2025.
SCIELO. Desafios da telessaúde no SUS. Disponível em: https://www.scielo.br/j/csp/a/swM7NVTrnYRw98Rz3drwpJf/?lang=pt. Acesso em: 12 jun. 2025.
SOCIEDADE BRASILEIRA DE QUEIMADURAS. Profissional de Saúde. 2022. Disponível em: https://www.sbqueimaduras.org.br/profissional-saude. Acesso em: 12 jun. 2025.
SOCIEDADE BRASILEIRA DE QUEIMADURAS. Serviços especializados no atendimento do paciente queimado no Brasil. SBQ, 27 maio 2022. Disponível em: https://sbqueimaduras.org.br/noticia/servicos-especializados-no-atendimento-do-paciente-queimado-no-brasil. Acesso em: 29 abr. 2025.

# <a name="c9"></a>Anexos

_Inclua aqui quaisquer complementos para seu projeto, como diagramas, imagens, tabelas etc. Organize em sub-tópicos utilizando headings menores (use ## ou ### para isso)_
