class ConsultationController {
  constructor(service) {
    this.service = service;
  }

  async index(req, res) {
    try {
      const data = await this.service.findAll();
      res.json(data);
    } catch (err) {
      console.error(
        "[ConsultationController] - erro ao buscar consultas:",
        err.message
      );
      res.status(400).json({ error: err.message });
    }
  }

  async show(req, res) {
    try {
      const data = await this.service.findById(req.params.id);
      res.json(data);
    } catch (err) {
      console.error(
        "[ConsultationController] - erro ao buscar consulta:",
        err.message
      );
      res.status(400).json({ error: err.message });
    }
  }

  async create(req, res) {
    try {
      const data = await this.service.create(req.body);
      res.status(201).json(data);
    } catch (err) {
      console.error(
        "[ConsultationController] - erro ao criar consulta:",
        err.message
      );
      res.status(400).json({ error: err.message });
    }
  }

  async update(req, res) {
    try {
      const data = await this.service.update(req.params.id, req.body);
      res.json(data);
    } catch (err) {
      console.error(
        "[ConsultationController] - erro ao atualizar consulta:",
        err.message
      );
      res.status(400).json({ error: err.message });
    }
  }

  async updateStatus(req, res) {
    try {
      const { status } = req.body;
      if (!status) {
        return res.status(400).json({ error: "Status é obrigatório" });
      }

      const data = await this.service.updateStatus(req.params.id, status);
      res.json(data);
    } catch (err) {
      console.error(
        "[ConsultationController] - erro ao atualizar status:",
        err.message
      );
      res.status(400).json({ error: err.message });
    }
  }

  async delete(req, res) {
    try {
      const data = await this.service.delete(req.params.id);
      res.json(data);
    } catch (err) {
      console.error(
        "[ConsultationController] - erro ao deletar consulta:",
        err.message
      );
      res.status(400).json({ error: err.message });
    }
  }

  async findByPatient(req, res) {
    try {
      const data = await this.service.findByPatient(req.params.patientId);
      res.json(data);
    } catch (err) {
      console.error(
        "[ConsultationController] - erro ao buscar consultas do paciente:",
        err.message
      );
      res.status(400).json({ error: err.message });
    }
  }

  async findByAgent(req, res) {
    try {
      const data = await this.service.findByAgent(req.params.agentId);
      res.json(data);
    } catch (err) {
      console.error(
        "[ConsultationController] - erro ao buscar consultas do agente:",
        err.message
      );
      res.status(400).json({ error: err.message });
    }
  }
}

module.exports = ConsultationController;
