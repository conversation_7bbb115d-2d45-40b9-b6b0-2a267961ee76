const Joi = require("joi");

class PhotoRecordModel {
  static get schema() {
    return Joi.object({
      photo_date: Joi.string().required(),
      file_path: Joi.string().uri().required(),
      wound_size_mm: Joi.number().required(),
      submitted_by_patient: Joi.string().required(),
      validated_by_agent: Joi.string().required(),
      wound_id: Joi.number().required(),
      validating_agent_id: Joi.number().required()
    });
  }
}

module.exports = PhotoRecordModel;