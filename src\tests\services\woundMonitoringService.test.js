const WoundMonitoringService = require("../../services/woundMonitoringService");

describe("WoundMonitoringService", () => {
  const mockRepository = {
    findAll: jest.fn(),
    findById: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
  };

  const service = new WoundMonitoringService(mockRepository);
  const woundMonitoringMock = [
    {
      evaluation_date: "2025-05-27",
      agent_observations: "Observação do agente",
      recommended_treatment: "Tratamento recomendado",
      monitoring_frequency: 3,
      next_evaluation_date: "2025-06-10",
      wound_id: 1,
      agent_id: 1,
    },
    {
      evaluation_date: "2025-05-28",
      agent_observations: "Outra observação do agente",
      recommended_treatment: "Outro tratamento recomendado",
      monitoring_frequency: 2,
      next_evaluation_date: "2025-06-11",
      wound_id: 2,
      agent_id: 2,
    },
  ];

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('deve retornar a lista de monitoramentos de ferimentos do repositório', async () => {
    mockRepository.findAll.mockResolvedValue(woundMonitoringMock);

    const resultado = await service.findAll();

    expect(mockRepository.findAll).toHaveBeenCalledTimes(1);
    expect(resultado).toEqual(woundMonitoringMock);
  });

  it('deve retornar um monitoramento de ferimento pelo ID', async () => {
    mockRepository.findById.mockResolvedValue(woundMonitoringMock[0]);

    const resultado = await service.findById(1);

    expect(mockRepository.findById).toHaveBeenCalledTimes(1);
    expect(resultado).toEqual(woundMonitoringMock[0]);
  });

  it('deve criar um novo monitoramento de ferimento', async () => {
    mockRepository.create.mockResolvedValue(woundMonitoringMock[0]);

    const resultado = await service.create(woundMonitoringMock[0]);

    expect(mockRepository.create).toHaveBeenCalledTimes(1);
    expect(resultado).toEqual(woundMonitoringMock[0]);
  });

  it('deve atualizar um monitoramento de ferimento existente', async () => {
    mockRepository.update.mockResolvedValue(woundMonitoringMock[0]);

    const resultado = await service.update(1, woundMonitoringMock[0]);

    expect(mockRepository.update).toHaveBeenCalledTimes(1);
    expect(resultado).toEqual(woundMonitoringMock[0]);
  });

  it('deve deletar um monitoramento de ferimento existente', async () => {
    mockRepository.delete.mockResolvedValue(woundMonitoringMock[0]);

    const resultado = await service.delete(1);

    expect(mockRepository.delete).toHaveBeenCalledTimes(1);
    expect(resultado).toEqual(woundMonitoringMock[0]);
  });
});
