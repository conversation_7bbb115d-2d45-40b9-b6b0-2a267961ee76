const Joi = require("joi");

class PatientComorbidityModel {
  static get schema() {
    return Joi.object({
      comorbidity_type: Joi.string().max(100).required(),
      diagnosis_date: Joi.date().required(),
      under_treatment: Joi.boolean().required(),
      observations: Joi.string().allow('').required(),
      patient_id: Joi.number().integer().required()
    });
  }
}

module.exports = PatientComorbidityModel;