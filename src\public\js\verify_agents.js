const db = require("../../config/db");

async function verifyAgents() {
  try {
    await db.connect();
    console.log("Conectado ao banco de dados");

    // Listar todos os agentes cadastrados
    const result = await db.query(
      "SELECT id, full_name, cpf, password FROM health_agent"
    );

    console.log("Agentes cadastrados:");
    result.rows.forEach((agent) => {
      console.log(
        `ID: ${agent.id}, Nome: ${agent.full_name}, CPF: ${agent.cpf}`
      );
    });

    console.log(`Total de ${result.rows.length} agentes encontrados`);

    // Verificar se os CPFs estão com formato consistente
    const cpfFormats = {};
    result.rows.forEach((agent) => {
      const format = agent.cpf.match(/\d/g) ? agent.cpf.match(/\d/g).length : 0;
      cpfFormats[format] = (cpfFormats[format] || 0) + 1;
    });

    console.log("Formatos de CPF encontrados:");
    Object.keys(cpfFormats).forEach((format) => {
      console.log(`${format} dígitos: ${cpfFormats[format]} agentes`);
    });
  } catch (err) {
    console.error("Erro ao verificar agentes:", err);
  } finally {
    await db.end();
  }
}

verifyAgents();
