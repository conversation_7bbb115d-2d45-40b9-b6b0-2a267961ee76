const express = require('express');
const router = express.Router();

const HealthUnitRepository = require('../repositories/healthUnitRepository');
const HealthUnitService = require('../services/healthUnitService');
const HealthUnitController = require('../controllers/healthUnitController');

const controller = new HealthUnitController(new HealthUnitService(new HealthUnitRepository()));
router.get('/', controller.index.bind(controller));
router.get('/:id', controller.show.bind(controller));

module.exports = router;
